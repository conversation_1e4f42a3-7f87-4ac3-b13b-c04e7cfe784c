@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/qualified.css");
}
@* @T("公司资质信息") *@
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a href="@Url.Action("Index")">
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>
                    @if (ViewBag.Type == 0)
                    {
                        @T("公司资质信息")
                    }
                    else
                    {
                        @T("店铺资质信息")
                    }
                </p>
            </div>
            <div class="step-item">
                <div class="step-circle">3</div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">4</div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <!-- 协议内容卡片 -->
        <div class="center-card layui-card">

            <div class="layui-card tip">
                <div class="layui-card-header">@T("注意事项")</div>
                <div class="layui-card-body">
                    @T("以下所需要上传的电子版资质文件仅支持JPG\\GIF\\PNG格式图片，大小请控制在1M之内。")
                </div>
            </div>


            <form class="layui-form" method="post">
                <h3 class="big-title">
                    @(ViewBag.Type == 0 ? T("公司及联系人信息") : T("店铺及联系人信息"))
                </h3>
                <input type="hidden" name="Type" value="@ViewBag.Type" />
                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@(ViewBag.Type == 0 ? T("公司名称") : T("店铺名称"))</label>
                        <div class="layui-input-inline">
                            <input type="text" name="CompanyName"
                                placeholder="@(ViewBag.Type == 0 ? T("请输入公司名称") : T("请输入店铺名称"))" autocomplete="off"
                                class="layui-input" value="" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="layui-row ">
                    <div class="layui-col-md10 layui-col-xs10">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("所在地")</label>
                            <div class="layui-input-inline no-input">
                                <select name="RegionId" lay-search id="Region" lay-filter="Region"
                                    lay-verify="required">
                                    <option value="">@T("请选择省")</option>
                                    @foreach (var item in ViewBag.Regions)
                                    {
                                        <option value="@item.Id" data-lng="@item.Lng" data-lat="@item.Lat">@item.Name
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="layui-input-inline no-input">
                                <select name="CityId" lay-search id="City" lay-filter="City" lay-verify="required">
                                    <option value="">@T("请选择市")</option>

                                </select>
                            </div>

                            <div class="layui-input-inline no-input">
                                <select name="AreaId" lay-search id="Area" lay-filter="Area" lay-verify="required">
                                    <option value="">@T("请选择市")</option>

                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row ">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("详细地址")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="Address" placeholder="@T("请输入详细地址")" autocomplete="off"
                                    class="layui-input" value="" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                @* 地图 *@
                <div class="layui-row">
                    <div class="layui-col-md8 layui-col-xs8">
                        <div class="layui-form-item">
                            <label class="layui-form-label layui-hide-v">@T("当前位置")</label>
                            <div class="layui-input-inline mapBox">
                                <div id="allMap" style="width:100%;height:400px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                @if (ViewBag.Type == 0)
                {
                    <div class="layui-row ">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("注册资金")</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="Fund" placeholder="@T("请输入注册资金")" autocomplete="off"
                                        class="layui-input" value="" lay-verify="required">
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("联系人姓名")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="UserName" placeholder="@T("请输入姓名")" autocomplete="off"
                                    class="layui-input" value="" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("联系人电话")</label>
                            <div class="layui-input-inline">
                                <input type="tel" name="Phone" autocomplete="off" lay-affix="clear"
                                    placeholder="@T("请输入电话")" value="13800000000" class="layui-input demo-phone"
                                    lay-verify="required|phone" value="">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("电子邮箱")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="Mail" placeholder="@T("请输入邮箱")" autocomplete="off"
                                    class="layui-input" value="<EMAIL>" lay-verify="required|email">
                            </div>
                        </div>
                    </div>
                </div>
                @if (ViewBag.Type == 0)
                {
                    <h3 class="big-title">@T("营业执照信息（副本）")</h3>
                    <div class="layui-row">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("营业执照号")</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="BusinessNum" placeholder="@T("请输入营业执照号")" autocomplete="off"
                                        class="layui-input" value="91110108MAOIEBFH9B" lay-verify="required|businessnum">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row ">
                        <div class="layui-col-md10 layui-col-xs10">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("营业执照所在地")</label>
                                <div class="layui-input-inline no-input">
                                    <select name="BusinessRegionId" lay-search id="BusinessRegion"
                                        lay-filter="BusinessRegion" lay-verify="required">
                                        <option value="">@T("请选择省")</option>
                                        @foreach (var item in ViewBag.Regions)
                                        {
                                            <option value="@item.Id">@item.Name</option>
                                        }
                                    </select>
                                </div>
                                <div class="layui-input-inline no-input">
                                    <select name="BusinessCityId" lay-search id="BusinessCity" lay-filter="BusinessCity"
                                        lay-verify="required">
                                        <option value="">@T("请选择市")</option>

                                    </select>
                                </div>
                                <div class="layui-input-inline no-input">
                                    <select name="BusinessAreaId" lay-search id="BusinessArea" lay-filter="BusinessArea"
                                        lay-verify="required">
                                        <option value="">@T("请选择县/区")</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row ">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("详细地址")</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="Address" placeholder="@T("请输入详细地址")" autocomplete="off"
                                        class="layui-input" value="" lay-verify="required">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md7 layui-col-xs7">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">@T("营业执照有效期")</label>
                                    <div class="layui-input-inline layui-input-wrap no-input">
                                        <div class="layui-input-prefix">
                                            <i class="layui-icon layui-icon-date"></i>
                                        </div>
                                        <input type="text" name="dateStart" id="date" lay-verify="required|date"
                                            placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <div class="layui-input-inline layui-input-wrap no-input">
                                        <div class="layui-input-prefix">
                                            <i class="layui-icon layui-icon-date"></i>
                                        </div>
                                        <input type="text" name="dateEnd" id="date" placeholder="yyyy-MM-dd"
                                            autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <p class="hint">@T("结束日期不填，表示营业执照为长期")</p>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">@T("法定经营范围")</label>
                                <div class="layui-input-inline">
                                    <textarea id="myContent" placeholder="@T("请输入内容")" class="layui-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    @* 上传文件 *@
                    <div class="layui-row">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("营业执照电子版")</label>
                                <div class="layui-btn-container">
                                    <button type="button" class="layui-btn demo-class-accept layui-btn-primary layui-border"
                                        lay-options="{accept: 'file'}" id="uploadBtn">
                                        @T("选择文件")
                                    </button>
                                    <input type="file" id="fileInput" style="display: none;" accept="image/*" />
                                    <div id="filePreview" style="margin: 0; padding-left: 135px;"></div>
                                    <p class="hint">@T("图片大小请控制在1M之内，请确保图片清晰，文字可辨并有清晰的红色公章。")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <script>
                        document.getElementById('uploadBtn').addEventListener('click', function () {
                            document.getElementById('fileInput').click();
                        });

                        document.getElementById('fileInput').addEventListener('change', function (e) {
                            const file = e.target.files[0];
                            if (!file) return;

                            // 检查文件大小（1MB）
                            if (file.size > 1024 * 1024) {
                                alert('@T("文件大小超过1M限制！")');
                                return;
                            }

                            // 读取文件到内存并显示
                            const reader = new FileReader();
                            reader.onload = function (e) {
                                const filePreview = document.getElementById('filePreview');
                                filePreview.innerHTML = '';

                                if (file.type.startsWith('image/')) {
                                    const img = document.createElement('img');
                                    img.src = e.target.result;
                                    img.style.maxWidth = '200px';
                                    img.style.maxHeight = '200px';
                                    filePreview.appendChild(img);
                                    isUpload = true;
                                    checkAllRequired();
                                } else {
                                    const p = document.createElement('p');
                                    p.textContent = file.name;
                                    filePreview.appendChild(p);
                                }
                            };
                            reader.readAsDataURL(file);
                        });
                    </script>
                }
                else
                {
                    <h3 class="big-title">@T("证件信息")</h3>

                    <div class="layui-row">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("证件号码")</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="IdentityNum" placeholder="@T("请输入证件号码")" autocomplete="off"
                                        class="layui-input" value="" lay-verify="required|identitynum">
                                </div>
                            </div>
                        </div>
                    </div>
                    @* 上传文件 *@
                    <div class="layui-row">
                        <div class="layui-col-md6 layui-col-xs6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">@T("证件照片")</label>
                                <div class="layui-btn-container">
                                    <button type="button" class="layui-btn demo-class-accept layui-btn-primary layui-border"
                                        lay-options="{accept: 'file'}" id="uploadBtn">
                                        @T("选择文件")
                                    </button>
                                    <input type="file" id="fileInput" style="display: none;" accept="image/*" />
                                    <div id="filePreview" style="margin: 0; padding-left: 135px;"></div>
                                    <p class="hint">@T("图片大小请控制在1M之内，请确保图片清晰，文字可辨并有清晰的红色公章。")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <script>
                        document.getElementById('uploadBtn').addEventListener('click', function () {
                            document.getElementById('fileInput').click();
                        });

                        document.getElementById('fileInput').addEventListener('change', function (e) {
                            const file = e.target.files[0];
                            if (!file) return;

                            // 检查文件大小（1MB）
                            if (file.size > 1024 * 1024) {
                                alert('@T("文件大小超过1M限制！")');
                                return;
                            }

                            // 读取文件到内存并显示
                            const reader = new FileReader();
                            reader.onload = function (e) {
                                const filePreview = document.getElementById('filePreview');
                                filePreview.innerHTML = '';

                                if (file.type.startsWith('image/')) {
                                    const img = document.createElement('img');
                                    img.src = e.target.result;
                                    img.style.maxWidth = '200px';
                                    img.style.maxHeight = '200px';
                                    filePreview.appendChild(img);
                                    isUpload = true;
                                    checkAllRequired();
                                } else {
                                    const p = document.createElement('p');
                                    p.textContent = file.name;
                                    filePreview.appendChild(p);
                                }
                            };
                            reader.readAsDataURL(file);
                        });
                    </script>
                }

                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit
                            lay-filter="demo1">@T("下一步")</button>
                    </div>
                </div>
            </form>
        </div>



    </div>
</div>
<div class="bug"></div>
<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=4466b7f03b3ccc26a491b9f08d5ee6ef"></script>

<script>
    // 初始化天地图并定位
    function initTianDiTuMap() {
        var map = new T.Map('allMap');
        map.centerAndZoom(new T.LngLat(116.40769, 39.89945), 12);
        // 获取浏览器定位
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function (position) {
                var lng = position.coords.longitude;
                var lat = position.coords.latitude;
                var point = new T.LngLat(lng, lat);
                map.centerAndZoom(point, 16);
                // 添加标记
                var marker = new T.Marker(point);
                map.addOverLay(marker);
                // 创建信息窗口
                var info = new T.InfoWindow("@T("当前位置")");
                // 绑定点击事件显示信息窗口
                marker.addEventListener("click", function () {
                    map.openInfoWindow(info, point);
                });
            }, function (err) {
                // 定位失败，保持默认
                console.warn('@T("定位失败")', err);
            });
        }
    }

    function setMapCenter(lng, lat, zoom = 16) {
        if (!window.map) {
            window.map = new T.Map('allMap');
        }
        var point = new T.LngLat(lng, lat);
        window.map.centerAndZoom(point, zoom);

        // 清除旧标记
        if (window.currentMarker) {
            window.map.removeOverLay(window.currentMarker);
        }

        // 添加新标记
        window.currentMarker = new T.Marker(point);
        window.map.addOverLay(window.currentMarker);

        // 创建信息窗口
        var info = new T.InfoWindow("@T("当前位置")");
        window.currentMarker.addEventListener("click", function () {
            window.map.openInfoWindow(info, point);
        });
    }

    var isUpload = false;

    // 监听所有必填项有值 - 移到全局作用域
    function checkAllRequired() {
        // 确保 jQuery 可用
        if (typeof $ === 'undefined') {
            console.warn('jQuery not available, checkAllRequired will be called later');
            return;
        }

        var allFilled = true;
        var requiredFields = [
            'input[name="CompanyName"]',        // 公司/店铺名称
            'select[name="RegionId"]',          // 所在地-省
            'select[name="CityId"]',            // 所在地-市
            'select[name="AreaId"]',            // 所在地-区
            'input[name="Address"]',            // 详细地址
            'input[name="UserName"]',           // 联系人姓名
            'input[name="Phone"]',              // 联系人电话
            'input[name="Mail"]'                // 电子邮箱
        ];

        // 根据不同类型添加额外的必填字段
        if (@ViewBag.Type == 0) {  // 公司类型
            requiredFields = requiredFields.concat([
                'input[name="Fund"]',           // 注册资金
                'input[name="BusinessNum"]',    // 营业执照号
                'select[name="BusinessRegion"]', // 营业执照所在地-省
                'select[name="BusinessCity"]',   // 营业执照所在地-市
                'select[name="BusinessArea"]',   // 营业执照所在地-区
                'input[name="dateStart"]'       // 营业执照有效期开始日期
            ]);
        } else if (@ViewBag.Type == 1) {  // 个人类型
            requiredFields = requiredFields.concat([
                'select[name="IdentityType"]',  // 证件类型
                'input[name="IdentityNum"]'     // 证件号码
            ]);
        }

        requiredFields.forEach(function (selector) {
            var $field = $(selector);
            if ($field.length > 0) {
                var value = $field.val();
                if (!value || value.trim() === '') {
                    allFilled = false;
                }
            }
        });

        var $submitBtn = $('#submitBtn');
        console.log("=== checkAllRequired 检查结果 ===");
        console.log("allFilled", allFilled);
        console.log("isUpload", isUpload);

        // 调试：显示哪些字段为空
        if (!allFilled) {
            console.log("未填写的必填字段:");
            requiredFields.forEach(function (selector) {
                var $field = $(selector);
                if ($field.length > 0) {
                    var value = $field.val();
                    if (!value || value.trim() === '') {
                        console.log("- " + selector + " (当前值: '" + value + "')");
                    }
                }
            });
        }

        if (allFilled && isUpload) {
            $submitBtn.removeClass('layui-btn-disabled');
        } else {
            $submitBtn.addClass('layui-btn-disabled');
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        initTianDiTuMap();
        layui.use(['form', 'laydate', 'util'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            var util = layui.util;
            var upload = layui.upload;
            var $ = layui.$;
            window.$ = $; // 设置为全局变量，供外部函数使用

            // 自定义营业执照号校验
            form.verify({
                businessnum: function (value, item) {
                    // 简单示例：15位或18位数字或字母
                    if (!/^([0-9A-Za-z]{15}|[0-9A-Za-z]{18})$/.test(value)) {
                        return '@T("请输入正确的营业执照号")';
                    }
                }
            });

            // 上传文件
            @* upload.render({
          elem: '.demo-class-accept', // 绑定多个元素
          url: '', // 此处配置你自己的上传接口即可
          size: 1024, // 限制文件大小，单位 KB
          accept: 'images ', // 普通文件
          done: function (res) {
            layer.msg('@T("上传成功")');
            console.log(res);
          }
        }); *@

                // 提交事件
                // 修改表单提交处理
                form.on('submit(demo1)', function (data) {
                    var field = data.field; // 获取表单字段值
                    field.Content = $('#myContent').val();

                    // 创建FormData对象
                    var formData = new FormData();

                    // 添加普通表单字段
                    for (var key in field) {
                        formData.append(key, field[key]);
                    }

                    // 添加文件数据
                    var fileInput = document.getElementById('fileInput');
                    if (fileInput.files.length > 0) {
                        formData.append('BusinessFile', fileInput.files[0]);
                    }

                    console.log("formData", formData);

                    // 使用AJAX提交表单
                    $.ajax({
                        url: '@Url.Action("SavaCompanyQualification")', // 替换为你的后端处理地址
                        type: 'POST',
                        data: formData,
                        processData: false,  // 必须设置为false
                        contentType: false, // 必须设置为false
                        success: function (response) {
                            if (response.success) {
                                // 处理成功响应
                                layer.msg('@T("提交成功")', { icon: 1 });
                                console.log(response);
                                @* window.location.href = "@Url.Action("FinanceQualification")?id="+response.data; *@
                    } else {
                                layer.msg(response.msg || "提交失败", { icon: 2 });
                            }

                        },
                        error: function (xhr) {
                            // 处理错误
                            layer.msg('@T("提交失败")');
                            console.error(11, xhr);
                        }
                    });

                    return false; // 阻止默认表单提交
                });

            // 日期
            laydate.render({
                elem: '#date'
            });

            form.on("select(Region)", function (res) {
                $("#City").empty();
                $("#Area").empty(); // 同时清空区选择器

                // 添加默认选项
                $('#City').append('<option value="">@T("请选择市")</option>');
                $('#Area').append('<option value="">@T("请选择县/区")</option>');

                // 获取当前选中项的经纬度
                var selectedOption = $(res.elem).find("option:selected");
                var lng = selectedOption.data('lng');
                var lat = selectedOption.data('lat');

                // 如果有经纬度数据，则设置地图中心
                if (lng && lat) {
                    setMapCenter(lng, lat);
                }

                // 重新渲染表单并检查状态
                form.render("select");
                checkAllRequired(); // 立即检查表单状态

                $.getJSON('@Url.Action("GetAddressIdList")', { Id: res.value }, function (res) {
                    if (res.data.length > 0) {
                        $('#City').empty(); // 清空之前添加的默认选项
                        $('#City').append('<option value="">@T("请选择市")</option>');
                        for (var i = 0; i < res.data.length; i++) {
                            // 将 data-id 逻辑加到 City 上，假设使用 Id 作为 data-id
                            $('#City').append('<option value="' + res.data[i].Id + '" data-id="' + res.data[i].Id + '" data-lng="' + res.data[i].Lng + '" data-lat="' + res.data[i].Lat + '">' + res.data[i].Name + '</option>');
                        }
                        form.render("select");
                        checkAllRequired(); // 数据加载完成后再次检查
                    }
                });
            })

            form.on("select(City)", function (res) {
                // 获取当前选中项的经纬度和data-id
                var selectedOption = $(res.elem).find("option:selected");
                var lng = selectedOption.data('lng');
                var lat = selectedOption.data('lat');
                var dataId = selectedOption.data('id');

                // 如果有经纬度数据，则设置地图中心
                if (lng && lat) {
                    setMapCenter(lng, lat);
                }

                // 可以在这里处理 data-id 相关逻辑
                console.log('Selected City data-id:', dataId);

                // 清空区选择器并立即检查状态
                $("#Area").empty();
                $('#Area').append('<option value="">@T("请选择县/区")</option>');
                form.render("select");
                checkAllRequired(); // 立即检查表单状态

                $.getJSON('@Url.Action("GetAddressIdList")', { Id: res.value }, function (res) {
                    if (res.data.length == 0) {
                        $('#xian').hide();
                    } else {
                        $('#xian').show();
                        $('#Area').empty(); // 清空之前添加的默认选项
                        $('#Area').append('<option value="">@T("请选择县/区")</option>');
                        for (var i = 0; i < res.data.length; i++) {
                            $('#Area').append('<option value="' + res.data[i].Id + '" data-id="' + res.data[i].Id + '" data-lng="' + res.data[i].Lng + '" data-lat="' + res.data[i].Lat + '">' + res.data[i].Name + '</option>');
                        }
                        form.render("select");
                        checkAllRequired(); // 数据加载完成后再次检查
                    }
                });
            })


            form.on("select(Area)", function (res) {
                // 获取当前选中项的经纬度和data-id
                var selectedOption = $(res.elem).find("option:selected");
                var lng = selectedOption.data('lng');
                var lat = selectedOption.data('lat');
                var dataId = selectedOption.data('id');

                // 如果有经纬度数据，则设置地图中心
                if (lng && lat) {
                    setMapCenter(lng, lat);
                }

                // 区选择变化时也要检查表单状态
                checkAllRequired();
            })

            form.on("select(BusinessRegion)", function (res) {
                $("#BusinessCity").empty();
                $("#BusinessArea").empty();
                $('#BusinessArea').append('<option value="">@T("请选择县/区")</option>');


                $.getJSON('@Url.Action("GetAddressIdList")', { Id: res.value }, function (res) {
                    if (res.data.length > 0) {
                        $('#BusinessCity').append('<option value="">@T("请选择市")</option>');
                        for (var i = 0; i < res.data.length; i++) {
                            $('#BusinessCity').append('<option value="' + res.data[i].Id + '">' + res.data[i].Name + '</option>');
                        }
                        form.render("select")
                    }

                });
            })

            form.on("select(BusinessCity)", function (res) {
                $("#BusinessArea").empty();
                $.getJSON('@Url.Action("GetAddressIdList")', { Id: res.value }, function (res) {
                    if (res.data.length == 0) {
                        $('#xian').hide();
                    } else {
                        $('#xian').show();
                        $('#BusinessArea').append('<option value="">@T("请选择县/区")</option>');
                        for (var i = 0; i < res.data.length; i++) {
                            $('#BusinessArea').append('<option value="' + res.data[i].Id + '" >' + res.data[i].Name + '</option>');
                        }
                        form.render("select")
                    }
                });
            })
        });




        // 监听所有必填字段的变化
        $(document).on('input change', 'input[lay-verify*="required"], input[lay-verify*="required"]', function () {
            checkAllRequired();
        });

        //监听下拉框选择事件
        form.on('select()', function (data) {
            checkAllRequired();
        });

        // 初始调用一次检查
        checkAllRequired();

    });

</script>