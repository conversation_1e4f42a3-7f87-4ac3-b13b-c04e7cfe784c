﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using MimeKit.Tnef;
using NewLife.Data;
using Pek;
using Pek.Models;
using Pek.NCube;
using Pek.Timing;
using System.Collections.Generic;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Member.Controllers;

/// <summary>
/// 会员心愿清单控制器
/// </summary>
[MemberArea]
[MemberAuthorize]
public class WishController : PekBaseControllerX {
    public IActionResult Index(long CId,int page = 1,int limit = 5)
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.CId = CId;

        ViewBag.Page = page;
        var pages = new PageParameter()
        {
            PageIndex = page, 
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "WishlistAddTime",
            Desc = true
        };

        viewModel.GoodsClassList = GoodsClass.FindAllByLevel(0).Select(e => new GoodsClass
        {
            Id = e.Id,
            Name = GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.RealName ?? e.Name
        });

        var userId = ManageProvider.User?.ID ?? 0;
        var exp = new WhereExpression();
        exp &= Wishlist._.BuyerId == userId;
        if(CId > 0)
        {
            var GIds = Goods.FindAllByCid1(CId).Select(e=>e.Id);
            if(GIds.Count() == 0)
            {
                ViewBag.Total = 0;
                viewModel.list = new List<object>();
                return View(viewModel);
            }
            exp &= Wishlist._.GoodsId.In(GIds);
        }
        var data = Wishlist.FindAll(exp, pages).Select(e =>
        {
            var goodsImage = GoodsImages.FindDefaultBySkuId(e.SkuId, WorkingLanguage.Id);
            if (goodsImage.IsNullOrEmpty())
            {
                goodsImage = GoodsLan.FindByGIdAndLId(e.SkuId, WorkingLanguage.Id)?.LanGoodsImage ?? e.Goods.GoodsImage ?? "";
            }
            WishDto wish = new();
            wish.Id = e.Id.SafeString();
            var goodsSku = GoodsSKUDetail.FindById(e.SkuId) ?? new GoodsSKUDetail();
            wish.GoodsId = goodsSku.GoodsId.SafeString();
            wish.SkuId = goodsSku.Id.SafeString();
            wish.GoodsName = (GoodsLan.FindByGIdAndLId(goodsSku.GoodsId, WorkingLanguage.Id)?.LanName ?? goodsSku.GoodsName)+" "+goodsSku.SpecValueDetail(WorkingLanguage.Id);
            wish.GoodsImage = AlbumPic.FindByNameAndSId(goodsImage, goodsSku.Goods.StoreId)?.Cover??"";
            wish.AddDate = UnixTime.ToDateTime(e.WishlistAddTime);
            
            // 获取个体价格并应用汇率
            wish.GoodsPrice = goodsSku.GoodsPrice * WorkingCurrencies.ExchangeRate;
            wish.GoodsMarketPrice = goodsSku.GoodsMarketPrice * WorkingCurrencies.ExchangeRate;

            var curTieredPrice = new List<GoodsTieredPrice> { };

            // 获取阶梯价格数据
            var tieredPrices = GoodsTieredPrice.FindAllByGoodsIdAndSkuId(goodsSku.GoodsId, goodsSku.Id)
                .Where(tp => tp.Enabled)
                .OrderBy(tp => tp.MinQuantity)
                .ToList();
                
            // 转换货币价格
            foreach (var tier in tieredPrices)
            {
                var tierTemp = tier.CloneEntity();
                tierTemp.Price *= WorkingCurrencies.ExchangeRate;
                tierTemp.OriginalPrice *= WorkingCurrencies.ExchangeRate;
                curTieredPrice.Add(tierTemp);
            }
            
            wish.TieredPrices = curTieredPrice;
            wish.SymbolLeft = WorkingCurrencies.SymbolLeft??"$";
            wish.MaterialIds = goodsSku.Goods.MaterialIds?.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList()??[];
            return wish;
        });
        viewModel.list = data;
        ViewBag.Total = pages.TotalCount;
        //获取商品相关推荐
        ViewBag.Randomlist = GoodsSKUDetail.FindAllByRandomLan(6, WorkingLanguage.Id, WorkingCurrencies.ExchangeRate);
        return View(viewModel);
    }

    /// <summary>
    /// 批量取消心愿清单
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult BatchCancelWish(string Ids)
    {
        var result = new DResult();
        if (Ids.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("不能为空");
            return Json(result);
        }
        foreach (var item in Ids.Split(","))
        {
            var wish = Wishlist.FindById(item.ToLong());
            if (wish == null) continue;
            var goodsSku = GoodsSKUDetail.FindById(wish.SkuId);
            if (goodsSku != null)//如果商品SKU存在，减少收藏数量（考虑到商品SKU可能被删除的情况）
            {
                goodsSku.GoodsCollect -= 1;
                goodsSku.Update();
            }
            wish.Delete();
        }
        result.success = true;
        result.msg = GetResource("操作成功");
        return Json(result);
    }

  
}
