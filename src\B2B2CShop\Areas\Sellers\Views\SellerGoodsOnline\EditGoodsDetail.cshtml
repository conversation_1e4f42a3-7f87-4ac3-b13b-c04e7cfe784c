﻿@using B2B2CShop.Entity
@model Goods
@inject IWorkContext workContext
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsOnline";
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
    PekHtml.AppendCssFileParts("~/public/css/pageCss/editgoods.css");
    var goods = Model;
    var goodsClass = GoodsClass.FindById(goods.CId);
    var goodsClassLans = GoodsClassLan.FindByGIds(goodsClass?.ParentIdList??"",workContext.WorkingLanguage.Id);
    var dateNow = DateTime.Now.ToString("yyyy-MM-dd");
    var localizationSettings = LocalizationSettings.Current;
    var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
}
@await Html.PartialAsync("_Left")
<link rel="stylesheet" href="/static/home/<USER>/common.css">
<link rel="stylesheet" href="/static/home/<USER>/seller.css">
<link rel="stylesheet" href="/public/static/plugins/js/jquery-ui/jquery-ui.min.css">


<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="seller_right">
    <div class="seller_items">
        <ul class="tabsrow">
            <li class="current"><a href="">@T("商品详情")</a></li>
            <li><a href="@Url.Action("EditGoodsSku",new{goodsId = Model.Id})">@T("商品SKU")</a></li>
            <li><a href="@Url.Action("EditGoodsPicture",new{goodsId = goods.Id})">@T("商品图片")</a></li>
        </ul>
    </div>
    <div class="dssc-form-default">
        <form id="goods_form" enctype="multipart/form-data" method="post" action="@Url.Action("EditGoodsDetail")">
            <div class="tab-pane active" id="detail">
                <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                    @if (localizationSettings.IsEnable)
                    {
                        <ul class="layui-tab-title">
                            <li data="" class="layui-this">@T("标准") </li>
                            @foreach (var item in LanguageList)
                            {
                                <li data="@item.Id" class="LId">@item.DisplayName</li>
                            }
                        </ul>
                    }
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="dssc-form-goods">
                                <!-- F码商品专有项 E -->
                                <h3 id="demoinfo">@T("商品基本信息")</h3>
                                <dl>
                                    <dt><i class="required">*</i>@T("商品名称：")</dt>
                                    <dd>
                                        <input name="goodsName" type="text" class="text w400" value="@goods.Name" />
                                        <span></span>
                                        <p class="hint">@T("商品标题名称长度至少3个字符，最长200个字符")</p>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("商品卖点：")</dt>
                                    <dd>
                                        <textarea name="advWord" class="textarea h60 w400">@goods.AdvWord</textarea>
                                        <span></span>
                                        <p class="hint">@T("商品卖点最长不能超过150个字符")</p>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("商品图片：")</dt>
                                    <dd>
                                        <div class="dssc-goods-default-pic">
                                            <div class="goodspic-uplaod">
                                                <div class="upload-thumb">
                                                    <img dstype="goods_image" src="@AlbumPic.FindByName(goods.GoodsImage??"")?.Cover" />
                                                </div>
                                                <input type="hidden" name="goodsImage" id="goodsImage" dstype="goods_image" value="@goods.GoodsImage" />
                                                <span></span>
                                                <p class="hint">@T("最多可发布5张商品图片。上传商品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用")<font color="red">@T("尺寸800x800像素以上、大小不超过1M的正方形图片")</font>，@T("上传后的图片将会自动保存在图片空间的默认分类中")。</p>
                                                <div class="handle">
                                                    <div class="dssc-upload-btn">
                                                        <a href="javascript:void(0);">
                                                            <span>
                                                                <input type="file" hidefocus="true" size="1" class="input-file" name="goods_image" id="goods_image" accept="image/jpeg,image/png,image/gif">
                                                            </span>
                                                            <p><i class="iconfont">&#xe733;</i>@T("图片上传")</p>
                                                        </a>
                                                    </div>
                                                    <a class="dssc-btn" dstype="show_image" href="@Url.Action("Piclist","SellerGoodsAdd",new {id=0})">
                                                        <i class="iconfont">&#xe72a;</i>@T("从图片空间选择")
                                                    </a>
                                                    <a href="javascript:void(0);" dstype="del_goods_demo" class="dssc-btn" style="display: none;">
                                                        <i class="iconfont">&#xe67a;</i>@T("关闭相册")
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="demo"></div>
                                    </dd>
                                </dl>
                                <dl>
                                    <dt>@T("商品视频")：</dt>
                                    <dd>
                                        <div class="dssc-goods-default-pic">
                                            <div class="goodspic-uplaod">
                                                <div class="upload-thumb" style="position:relative;">
                                                    <div class="plyr__video-embed" id="player">
                                                        <iframe id="vimeoPlayer" src="" allowfullscreen allowtransparency allow="autoplay"></iframe>
                                                    </div>
                                                </div>
                                                <span></span>
                                                <p class="hint">@T("只能发布一个商品视频")</p>
                                                <div class="input-container">
                                                    <input type="text" class="videoUrl" id="videoUrl" name="videoUrl" placeholder="@T("请输入Vimeo视频链接，例如")：https://player.vimeo.com/video/1074875567?h=a1db9af59c" value="@goods.GoodsVideoName">
                                                    <a id="loadBtn" onclick="loadVideo()" class="loadBtn dssc-btn"><i class="iconfont">&#xe733;</i>@T("加载视频")</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="goods_video_list"></div>

                                    </dd>
                                </dl>
                                <h3 id="demoseo">@T("商品SEO内容")</h3>
                                <dl>
                                    <dt>@T("SEO标题：")</dt>
                                    <dd>
                                        <input name="seotitle" type="text" class="text w400" value="@goods.SeoTitle" />
                                        <span></span>
                                        <p class="hint">@T("用于商品搜索引擎的优化，SEO标题名称长度至少3个字符，最长200个字符")</p>
                                    </dd>
                                    <dt>@T("SEO关键字：")</dt>
                                    <dd>
                                        <input name="seokeys" type="text" class="text w400" value="@goods.SeoKeys" />
                                        <span></span>
                                        <p class="hint">@T("用于商品搜索引擎的优化，关键字之间请用英文逗号分隔")</p>
                                    </dd>
                                    <dt>@T("SEO描述：")</dt>
                                    <dd>
                                        <textarea name="seodescription" class="textarea h60 w400">@goods.SeoDescription</textarea>
                                        <span></span>
                                        <p class="hint">@T("用于商品搜索引擎的优化，建议255字以内")</p>
                                    </dd>
                                </dl>
                                <h3 id="demodesc">@T("商品详情描述")</h3>
                                <dl>
                                    <dt>@T("商品描述：")</dt>
                                    <dd id="dsProductDetails">
                                        <div class="tabs">
                                            <ul class="ui-tabs-nav">
                                                <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i> @T("电脑端")</a></li>
                                                <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>@T("手机端")</a></li>
                                            </ul>
                                            <div id="panel-1" class="ui-tabs-panel">
                                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.config.js"></script>
                                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/ueditor.all.min.js"></script>
                                                <script type="text/javascript" charset="utf-8" src="/static/plugins/ueditor/lang/zh-cn/zh-cn.js"></script>
                                                <script type="text/javascript">

                                                    var ue = UE.getEditor('goods_body');
                                                    if ("") {
                                                    ue.ready(function () {
                                                    this.setContent("");
                                                    })
                                                    }
                                                </script>
                                                <textarea name="goodsBody" id="goods_body" style="height:400px">@goods.GoodsCommon?.GoodsBody</textarea>
                                                <div class="hr8">

                                                    <a class="dssc-btn mt5" dstype="show_desc" href="@Url.Action("PiclistConten","SellerGoodsAdd",new { type = "Pc" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desc" class="dssc-btn mt5" style="display: none;"><i class=" iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                </div>
                                                <p id="des_demo"></p>
                                            </div>
                                            <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                                <script type="text/javascript">

                                                    var ue = UE.getEditor('goods_Mobilebody');
                                                    if ("") {
                                                    ue.ready(function () {
                                                    this.setContent("");
                                                    })
                                                    }
                                                </script>
                                                <textarea name="goods_Mobilebody" id="goods_Mobilebody">@goods.GoodsCommon?.MobileBody</textarea>
                                                <div class="hr8">
                                                    <a class="dssc-btn mt5" dstype="show_desca" href="@Url.Action("PiclistConten","SellerGoodsAdd",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                </div>
                                                <p id="des_Mobieldemo"></p>
                                            </div>
                                            <div class="dssc-upload-btn">
                                                <a href="javascript:void(0);">
                                                    <span>
                                                        <input type="file" hidefocus="true" size="1" class="input-file" name="add_album" id="add_album" multiple="multiple">
                                                    </span>
                                                    <p><i class="iconfont" data_type="0" dstype="add_album_i">&#xe733;</i>@T("图片上传")</p>
                                                </a>
                                            </div>
                                        </div>
                                    </dd>
                                </dl>
                                <h3 id="">@T("商品货期设置")</h3>
                                <dl>
                                    <dt>@T("出货：")</dt>
                                    <dd>
                                        <input name="deliveryTime" type="text" class="text w400" value="@goods.DeliveryTime" />
                                        <span></span>
                                        <p class="hint">@T("用于提示商品出货的时间")</p>
                                    </dd>
                                    <dt>@T("到货：")</dt>
                                    <dd>
                                        <input name="deliveryDate" type="text" class="text w400" value="@goods.DeliveryDate" />
                                        <span></span>
                                        <p class="hint">@T("用于提示商品到货的时间")</p>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        @if (localizationSettings.IsEnable)
                        {
                            @foreach (var item in LanguageList)
                            {
                                var goodslan = GoodsLan.FindByGIdAndLId(goods.Id, item.Id);
                                <div class="layui-tab-item">
                                    <div class="dssc-form-goods">
                                        <h3 id="[@item.Id].demoinfo">@T("商品基本信息")</h3>
                                        <dl>
                                            <dt>@T("商品名称：")</dt>
                                            <dd>
                                                <input name="[@item.Id].goodsName" type="text" class="text w400" value="@goodslan?.Name" />
                                                <span></span>
                                                <p class="hint">@T("商品标题名称长度至少3个字符，最长200个字符")</p>
                                            </dd>
                                        </dl>
                                        <dl>
                                            <dt>@T("商品卖点：")</dt>
                                            <dd>
                                                <textarea name="[@item.Id].advWord" class="textarea h60 w400">@goodslan?.AdvWord</textarea>
                                                <span></span>
                                                <p class="hint">@T("商品卖点最长不能超过150个字符")</p>
                                            </dd>
                                        </dl>
                                        <dl>
                                            <dt>@T("商品图片：")</dt>
                                            <dd>
                                                <div class="dssc-goods-default-pic">
                                                    <div class="goodspic-uplaod">
                                                        <div class="upload-thumb">
                                                            <img dstype="goods_image@(item.Id)" src="@AlbumPic.FindByName(goodslan?.GoodsImage??"")?.Cover" />
                                                        </div>
                                                        <input type="hidden" name="[@item.Id].goodsImage" id="[@item.Id].goodsImage" dstype="goods_image@(item.Id)" value="@goodslan?.GoodsImage" />
                                                        <span></span>
                                                        <p class="hint">@T("最多可发布5张商品图片。上传商品默认主图，如多规格值时将默认使用该图或分规格上传各规格主图；支持jpg、gif、png格式上传或从图片空间中选择，建议使用")<font color="red">@T("尺寸800x800像素以上、大小不超过1M的正方形图片")</font>，@T("上传后的图片将会自动保存在图片空间的默认分类中")。</p>
                                                        <div class="handle">
                                                            <div class="dssc-upload-btn">
                                                                <a href="javascript:void(0);">
                                                                    <span>
                                                                        <input type="file" hidefocus="true" size="1" class="input-file" name="goods_image@(item.Id)" id="goods_image@(item.Id)" accept="image/jpeg,image/png,image/gif">
                                                                    </span>
                                                                    <p><i class="iconfont">&#xe733;</i>@T("图片上传")</p>
                                                                </a>
                                                            </div>
                                                            <a class="dssc-btn" dstype="show_image@(item.Id)" href="@Url.Action("Piclist","SellerGoodsAdd",new {id=0})">
                                                                <i class="iconfont">&#xe72a;</i>@T("从图片空间选择")
                                                            </a>
                                                            <a href="javascript:void(0);" dstype="del_goods_demo@(item.Id)" class="dssc-btn mt5 del_goods_demo" style="display: none;">
                                                                <i class="iconfont">&#xe67a;</i>@T("关闭相册")
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="demo@(item.Id)"></div>
                                            </dd>
                                        </dl>
                                        <dl>
                                            <dt>@T("商品视频")：</dt>
                                            <dd>
                                                <div class="dssc-goods-default-pic">
                                                    <div class="goodspic-uplaod">
                                                        <div class="upload-thumb" style="position:relative;">
                                                            <div class="plyr__video-embed" id="player_@(item.Id)">
                                                                <iframe id="vimeoPlayer_@(item.Id)" src="" allowfullscreen allowtransparency allow="autoplay"></iframe>
                                                            </div>
                                                        </div>
                                                        <input type="hidden" name="[@item.Id].goodsvideo_name" id="[@item.Id].goodsvideo_name" dstype="[@item.Id].goodsvideo_name" value="" />
                                                        <span></span>
                                                        <p class="hint">@T("只能发布一个商品视频")</p>
                                                        <div class="input-container">
                                                            <input type="text" class="videoUrl" id="videoUrl_@(item.Id)" name="[@item.Id].videoUrl" placeholder="@T("请输入Vimeo视频链接，例如")：https://player.vimeo.com/video/1074875567?h=a1db9af59c" value="@goodslan?.GoodsVideoName">
                                                            <a id="loadBtn_@(item.Id)" onclick="loadVideo('@(item.Id)')" class="loadBtn dssc-btn"><i class="iconfont">&#xe733;</i>@T("加载视频")</a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="[@item.Id].goods_video_list"></div>
                                            </dd>
                                        </dl>
                                        <h3 id="[@item.Id].demoseo">@T("商品SEO内容")</h3>
                                        <dl>
                                            <dt>@T("SEO标题：")</dt>
                                            <dd>
                                                <input name="[@item.Id].seotitle" type="text" class="text w400" value="@goodslan?.SeoTitle" />
                                                <span></span>
                                                <p class="hint">@T("用于商品搜索引擎的优化，SEO标题名称长度至少3个字符，最长200个字符")</p>
                                            </dd>
                                            <dt>@T("SEO关键字：")</dt>
                                            <dd>
                                                <input name="[@item.Id].seokeys" type="text" class="text w400" value="@goodslan?.SeoKeys" />
                                                <span></span>
                                                <p class="hint">@T("用于商品搜索引擎的优化，关键字之间请用英文逗号分隔")</p>
                                            </dd>
                                            <dt>@T("SEO描述：")</dt>
                                            <dd>
                                                <textarea name="[@item.Id].seodescription" class="textarea h60 w400">@goodslan?.SeoDescription</textarea>
                                                <span></span>
                                                <p class="hint">@T("用于商品搜索引擎的优化，建议255字以内")</p>
                                            </dd>
                                        </dl>
                                        <h3 id="[@item.Id].demodesc">@T("商品详情描述")</h3>
                                        <dl>
                                            <dt>@T("商品描述")：</dt>
                                            <dd id="[@item.Id].dsProductDetails">
                                                <div class="tabs">
                                                    <ul class="ui-tabs-nav">
                                                        <li class="ui-tabs-selected"><a href="#panel-1"><i class="iconfont">&#xe60c;</i>@T("电脑端") </a></li>
                                                        <li class="selected"><a href="#panel-2"><i class="iconfont">&#xe60e;</i>@T("手机端")</a></li>
                                                    </ul>
                                                    <div id="panel-1" class="ui-tabs-panel">
                                                        <script type="text/javascript">
                                                            var ue@(item.Id) = UE.getEditor('<EMAIL>');
                                                            if ("") {
                                                            ue@(item.Id).ready(function () {
                                                            this.setContent("");
                                                            })
                                                            }

                                                        </script>
                                                        <textarea name="<EMAIL>" id="<EMAIL>" style="height:400px">@goodslan?.Content</textarea>
                                                        <div class="hr8">

                                                            <a class="dssc-btn mt5" dstype="show_desc@(item.Id)" href="@Url.Action("PiclistConten","SellerGoodsAdd",new { type = "Pc" })">
                                                                <i class="iconfont">&#xe72a;</i>@T("插入相册图片")
                                                            </a>
                                                            <a href="javascript:void(0);" dstype="del_desc@(item.Id)" class="dssc-btn mt5" style="display: none;">
                                                                <i class=" iconfont">&#xe67a;</i>@T("关闭相册")
                                                            </a>
                                                        </div>
                                                        <p id="des_demo@(item.Id)"></p>
                                                    </div>
                                                    <div id="panel-2" class="ui-tabs-panel ui-tabs-hide">
                                                        <script type="text/javascript">
                                                            var ue@(item.Id) = UE.getEditor('goods_Mobilebody_@(item.Id)');
                                                            if ("") {
                                                            ue@(item.Id).ready(function () {
                                                            this.setContent("");
                                                            })
                                                            }
                                                        </script>
                                                        <textarea name="goods_Mobilebody_@(item.Id)" id="goods_Mobilebody_@(item.Id)">@goodslan?.MobileContent</textarea>
                                                        <div class="hr8">
                                                            <a class="dssc-btn mt5" dstype="show_desca@(item.Id)" href="@Url.Action("PiclistConten","SellerGoodsAdd",new { type = "Mobile" })"><i class="iconfont">&#xe72a;</i>@T("插入相册图片")</a> <a href="javascript:void(0);" dstype="del_desca@(item.Id)" class="dssc-btn mt5" style="display: none;"><i class="iconfont">&#xe67a;</i>@T("关闭相册")</a>
                                                        </div>
                                                        <p id="des_Mobieldemo@(item.Id)"></p>
                                                    </div>
                                                    <div class="dssc-upload-btn">
                                                        <a href="javascript:void(0);">
                                                            <span>
                                                                <input type="file" hidefocus="true" size="1" class="input-file" name="[@item.Id].add_album" id="add_album@(item.Id)" multiple="multiple">
                                                            </span>
                                                            <p><i class="iconfont" data_type="0" dstype="add_album_i@(item.Id)">&#xe733;</i>@T("图片上传")</p>
                                                        </a>
                                                    </div>
                                                </div>
                                            </dd>
                                        </dl>
                                        <h3 id="">@T("商品货期设置")</h3>
                                        <dl>
                                            <dt>@T("出货：")</dt>
                                            <dd>
                                                <input name="[@item.Id].deliveryTime" type="text" class="text w400" value="@goodslan?.DeliveryTime" />
                                                <span></span>
                                                <p class="hint">@T("用于提示商品出货的时间")</p>
                                            </dd>
                                            <dt>@T("到货：")</dt>
                                            <dd>
                                                <input name="[@item.Id].deliveryDate" type="text" class="text w400" value="@goodslan?.DeliveryDate" />
                                                <span></span>
                                                <p class="hint">@T("用于提示商品到货的时间")</p>
                                            </dd>
                                        </dl>

                                    </div>
                                </div>
                            }
                        }
                    </div>
                    <div class="dssc-form-goods">
                        <h3 id="demoinfo2">@T("商品基本信息")</h3>
                        <dl>
                            <dt>@T("商品分类：")</dt>
                            <dd id="gcategory">
                                @{
                                    var classids = (goodsClass?.ParentIdList??"").Split(",").ToList();
                                    string classStr = "";
                                    foreach (var item in classids)
                                    {
                                        classStr += GoodsClass.FindById(Int64.Parse(item))?.Name + ">";
                                    }
                                    classStr = classStr.Remove(classStr.Length - 1);
                                    @classStr
                                }
                                <a class="dssc-btn" href="@Url.Action("Index")">@T("编辑")</a>
                                <input type="hidden" id="classId" name="classId" value="@goodsClass?.Id" class="text" />
                                <input type="hidden" id="goodsId" name="goodsId" value="@goods.Id" class="text" />
                                <input type="hidden" name="cate_name" value="@classStr" class="text" />
                            </dd>
                        </dl>
                        <dl>
                            <dt ds_type="no_spec"><i class="required">*</i>@T("商品价格：")</dt>
                            <dd ds_type="no_spec">
                                <input name="goodsPrice" value="@goods.GoodsPrice" type="text" class="text w60" /><em class="add-on"><i class="iconfont">&#xe65c;</i></em> <span></span>
                                <p class="hint">
                                    @T("价格必须是0.01~9999999之间的数字，且不能高于市场价。")<br>
                                    @T("此价格为商品实际销售价格，如果商品存在规格，该价格显示最低价格。")
                                </p>
                            </dd>
                        </dl>
                        <dl>
                            <dt ds_type="no_spec">@T("重量")：</dt>
                            <dd ds_type="no_spec">
                                <input name="goodsWeight" value="@goods.GoodsWeight" type="text" class="text w60" /> <span></span>
                                <p class="hint">kg</p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("市场价")：</dt>
                            <dd>
                                <input name="goodsMarketPrice" value="@goods.GoodsCommon?.GoodsMarketPrice" type="text" class="text w60" /><em class="add-on"><i class="iconfont">&#xe65c;</i></em> <span></span>
                                <p class="hint">价格必须是0.01~9999999之间的数字此价格仅为市场参考售价，请根据该实际情况认真填写。</p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("成本价")：</dt>
                            <dd>
                                <input name="goodsCostPrice" value="@goods.GoodsCommon?.GoodsCostPrice" type="text" class="text w60" /><em class="add-on"><i class="iconfont">&#xe65c;</i></em> <span></span>
                                <p class="hint">价格必须是0.00~9999999之间的数字，此价格为商户对所销售的商品实际成本价格进行备注记录，非必填选项，不会在前台销售页面中显示。</p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("折扣")：</dt>
                            <dd>
                                <input name="goodsDiscount" value="@goods.GoodsCommon?.GoodsDiscount" type="text" class="text w60" readonly="readonly" style="background:#E7E7E7 none;" /><em class="add-on">%</em>
                                <p class="hint">@T("根据销售价与市场价比例自动生成，不需要编辑。")</p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("阶梯价格")：</dt>
                            <dd>
                                <div id="tieredPriceContainer">
                                    <table class="tiered-price-table" style="width:100%; margin-bottom:10px;">
                                        <thead>
                                            <tr>
                                                <th style="width:30%">@T("最小购买数量")</th>
                                                <th style="width:30%">@T("最大购买数量")</th>
                                                <th style="width:20%">@T("原价")</th>
                                                <th style="width:20%">@T("售价")</th>
                                                <th style="width:10%"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="tieredPriceRows">
                                            @{
                                                var tieredPrices = GoodsTieredPrice.FindAllByGoodsIdAndSkuId(goods.Id,0).OrderBy(x => x.MinQuantity).ToList();
                                                for (int i = 0; i < tieredPrices.Count; i++)
                                                {
                                                    var tier = tieredPrices[i];
                                                    <tr class="tiered-price-row">
                                                        <td><input name="tieredPrice[@i].MinQuantity" type="number" min="1" class="text w60" value="@tier.MinQuantity" @(i > 0 ? "readonly" : "") /></td>
                                                        <td><input name="tieredPrice[@i].MaxQuantity" type="number" min="1" class="text w60" value="@(tier.MaxQuantity > 0 ? tier.MaxQuantity.ToString() : "")" /></td>
                                                        <td><input name="tieredPrice[@i].OriginalPrice" type="text" class="text w60" value="@tier.OriginalPrice.ToString("F2")" /></td>
                                                        <td><input name="tieredPrice[@i].Price" type="text" class="text w60" value="@tier.Price.ToString("F2")" /></td>
                                                        <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
                                                    </tr>
                                                }
                                                if (tieredPrices.Count == 0)
                                                {
                                                    <tr class="tiered-price-row">
                                                        <td><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text w60" /></td>
                                                        <td><input name="tieredPrice[0].MaxQuantity" type="number" min="1" class="text w60" /></td>
                                                        <td><input name="tieredPrice[0].OriginalPrice" type="text" class="text w60" value="@Model.GoodsPrice"/></td>
                                                        <td><input name="tieredPrice[0].Price" type="text" class="text w60" value="@Model.GoodsPrice"/></td>
                                                        <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
                                                    </tr>
                                                }
                                            }
                                        </tbody>
                                    </table>
                                    <a href="javascript:void(0);" id="addTieredPriceRow" class="dssc-btn"><i class="iconfont">&#xe733;</i>@T("添加阶梯价格")</a>
                                </div>
                                <p class="hint">@T("设置不同数量区间的商品价格，鼓励客户批量购买。")</p>
                            </dd>
                        </dl>
                        <dl virtual_type='0'>
                            <dt ds_type="no_spec"><i class="required">*</i>@T("商品物料")：</dt>
                            <dd ds_type="no_spec">
                                @* <input name="merchantMaterial" id="merchantMaterial" value="@goods.MerchantMaterial" type="text" class="text w200" /> *@
                                @{
                                    Int64 storeId = Store.FindByUId(ManageProvider.User.ID).Id;
                                    var materialList = MerchantMaterial.GetEnableList(storeId);
                                    <select name="merchantMaterial" id="merchantMaterial" class="layui-select w200" lay-search lay-filter="goodsSelect">
                                        <option value="">@T("请选择物料")</option>
                                        @foreach (var item in materialList)
                                        {
                                            <option value="@item.Id" selected="@(item.Id==goods.MerchantMaterial)">@item.Name</option>
                                        }
                                    </select>
                                }
                                <span class="failtip"></span>
                                <p class="hint"><br /></p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("库存预警值")：</dt>
                            <dd>
                                <input name="goodsStorageAlarm" value="@goods.GoodsStorageAlarm" type="text" class="text w60" />
                                <span></span>
                                <p class="hint">
                                    @T("设置最低库存预警值。当库存低于预警值时商家中心商品列表页库存列红字提醒。")<br>
                                    @T("请填写0~255的数字，0为不预警。")
                                </p>
                            </dd>
                        </dl>
                        <dl>
                            <dt ds_type="no_spec">@T("商家货号")：</dt>
                            <dd ds_type="no_spec">
                                <p>
                                    <input name="goodsSerial" value="@goods.GoodsSerial" type="text" class="text" />
                                </p>
                                <p class="hint">@T("商家货号是指商家管理商品的编号，买家不可见")<br />@T("最多可输入20个字符，支持输入中文、字母、数字、_、/、-和小数点")</p>
                            </dd>
                        </dl>
                        <dl style="overflow: visible;">
                            <dt>@T("商品品牌")：</dt>
                            <dd>
                                <div class="dssc-brand-select">
                                    <div class="selection">
                                        <input name="b_name" id="b_name" value="" type="text" class="text w180" readonly="readonly" />
                                        <input type="hidden" name="brandId" id="b_id" value="@goods.BrandId" />
                                        <em class="add-on" dstype="add-on"><i class="iconfont">&#xe73a;</i></em>
                                    </div>
                                    <div class="dssc-brand-select-container">
                                        <div class="brand-index" data-tid="0" data-url="/index.php/home/<USER>/ajax_get_brand.html">
                                            <div class="letter" dstype="letter">
                                                <ul>
                                                    <li><a href="javascript:void(0);" data-letter="all">@T("全部")</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="A">A</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="B">B</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="C">C</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="D">D</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="E">E</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="F">F</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="G">G</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="H">H</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="I">I</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="J">J</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="K">K</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="L">L</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="M">M</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="N">N</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="O">O</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="P">P</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="Q">Q</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="R">R</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="S">S</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="T">T</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="U">U</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="V">V</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="W">W</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="X">X</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="Y">Y</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="Z">Z</a></li>
                                                    <li><a href="javascript:void(0);" data-letter="0-9">@T("其他")</a></li>
                                                    <li><a href="javascript:void(0);" data-empty="0">@T("清空")</a></li>
                                                </ul>
                                            </div>
                                            <div class="search" dstype="search">
                                                <input name="search_brand_keyword" id="search_brand_keyword" type="text" class="text" placeholder="@T("品牌名称关键字查找")" /><a href="javascript:void(0);" class="dssc-btn-mini" style="vertical-align: top;">Go</a>
                                            </div>
                                        </div>
                                        <div class="brand-list" dstype="brandList">
                                            <ul dstype="brand_list">
                                            </ul>
                                        </div>
                                        <div class="no-result" dstype="noBrandList" style="display: none;">@T("没有符合")"<strong>@T("搜索关键字")</strong>"@T("条件的品牌")</div>
                                    </div>
                                </div>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("关联版式")：</dt>
                            <dd>
                                <span class="mr50">
                                    <label>@T("顶部版式")</label>
                                    <select name="plateidTop">
                                        <option>-@T("请选择")-</option>
                                    </select>
                                </span> <span class="mr50">
                                    <label>@T("底部版式")</label>
                                    <select name="plateidbottom">
                                        <option>-@T("请选择")-</option>
                                    </select>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            @{
                                var goodsClassAttributes = ClassAttributes.FindAllByClassIdLan(goodsClass?.Id??0, workContext.WorkingLanguage.Id);
                                var goodsAttribute = GoodsExAttributes.FindByGId(goods.Id);
                                if (goodsClassAttributes.Count > 0)
                                {
                                    <dt>@T("商品分类属性")：</dt>
                                    <dd>
                                        <div class="attribute-wrapper">
                                            @for (int i = 0; i < goodsClassAttributes.Count; i++)
                                            {
                                                ClassAttributes classAttribute = goodsClassAttributes[i];
                                                var attributeStr = goodsAttribute != null ? goodsAttribute[classAttribute.MappingField??""] : "";
                                                <div class="attribute-item">
                                                    <label>@classAttribute.Name：</label>
                                                    <input name="<EMAIL>" value="@attributeStr" type="text" class="text w100" />
                                                </div>
                                                @if ((i + 1) % 4 == 0 && i != goodsClassAttributes.Count - 1)
                                                {
                                                    <div class="clear"></div>
                                                }
                                            }
                                        </div>
                                    </dd>

                                }
                            }
                        </dl>
                        <h3 id="demospecial">@T("特殊商品")</h3>
                        <!-- 只有可发布虚拟商品才会显示 S -->
                        <!-- 只有可发布虚拟商品才会显示 E -->
                        <!-- F码商品专有项 S -->
                        <dl class="special-02" dstype="virtual_null">
                            <dt>@T("F码商品")：</dt>
                            <dd>
                                <ul class="dssc-form-radio-list">
                                    <li>
                                        <input type="radio" name="isGoodsFCode" id="is_goodsfcode_1" value="1" checked="@(goods.IsGoodsFCode==1)">
                                        <label for="is_goodsfcode_1">@T("是")</label>
                                    </li>
                                    <li>
                                        <input type="radio" name="isGoodsFCode" id="is_goodsfcode_0" value="0" checked="@(goods.IsGoodsFCode==0)">
                                        <label for="is_goodsfcode_0">@T("否")</label>
                                    </li>
                                </ul>
                                <p class="hint vital">@T("*F码商品不能参加抢购、秒杀和组合销售三种促销活动。也不能预售和推荐搭配。")</p>
                            </dd>
                        </dl>
                        <dl class="special-02" dstype="fcode_valid" style="display:none;">
                            <dt>
                                <i class="required">*</i>
                                @T("生成F码数量")：
                            </dt>
                            <dd>
                                <input type="text" name="g_fccount" id="g_fccount" class="w80 text" value="">
                                <span></span>
                                <p class="hint">@T("请填写100以内的数字。编辑商品时添加F码会不计算原有F码数量继续生成相应数量。")</p>
                            </dd>
                        </dl>
                        <dl class="special-02" dstype="fcode_valid" style="display:none;">
                            <dt>
                                <i class="required">*</i>
                                @T("F码前缀")：
                            </dt>
                            <dd>
                                <input type="text" name="g_fcprefix" id="g_fcprefix" class="w80 text" value="">
                                <span></span>
                                <p class="hint">@T("请填写3~5位的英文字母。建议每次生成的F码使用不同的前缀。")</p>
                            </dd>
                        </dl>
                        <!-- 商品物流信息 S -->
                        <h3 id="demowl">@T("商品物流信息")</h3>
                        <dl>
                            <dt>@T("所在地")：</dt>
                            <dd>
                                <p>
                                    <select name="country" id="country" class="select">
                                        <option value="0">-@T("请选择")-</option>

                                        <option value="0">@T("请选择")</option>
                                    </select>
                                    <select name="province" id="province" class="select">
                                        <option value="0">@T("请选择")</option>
                                    </select>
                                    <select name="city" id="city" class="select">
                                        <option value="0">@T("请选择")</option>
                                    </select>
                                </p>
                            </dd>
                        </dl>
                        <dl dstype="virtual_null">
                            <dt>@T("运费")：</dt>
                            <dd>
                                <ul class="dssc-form-radio-list">
                                    <li>
                                        <input id="freight_0" dstype="freight" name="freight" class="radio" type="radio" checked="checked" value="0">
                                        <label for="freight_0">@T("固定运费")</label>
                                        <div dstype="div_freight">
                                            <input id="goodsFreight" class="w50 text" ds_type='transport' type="text" value="@goods.GoodsFreight.ToString("F2")" name="g_freight"><em class="add-on"><i class="iconfont">&#xe65c;</i></em>
                                        </div>
                                    </li>
                                    <li>
                                        <input id="freight_1" dstype="freight" name="freight" class="radio" type="radio" value="1">
                                        <label for="freight_1">@T("使用售卖区域")</label>
                                        <div dstype="div_freight" style="display: none;">
                                            <input id="transport_id" type="hidden" value="" name="transport_id">
                                            <input id="transport_title" type="hidden" value="" name="transport_title">
                                            <span id="postageName" class="transport-name"></span>
                                            <a href="JavaScript:void(0);" onclick="window.open('/index.php/home/<USER>/index.html?type=select')" class="dsbtn" id="postageButton"><i class="iconfont">&#xe6f1;</i>@T("选择售卖区域")</a>
                                        </div>
                                    </li>
                                </ul>
                                <p class="hint"> @T("运费设置为 0 元，前台商品将显示为免运费。")</p>
                            </dd>
                        </dl>
                        <!-- 商品物流信息 E -->
                        <h3 id="demovat" dstype="virtual_null">@T("发票信息")</h3>
                        <dl dstype="virtual_null">
                            <dt>@T("是否开增值税发票")：</dt>
                            <dd>
                                <ul class="dssc-form-radio-list">
                                    <li>
                                        <label>
                                            <input name="goodsVat" value="@(goods.GoodsVat==1)" type="radio" />
                                            @T("是")
                                        </label>
                                    </li>
                                    <li>
                                        <label>
                                            <input name="goodsVat" value="0" checked="@(goods.GoodsVat==0)" type="radio" />
                                            @T("否")
                                        </label>
                                    </li>
                                </ul>
                                <p class="hint"></p>
                            </dd>
                        </dl>
                        <h3 id="demoother">@T("其他信息")</h3>
                        <dl>
                            <dt>@T("本店分类")：</dt>
                            <dd>
                                <span class="new_add"><a href="javascript:void(0)" id="add_sgcategory" class="dssc-btn">@T("新增分类")</a> </span>
                                <select name="goodsStcids[]" class="sgcategory">
                                    <option value="0">-@T("请选择")-</option>
                                </select>
                                <p class="hint">@T("商品可以从属于店铺的多个分类之下，店铺分类可以由 &quot;商家中心 -&gt; 店铺 -&gt; 店铺分类&quot; 中自定义")</p>
                            </dd>
                        </dl>
                        <dl>
                            <dt>@T("商品推荐")：</dt>
                            <dd>
                                <ul class="dssc-form-radio-list">
                                    <li>
                                        <label>
                                            <input name="goodsCommend" value="1" checked="checked" type="radio" />
                                            @T("是")
                                        </label>
                                    </li>
                                    <li>
                                        <label>
                                            <input name="goodsCommend" value="0" type="radio" />
                                            @T("否")
                                        </label>
                                    </li>
                                </ul>
                                <p class="hint">@T("被推荐的商品会显示在店铺首页")</p>
                            </dd>
                        </dl>
                    </div>
                    <input type="hidden" name="commonid" value="" />
                    <input type="hidden" name="type_id" value="0" />
                </div>
            </div>
            <div class="bottom tc hr32">
                <input type="submit" class="submit" value="@T("保存")" />
            </div>
        </form>
    </div>
</div>

<script>
    var BASESITEROOT = "";
    var HOMESITEROOT = "/static/home";
    var BASESITEURL = "http://b2b2c.h.com/index.php";
    var HOMESITEURL = "http://b2b2c.h.com/index.php/home";
</script>
<script src="/public/static/plugins/jquery-2.1.4.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery-ui.min.js"></script>
<script src="/public/static/plugins/js/jquery-ui/jquery.ui.datepicker-zh-CN.js"></script>
<script src="/static/plugins/common.js"></script>
<script src="/static/plugins/jquery.validate.min.js"></script>
<script src="/static/plugins/additional-methods.min.js"></script>
<script src="/static/plugins/layer/layer.js"></script>
<script src="/static/home/<USER>/member.js"></script>
<script src="/static/plugins/js/dialog/dialog.js" id="dialog_js" charset="utf-8"></script>
<script>   
    jQuery.browser={};(function(){jQuery.browser.msie=false; jQuery.browser.version=0;if(navigator.userAgent.match(/MSIE ([0-9]+)./)){ jQuery.browser.msie=true;jQuery.browser.version=RegExp.$1;}})();
</script>
<script src="/static/plugins/jquery.ajaxContent.pack.js"></script>
<script src="/static/plugins/mlselection.js"></script>
<script type="text/javascript" src="/static/plugins/js/fileupload/jquery.iframe-transport.js" charset="utf-8"></script>
<script type="text/javascript" src="/static/plugins/js/fileupload/jquery.ui.widget.js" charset="utf-8"></script>
<script type="text/javascript" src="/static/plugins/js/fileupload/jquery.fileupload.js" charset="utf-8"></script>
<script src="/static/plugins/jquery.mousewheel.js"></script>
<script type="text/javascript" src="/static/plugins/jquery.charCount.js"></script>
<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet" />
<script src="~/static/plugins/js/layui/layui.js"></script>
<script src="/static/home/<USER>/sellergoods_add_step2.js"></script>
<script src="/static/plugins/mlselection.js"></script>
<script type="text/javascript" asp-location="Footer">
    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
    var $ = layui.jquery,
    form = layui.form,
    layer = layui.layer,
    upload = layui.upload,
    layer = layui.layer,
    element = layui.element;
    })

</script>
<script type="text/javascript">
    var DEFAULT_GOODS_IMAGE = "http://b2b2c.h.com/uploads/home/<USER>/default_goods_image.jpg";
    // 按规格存储规格值数据
    var spec_group_checked = new Array();
    var str = '';
    var V = new Array();
    var lid = "";
    $(function(){
    if($('input[name="vc_card"]').val()){
    var cardStr=$('input[name="vc_card"]').val()
    var snsArr = cardStr.split(/[(\r\n)\r\n]+/);
    $('#card_num').text(snsArr.length)
    }
    if($('input[name="vc_file"]').val()){
    var html=''
    html+='<div class="upload-file">'
    html+='<span class="upload-file-name">'
    html+=$('input[name="vc_file"]').val()
    html+='</span>'
    html+='<i class="upload-file-icon iconfont" onclick="delResource(this,\''+$('input[name="vc_file"]').val()+'\')">'
    html+='&#xe696;'
    html+='</i>'
    html+='</div>'
    $('input[name="vc_file"]').parents('.upload-wrapper').find('.dssc-upload-btn').hide()
    $('input[name="vc_file"]').parents('.upload-wrapper').find('.upload-file-wrapper').html(html)
    }
    $("#region").ds_region({show_deep:2,tip_type:1});
    //电脑端手机端tab切换
    $(".tabs").tabs();
    $.validator.addMethod('checkPrice', function(value,element){
    _goodsPrice = parseFloat($('input[name="goodsPrice"]').val());
    _goodsMarketPrice = parseFloat($('input[name="goodsMarketPrice"]').val());
    if (_goodsPrice > _goodsMarketPrice) {
    return false;
    }else {
    return true;
    }
    }, '<i class="iconfont">&#xe64c;</i>商品价格不能高于市场价格');
    jQuery.validator.addMethod("checkFCodePrefix", function(value, element) {       
    return this.optional(element) || /^[a-zA-Z]+$/.test(value);       
    },'<i class="iconfont">&#xe64c;</i>请填写不多于5位的英文字母');  
    $('#goods_form').validate({
    errorPlacement: function(error, element){
         // 找到最近的class为failtip的span元素
            var failTip = $(element).closest('dl').find('span.failtip');
            
            // 如果找不到failtip，则使用默认位置
            if(!failTip.length) {
                failTip = $(element).nextAll('span').first();
            }
            
            // 清空原有错误信息并添加新的
            failTip.empty().append(error);
    },
    //方便测试一下可进行删除
    rules : {
    goodsName : {
    required    : true,
    minlength   : 3,
    maxlength   : 200
    },
    advWord : {
    maxlength   : 150
    },
    merchantMaterial  : {
    required    : true,
    },
    goodsImage : {
    required    : false
    },
    VirtualIndate : {
    required    : function() {if ($("#is_gv_1").prop("checked")) {return true;} else {return false;}}
    },
    virtualLimit : {
    required	: function() {if ($("#is_gv_1").prop("checked")) {return true;} else {return false;}},
    range		: [1,10]
    },
    g_fccount : {
    required	: function() {if ($("#is_goodsfcode_1").prop("checked")) {return true;} else {return false;}},				range		: [1,100]
    },
    g_fcprefix : {
    required	: function() {if ($("#is_goodsfcode_1").prop("checked")) {return true;} else {return false;}},				checkFCodePrefix : true,
    rangelength	: [3,5]
    },
    appointSatedate : {
    required	: function () {if ($('#is_appoint_1').prop("checked")) {return true;} else {return false;}}
    }
    },
    messages : {
    goodsName  : {
    required    : '<i class="iconfont">&#xe64c;</i>@T("商品名称不能为空")',
    minlength   : '<i class="iconfont">&#xe64c;</i>@T("商品标题名称长度至少3个字符，最长200个字符")',
    maxlength   : '<i class="iconfont">&#xe64c;</i>@T("商品标题名称长度至少3个字符，最长200个字符")'
    },
    advWord : {
    maxlength   : '<i class="iconfont">&#xe64c;</i>@T("商品卖点不能超过150个字符")'
    },
    merchantMaterial : {
    required    : '<i class="iconfont">&#xe64c;</i>@T("商品物料不能为空")',
    },
    goodsImage : {
    required    : '<i class="iconfont">&#xe64c;</i>@T("请设置商品主图")'
    },
    VirtualIndate : {
    required    : '<i class="iconfont">&#xe64c;</i>@T("请选择有效期")'
    },
    virtualLimit : {
    required	: '<i class="iconfont">&#xe64c;</i>@T("请填写1~10之间的数字")',
    range		: '<i class="iconfont">&#xe64c;</i>@T("请填写1~10之间的数字")'
    },
    g_fccount : {
    required	: '<i class="iconfont">&#xe64c;</i>@T("请填写1~100之间的数字")',
    range		: '<i class="iconfont">&#xe64c;</i>@T("请填写1~100之间的数字")'
    },
    g_fcprefix : {
    required	: '<i class="iconfont">&#xe64c;</i>@T("请填写3~5位的英文字母")',
    rangelength	: '<i class="iconfont">&#xe64c;</i>@T("请填写3~5位的英文字母")'
    },
    appointSatedate : {
    required	: '<i class="iconfont">&#xe64c;</i>@T("请选择有效期")'
    }
    }
    });
    $('dl[dstype="spec_group_dl"]').on('click', 'span[dstype="input_checkbox"] > input[type="checkbox"]',function(){
    into_array();
    goods_stock_set();
    });
    // 提交后不没有填写的价格或库存的库存配置设为默认价格和0
    // 库存配置隐藏式 里面的input加上disable属性
    $('input[type="submit"]').click(function(){
    $('input[data_type="price"]').each(function(){
    if($(this).val() == ''){
    $(this).val($('input[name="goodsPrice"]').val());
    }
    });
    $('input[data_type="goods_weight"]').each(function(){
    if($(this).val() == ''){
    $(this).val($('input[name="goods_weight"]').val());
    }
    });
    $('input[data_type="stock"]').each(function(){
    if($(this).val() == ''){
    $(this).val('0');
    }
    });
    $('input[data_type="alarm"]').each(function(){
    if($(this).val() == ''){
    $(this).val('0');
    }
    });
    if($('dl[ds_type="spec_dl"]').css('display') == 'none'){
    $('dl[ds_type="spec_dl"]').find('input').prop('disabled','disabled');
    }
    });
    $('#country').change(function(){
    var country_id = $(this).val();
    if(country_id != '0'){
    $.ajax({
    url:'GetProvince',
    type:'Get',
    data:{cId:country_id},
    dataType:'json',    
    success:function(result){
    var provinceSelect = $('#province');
    provinceSelect.empty();
    $('#city').empty();
    $('#county').empty();
    provinceSelect.append('<option value="0">-请选择-</option>');
    if(result && result.data.length > 0) {
    $.each(result.data, function(index, item) {
    provinceSelect.append(
    $('<option></option>').val(item.AreaCode).text(item.Name)
    );
    });
    }
    },
    error: function(xhr, status, error) {
    console.error('@T("获取省份数据失败"):', error);
    },
    complete: function() {
    // 触发 province 的 change 事件
    $('#province').trigger('change');
    }
    })
    }
    });
    });
</script> 
<script src="/static/plugins/jquery.cookie.js"></script>
<script src="/static/home/<USER>/compare.js"></script>
<link rel="stylesheet" href="/static/plugins/perfect-scrollbar.min.css">
<script src="/static/plugins/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/plugins/js/qtip/jquery.qtip.min.js"></script>
<link href="/static/plugins/js/qtip/jquery.qtip.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/plugins/jquery.lazyload.min.js"></script>

<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>

<script asp-location="Footer">
    var Selleralbum = "@Url.Action("PiclistConten", new { type = "Mobile" })"
    var ADMINSITEROOT = "/static/admin";
    //var WWWROOT = "/images";
    var Querytheson = "@Url.Action("Querytheson", "ProductCategory")";
    var createData = "@Url.Action("CreateGoods")";
    var CreateImg = "@Url.Action("UploadImg")";
    var CreateImgs = "@Url.Action("UploadImg")";
    var DEFAULT_GOODS_IMAGE = "/uploads\common\default_goods_image.jpg";


    layui.use(['element', 'layer', 'jquery', 'upload', 'form'], function () {
    var $ = layui.jquery,
    form = layui.form,
    layer = layui.layer,
    upload = layui.upload,
    layer = layui.layer,
    element = layui.element;
    })

    var lid = "";
    var demo = "";
    var show_image = "";
    var del_goods_demo = "";
    var goods_image = "";
    var show_desc = "";
    var des_demo = "";
    var del_desc = "";
    var del_descs = "";
    var des_Mobieldemo = "#des_Mobieldemo";
    var show_desca = "";
    var del_desca = "";
    var show_descs = "";
    var des_demos = "";

    $(function () {

    $(".layui-tab-title").on("click", "li", function () {
    lid = $(this).attr("data");
    demo = "#demo" + lid;
    show_image = "show_image" + lid;
    del_goods_demo = "del_goods_demo" + lid;
    goods_image = "goods_image" + lid;
    show_desc = "show_desc" + lid;
    des_demo = "#des_demo" + lid;
    del_desc = "del_desc" + lid;
    add_album = "add_album" + lid;
    add_album_i = "add_album_i" + lid;
    del_descs = "del_descs" + lid;
    show_descs = "show_descs" + lid;
    des_Mobieldemo = "#des_Mobieldemo" + lid;
    show_desca = "show_desca" + lid;
    del_desca = "del_desca" + lid;
    des_demos = "#des_demos" + lid;
    // 关闭所有打开的相册

    $('a[dstype^="del_goods_demo"]').each(function() {
    $(this).click();
    });


    //主图打开图片空间
    $('a[dstype="' + show_image + '"]').unbind().ajaxContent({
    event: 'click', //mouseover
    loaderType: "img",
    loadingMsg: "/images/loading.gif",
    //target: '#demo'
    target: "#demo" + lid
    }).click(function () {
    $('a[dstype="' + del_goods_demo + '"]').show();
    $(this).hide();
    });
    $('a[dstype="' + del_goods_demo + '"]').unbind().click(function () {
    $('' + demo + '').html('');
    $('a[dstype="' + show_image + '"]').show();
    $(this).hide();

    });


    @* //参数规格使用 *@
    @* //商品规格参数使用 *@
    @* $('a[dstype="' + show_descs + '"]').unbind().ajaxContent({ *@
    @*     event: 'click', //mouseover *@
    @*     loaderType: "img", *@
    @*     loadingMsg: "/images/loading.gif", *@
    @*     target: des_demos *@
    @* }).click(function () { *@
    @*     $(this).hide(); *@
    @*     $('a[dstype="' + del_descs + '"]').show(); *@
    @* }); *@

    @* $('a[dstype="' + del_descs + '"]').click(function () { *@
    @*     $('' + des_demos+ '').html(''); *@
    @*     $(this).hide(); *@
    @*     $('a[dstype="' + show_descs + '"]').show(); *@
    @* }); *@


    //参数手机端使用
    //商品手机端内容使用
    $('a[dstype="' + show_desca + '"]').unbind().ajaxContent({
    event: 'click', //mouseover
    loaderType: "img",
    loadingMsg: "/images/loading.gif",
    target: des_Mobieldemo
    }).click(function () {
    $(this).hide();
    $('a[dstype="' + del_desca + '"]').show();
    });

    $('a[dstype="' + del_desca + '"]').click(function () {
    $('' + des_Mobieldemo + '').html('');
    $(this).hide();
    $('a[dstype="' + des_Mobieldemo + '"]').show();
    $('a[dstype="' + show_desca + '"]').show();
    });

    /* 插入商品描述 */
    // 商品描述使用
    $('a[dstype="' + show_desc + '"]').unbind().ajaxContent({
    event: 'click', //mouseover
    loaderType: "img",
    loadingMsg: "/images/loading.gif",
    target: des_demo
    }).click(function () {
    $(this).hide();
    $('a[dstype="' + del_desc + '"]').show();
    });

    $('a[dstype="' + del_desc + '"]').click(function () {
    $('' + des_demo + '').html('');
    $(this).hide();
    $('a[dstype="' + show_desc + '"]').show();
    });

    $('#' + add_album + '').fileupload({
    dataType: 'json',
    //url: HOMESITEURL+'/Sellergoodsadd/image_upload.html',
    url: CreateImgs,
    formData: { name: '' + add_album + '' },
    add: function (e, data) {
    $('i[dstype="' + add_album_i + '"]').html("&#xe717;").addClass('rotate').attr('data_type', parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type')) + 1);
    data.submit();
    },
    done: function (e, data) {
    var _counter = parseInt($('i[dstype="' + add_album_i + '"]').attr('data_type'));
    _counter -= 1;
    if (_counter == 0) {
    $('i[dstype="' + add_album_i + '"]').removeClass('rotate').html("&#xe733;");
    $('a[dstype="' + show_desc + '"]').click();
    }
    $('i[dstype="' + add_album_i + '"]').attr('' + show_desc + '', _counter);
    }
    });

    //上传商品主图
    $('#' + goods_image + '').fileupload({
    dataType: 'json',
    url: CreateImgs,
    formData: { name: $("#ModelId").val() },
    add: function (e, data) {
    $('img[dstype="' + goods_image + '"]').attr('src', '/images/loading.gif');
    data.submit();
    },
    done: function (e, data) {
    var param = data.result;
    if (!param.success) {
    alert(param.meg);
    $('img[dstype="' + goods_image + '"]').attr('src', DEFAULT_GOODS_IMAGE);
    } else {
    $('input[dstype="' + goods_image + '"]').val(param.file_name);
    $('img[dstype="' + goods_image + '"]').attr('src', param.file_path);
    }
    }
    });



    })
    $(".layui-tab-title li.layui-this").trigger("click");
    })

    insert_mobile_img = function (file_path) {
    if (!lid) {
    var ue = UE.getEditor('goods_Mobilebody');
    ue.execCommand('insertimage', { src: file_path });
    }
    else {
    var ue = UE.getEditor('goods_Mobilebody_' + lid);
    ue.execCommand('insertimage', { src: file_path });
    }
    }



    function insert_img(name, src) {
    if (!goods_image)
    {
    goods_image = "goods_image";
    }
    $('input[dstype="' + goods_image + '"]').val(name);
    $('img[dstype="' + goods_image + '"]').attr('src', src);
    //$("#imageType").val("1");//验证是从从相册图片选取的
    }

    function insert_editor(file_path) {
    if (!lid) {
    var ue = UE.getEditor('goods_body');
    ue.execCommand('insertimage', { src: file_path });
    }
    else {
    var ue = UE.getEditor('goods_body_' + lid);
    ue.execCommand('insertimage', { src: file_path });
    }

    }
    function insert_editor1(file_path) {
    if (!lid) {
    var ue = UE.getEditor('spec');
    ue.execCommand('insertimage', { src: file_path });
    } else {
    var ue = UE.getEditor('spec_' + lid);
    ue.execCommand('insertimage', { src: file_path });
    }
    }


    $(function () {
    $("#region").ds_region({ show_deep: 2, tip_type: 1 });
    //电脑端手机端tab切换
    $(".tabs").tabs();
    })
</script>
<script type="text/javascript">
    $(function() {
        // 初始化行计数器
        var rowCount = @(Math.Max(GoodsTieredPrice.FindAllByGoodsIdAndSkuId(goods.Id,0).Count, 1));
        // 默认的最小购买数量
        var DEFAULT_MIN_QUANTITY = 2;

        // 页面加载时检查第一行的最小购买数量，如果没有设置则设为默认值
        $(document).ready(function() {
            if ($("#tieredPriceRows tr").length > 0) {
                var firstRowMinInput = $("#tieredPriceRows tr:first").find("input[name$='.MinQuantity']");
                if (!firstRowMinInput.val()) {
                    firstRowMinInput.val(DEFAULT_MIN_QUANTITY);
                }
                // 获取当前商品价格，设置到所有阶梯价格的原价字段，并设为只读
                var goodsPrice = $('input[name="goodsPrice"]').val();
                if(goodsPrice) {
                    $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
                }
            } else {
                // 如果没有行，添加一个默认行
                resetTieredPriceTable();
            }

            $('#merchantMaterial').select2({
                placeholder: "@T("请选择商品")",
                allowClear: true
            });
        });

        // 商品价格自动同步到阶梯价格的原价字段
        $('input[name="goodsPrice"]').on('input change', function() {
            var goodsPrice = $(this).val();
            $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
            $('input[name$="tieredPrice[0].Price"]').val(goodsPrice);
            $('input[name$="goodsMarketPrice"]').val(goodsPrice);
        });

        // 重置阶梯价格表格，清空所有行，重新添加首行
        function resetTieredPriceTable() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="goodsPrice"]').val() || '';

            // 清空表格
            $("#tieredPriceRows").empty();
            rowCount = 0;

            // 添加第一行，最小购买数量默认为2
            var firstRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text w60" value="${DEFAULT_MIN_QUANTITY}" /></td>
                <td><input name="tieredPrice[0].MaxQuantity" type="number" min="1" class="text w60" /></td>
                <td><input name="tieredPrice[0].OriginalPrice" type="text" class="text w60" value="${goodsPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[0].Price" type="text" class="text w60" /></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(firstRow);
            rowCount = 1;

            // 启用新行的验证
            setupTieredPriceValidation();
        }

        // 添加重置按钮到阶梯价格区域
        $("#tieredPriceContainer").append('<a href="javascript:void(0);" id="resetTieredPrice" class="dssc-btn" style="margin-left:10px;"><i class="iconfont">&#xe67a;</i>@T("重置阶梯价格")</a>');

        // 为重置按钮绑定点击事件
        $("#resetTieredPrice").click(function() {
            // 询问用户是否确定重置
            layer.confirm('@T("确定要重置所有阶梯价格规则吗？")', {
                btn: ['@T("确定")','@T("取消")']
            }, function(){
                resetTieredPriceTable();
                layer.msg('@T("阶梯价格已重置")');
            });
        });

        // 添加新分层价格行
        $("#addTieredPriceRow").click(function() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="goodsPrice"]').val() || '';

            // 如果表格中没有行，先添加一行
            if ($("#tieredPriceRows tr").length === 0) {
                resetTieredPriceTable();
                return;
            }

            // 查找最后一行的最大数量
            var lastMaxQuantity = 0;
            if ($("#tieredPriceRows tr").length > 0) {
                var lastRow = $("#tieredPriceRows tr:last");
                var lastMaxInput = lastRow.find("input[name$='.MaxQuantity']");
                lastMaxQuantity = parseInt(lastMaxInput.val()) || 0;

                // 如果最后最大数量为0（无限），则提醒用户
                if (lastMaxQuantity === 0) {
                    layer.msg("@T("上一个价格区间的最大数量为不限，不能再添加价格区间")");
                    return;
                }
            }

            rowCount = $("#tieredPriceRows tr").length;

             var newRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[${rowCount}].MinQuantity" type="number" min="1" class="text w60" value="${lastMaxQuantity > 0 ? (lastMaxQuantity + 1) : DEFAULT_MIN_QUANTITY}" ${lastMaxQuantity > 0 ? 'readonly' : ''} /></td>
                <td><input name="tieredPrice[${rowCount}].MaxQuantity" type="number" min="1" class="text w60" /></td>
                <td><input name="tieredPrice[${rowCount}].OriginalPrice" type="text" class="text w60" value="${goodsPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[${rowCount}].Price" type="text" class="text w60" /></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(newRow);

            // 启用新行的验证
            setupTieredPriceValidation();
        });

        // 删除阶梯价格行
        $(document).on("click", ".remove-row", function() {
            var currentRow = $(this).closest("tr");
            var nextRow = currentRow.next("tr.tiered-price-row");
            var prevRow = currentRow.prev("tr.tiered-price-row");

            // 如果这是最后一行，则不允许删除，而是重置
            if ($("#tieredPriceRows tr").length === 1) {
                var firstRowMinInput = currentRow.find("input[name$='.MinQuantity']");
                firstRowMinInput.val(DEFAULT_MIN_QUANTITY);
                firstRowMinInput.prop('readonly', false);

                // 清空其他输入
                currentRow.find("input[name$='.MaxQuantity']").val("");
                currentRow.find("input[name$='.Price']").val("");

                // 获取当前商品价格
                var goodsPrice = $('input[name="goodsPrice"]').val() || '';
                currentRow.find("input[name$='.OriginalPrice']").val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');

                layer.msg("@T("至少需要保留一行阶梯价格，已重置为默认值")");
                return;
            }

            // 如果有下一行，需要更新其最小数量
            if (nextRow.length > 0) {
                // 如果有上一行，则使用上一行的最大数量+1作为下一行的最小数量
                if (prevRow.length > 0) {
                    var prevMaxQuantity = parseInt(prevRow.find("input[name$='.MaxQuantity']").val()) || 0;
                    if (prevMaxQuantity > 0) {
                        var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                        nextMinInput.val(prevMaxQuantity + 1);
                        nextMinInput.prop('readonly', true);
                    }
                } else {
                    // 如果没有上一行，则下一行的最小数量设为默认值
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(DEFAULT_MIN_QUANTITY);
                    nextMinInput.prop('readonly', false);
                }
            }

            // 删除当前行
            currentRow.remove();

            // 重新计算行索引
            recalculateRowIndices();
        });

        // 阶梯价格输入的验证设置
        function setupTieredPriceValidation() {
            // 更改最大数量时验证
            $("#tieredPriceRows").on("change", "input[name$='.MaxQuantity']", function() {
                var currentRow = $(this).closest("tr");
                var currentMinInput = currentRow.find("input[name$='.MinQuantity']");
                var currentMaxInput = $(this);
                var nextRow = currentRow.next("tr.tiered-price-row");

                var minQuantity = parseInt(currentMinInput.val()) || 0;
                var maxQuantity = parseInt(currentMaxInput.val()) || 0;

                // 确保最大值大于最小值
                if (maxQuantity > 0 && maxQuantity <= minQuantity) {
                    layer.msg("@T("最大购买数量必须大于最小购买数量")");
                    currentMaxInput.val('');
                    return;
                }

                // 如果有下一行，则更新其最小数量为当前行的最大数量+1
                if (nextRow.length > 0 && maxQuantity > 0) {
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(maxQuantity + 1);
                    nextMinInput.prop('readonly', true);
                }
            });

            // 价格输入变化时验证
            $("#tieredPriceRows").on("change", "input[name$='.Price'], input[name$='.OriginalPrice']", function() {
                var value = parseFloat($(this).val());
                if (isNaN(value) || value <= 0) {
                    layer.msg("@T("价格必须是大于0的数字")");
                    $(this).val('');
                }
            });
        }

        // 删除后重新计算行索引
        function recalculateRowIndices() {
            $("#tieredPriceRows tr").each(function(index) {
                $(this).find("input").each(function() {
                    var name = $(this).attr("name");
                    if (name) {
                        name = name.replace(/\[\d+\]/, '[' + index + ']');
                        $(this).attr("name", name);
                    }
                });
            });

            // 更新行计数器
            rowCount = $("#tieredPriceRows tr").length;
        }

        // 初始化验证
        setupTieredPriceValidation();
    });
</script>
<link href="~/public/static/plugins/plyr/plyr.css" rel="stylesheet" />
<script src="~/public/static/plugins/plyr/plyr.js"></script>
<style>
    .plyr__video-embed {
        width: 200px;
        margin: 0 auto;
    }

    .container {
        max-width: 400px;
        max-height: 300px;
        margin: 20px auto;
        text-align: center;
    }

    .input-container {
        margin-bottom: 20px;
    }

    .videoUrl {
        width: 500px;
        padding: 10px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .loadBtn {
        padding: 10px 20px;
        background-color: #00adef;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

        .loadBtn:hover {
            background-color: #0089bd;
        }

    .plyr__video-embed {
        height: 300px;
        margin: 0 auto;
    }
</style>
<script>
    // 存储所有播放器实例
    let players = {};

    // 初始化播放器函数
    function initPlayer(langId = '') {
        const playerId = langId ? `player_${langId}` : 'player';
        if (players[playerId]) {
            players[playerId].destroy();
        }
        players[playerId] = new Plyr(`#${playerId}`, {
            controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            ratio: '6:9',
            resetOnEnd: false
        });
    }

    // 页面加载时初始化默认播放器
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化默认语言的播放器
        initPlayer();

        // 初始化其他语言的播放器
        document.querySelectorAll('[id^="player_"]').forEach(player => {
            const langId = player.id.split('_')[1];
            initPlayer(langId);
        });
    });

    function loadVideo(langId = '') {
        const urlInput = document.getElementById(langId ? `videoUrl_${langId}` : 'videoUrl').value.trim();
        if (!urlInput) {
            alert('@T("请输入视频链接")');
            return;
        }

        // 提取视频ID和hash
        const urlMatch = urlInput.match(/video\/(\d+)(?:\?h=([a-zA-Z0-9]+))?/);
        if (!urlMatch) {
            alert('@T("请输入有效的Vimeo视频链接")');
            return;
        }

        const videoId = urlMatch[1];
        const hash = urlMatch[2] || '';
        const playerId = langId ? `player_${langId}` : 'player';

        // 先销毁现有播放器
        if (players[playerId]) {
            players[playerId].destroy();
        }

        // 重建播放器容器
        const playerContainer = document.getElementById(playerId);
        playerContainer.innerHTML = `
            <iframe
                id="vimeoPlayer${langId ? '_' + langId : ''}"
                src=""
                allowfullscreen
                allowtransparency
                allow="autoplay"
            ></iframe>
        `;

        // 构建嵌入URL
        let embedUrl = `${urlInput}`;
        embedUrl += (embedUrl.includes('?') ? '&' : '?') + 'background=1&autoplay=1&loop=1&byline=0&title=0&controls=0';

        // 更新iframe的src
        document.getElementById(`vimeoPlayer${langId ? '_' + langId : ''}`).src = embedUrl;

        // 重新初始化播放器
        setTimeout(() => {
            initPlayer(langId);
        }, 100);
    }
    
</script>