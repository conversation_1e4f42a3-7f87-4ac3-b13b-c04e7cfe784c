﻿using B2B2CShop.Entity;
using DH.Entity;
using Flurl.Http;
using Microsoft.VisualStudio.Web.CodeGenerators.Mvc.Templates.BlazorIdentity.Pages.Manage;
using NewLife.Log;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.Mail.Core;
using XCode;

namespace B2B2CShop.Common
{
    /// <summary>  
    /// 静态类，用于定义扩展方法  
    /// </summary>  
    public static class CommonHelp
    {
        /// <summary>  
        /// 将decimal类型转换为进1保留两位小数的格式  
        /// </summary>  
        /// <param name="value"></param>  
        /// <returns></returns>  
        public static decimal ToKeepTwoDecimal(this decimal value)
        {
            return Math.Round(value, 2, MidpointRounding.ToPositiveInfinity);
        }

        /// <summary>
        /// 发送到货通知
        /// </summary>
        /// <param name="MaterialId">物料编号</param>

        public static async Task SendArrivalNotification(long MaterialId)
        {
            var arrivalNoticeList = ArrivalNotice.FindAllByMaterialIdAndStatus(MaterialId,0);
            var inventory = MerchantMaterial.GetQuantityByWIds(MaterialId.SafeString()).SafeString();

            foreach (var notice in arrivalNoticeList)
            {
                var skuDetail = GoodsSKUDetail.FindById(notice.SkuID);
                if (skuDetail == null) continue;
                var goodsName = GoodsLan.FindByGIdAndLId(skuDetail.GoodsId, notice.LId)?.LanName??skuDetail.GoodsName;
                var skuInfo = skuDetail.SpecValueDetail(notice.LId);
                if (!skuInfo.IsNullOrEmpty())
                {
                    skuInfo = $"({skuInfo.TrimEnd()})";
                }
                var modelMsgTpl = OtherMsgTpl.FindByMCode("ArrivalNotice"); //获取注册消息模板
                if (modelMsgTpl == null)
                {
                    XTrace.WriteLine("没有找到预约到货通知模板");
                    return;
                }
                var language = Language.FindById(notice.Id);//语言

                var currency = Currencies.FindByCode(notice.CurrencyCode??"USD");//货币

                var name = UserE.FindByID(notice.CreateUserID)?.Name ?? LocaleStringResource.GetResource("客户", language?.UniqueSeoCode??"CN");

                var price = currency?.SymbolLeft + (skuDetail.GoodsPrice * currency?.ExchangeRate??1).ToKeepTwoDecimal();

                var goodsLink = $"<a href='{DHSetting.Current.CurDomainUrl}/Goods/{notice.SkuID}.html' target='_blank' >{goodsName}</a>";//商品链接

                var (fs, fs1) = MailCommonHelp.GetArrivalNotice(modelMsgTpl, goodsName, skuInfo, DateTime.Now.ToShortDateString(), name,inventory,goodsLink,price, notice.LId);
                var box = new EmailBox
                {
                    Subject = fs1.ToString(),
                    To = [notice.Email],
                    Body = fs.ToString(),
                    IsBodyHtml = true
                };
                try
                {
                    var mes = await MailCommonHelp.SendMail(box).ConfigureAwait(false);  //邮件发送
                    //notice.Delete(); //删除通知记录
                    var model = new SendLog
                    {
                        SType = 1,
                        Account = notice.Email,
                        Msg = fs.ToString(),
                        MType = 1
                    };
                    model.SaveAsync();


                    if (!mes.Contains("OK", StringComparison.OrdinalIgnoreCase))
                    {
                        XTrace.WriteLine($"[{notice.Email}]到货通知发送失败");
                        continue;
                    }
                    
                }
                catch (Exception ex)
                {
                    XTrace.WriteLine($"[{notice.Email}]发送失败:" + ex.Message);
                    continue;
                }
            }
            arrivalNoticeList.Delete();
            ArrivalNotice.Meta.Cache.Clear("", true);
        }
    }
}
