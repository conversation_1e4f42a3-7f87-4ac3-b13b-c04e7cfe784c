﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace B2B2CShop.Entity;

/// <summary>商家物料</summary>
[Serializable]
[DataObject]
[Description("商家物料")]
[BindIndex("IU_DH_MerchantMaterial_StoreId_Name", true, "StoreId,Name")]
[BindIndex("IX_DH_MerchantMaterial_Enabled", false, "Enabled")]
[BindTable("DH_MerchantMaterial", Description = "商家物料", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class MerchantMaterial : IMerchantMaterial, IEntity<IMerchantMaterial>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _StoreId;
    /// <summary>店铺ID</summary>
    [DisplayName("店铺ID")]
    [Description("店铺ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("StoreId", "店铺ID", "")]
    public Int64 StoreId { get => _StoreId; set { if (OnPropertyChanging("StoreId", value)) { _StoreId = value; OnPropertyChanged("StoreId"); } } }

    private String _Name = null!;
    /// <summary>名称</summary>
    [DisplayName("名称")]
    [Description("名称")]
    [DataObjectField(false, false, false, 50)]
    [BindColumn("Name", "名称", "", Master = true)]
    public String Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private Int32 _Quantity;
    /// <summary>总数量</summary>
    [DisplayName("总数量")]
    [Description("总数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Quantity", "总数量", "")]
    public Int32 Quantity { get => _Quantity; set { if (OnPropertyChanging("Quantity", value)) { _Quantity = value; OnPropertyChanged("Quantity"); } } }

    private Int32 _TemporaryQuantity;
    /// <summary>暂扣数量</summary>
    [DisplayName("暂扣数量")]
    [Description("暂扣数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("TemporaryQuantity", "暂扣数量", "")]
    public Int32 TemporaryQuantity { get => _TemporaryQuantity; set { if (OnPropertyChanging("TemporaryQuantity", value)) { _TemporaryQuantity = value; OnPropertyChanged("TemporaryQuantity"); } } }

    private Boolean _Enabled;
    /// <summary>是否启用。默认启用</summary>
    [DisplayName("是否启用")]
    [Description("是否启用。默认启用")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Enabled", "是否启用。默认启用", "")]
    public Boolean Enabled { get => _Enabled; set { if (OnPropertyChanging("Enabled", value)) { _Enabled = value; OnPropertyChanged("Enabled"); } } }

    private Int32 _Status;
    /// <summary>状态 0:未审核,1:已审核 2:审核不通过</summary>
    [DisplayName("状态0")]
    [Description("状态 0:未审核,1:已审核 2:审核不通过")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Status", "状态 0:未审核,1:已审核 2:审核不通过", "", DefaultValue = "0")]
    public Int32 Status { get => _Status; set { if (OnPropertyChanging("Status", value)) { _Status = value; OnPropertyChanged("Status"); } } }

    private String? _Images;
    /// <summary>上传图片</summary>
    [DisplayName("上传图片")]
    [Description("上传图片")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("Images", "上传图片", "")]
    public String? Images { get => _Images; set { if (OnPropertyChanging("Images", value)) { _Images = value; OnPropertyChanged("Images"); } } }

    private String? _Cause;
    /// <summary>审核原因</summary>
    [DisplayName("审核原因")]
    [Description("审核原因")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("Cause", "审核原因", "")]
    public String? Cause { get => _Cause; set { if (OnPropertyChanging("Cause", value)) { _Cause = value; OnPropertyChanged("Cause"); } } }

    private Int32 _Auditor;
    /// <summary>审核者</summary>
    [DisplayName("审核者")]
    [Description("审核者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Auditor", "审核者", "")]
    public Int32 Auditor { get => _Auditor; set { if (OnPropertyChanging("Auditor", value)) { _Auditor = value; OnPropertyChanged("Auditor"); } } }

    private Int64 _AuditTime;
    /// <summary>审核时间</summary>
    [DisplayName("审核时间")]
    [Description("审核时间")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("AuditTime", "审核时间", "")]
    public Int64 AuditTime { get => _AuditTime; set { if (OnPropertyChanging("AuditTime", value)) { _AuditTime = value; OnPropertyChanged("AuditTime"); } } }

    private String? _Remark;
    /// <summary>备注</summary>
    [DisplayName("备注")]
    [Description("备注")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Remark", "备注", "")]
    public String? Remark { get => _Remark; set { if (OnPropertyChanging("Remark", value)) { _Remark = value; OnPropertyChanged("Remark"); } } }

    private Decimal _Weight;
    /// <summary>重量</summary>
    [DisplayName("重量")]
    [Description("重量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Weight", "重量", "", DefaultValue = "0.000")]
    public Decimal Weight { get => _Weight; set { if (OnPropertyChanging("Weight", value)) { _Weight = value; OnPropertyChanged("Weight"); } } }

    private Decimal _Volume;
    /// <summary>体积</summary>
    [DisplayName("体积")]
    [Description("体积")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Volume", "体积", "", DefaultValue = "0.000")]
    public Decimal Volume { get => _Volume; set { if (OnPropertyChanging("Volume", value)) { _Volume = value; OnPropertyChanged("Volume"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IMerchantMaterial model)
    {
        Id = model.Id;
        StoreId = model.StoreId;
        Name = model.Name;
        Quantity = model.Quantity;
        TemporaryQuantity = model.TemporaryQuantity;
        Enabled = model.Enabled;
        Status = model.Status;
        Images = model.Images;
        Cause = model.Cause;
        Auditor = model.Auditor;
        AuditTime = model.AuditTime;
        Remark = model.Remark;
        Weight = model.Weight;
        Volume = model.Volume;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "StoreId" => _StoreId,
            "Name" => _Name,
            "Quantity" => _Quantity,
            "TemporaryQuantity" => _TemporaryQuantity,
            "Enabled" => _Enabled,
            "Status" => _Status,
            "Images" => _Images,
            "Cause" => _Cause,
            "Auditor" => _Auditor,
            "AuditTime" => _AuditTime,
            "Remark" => _Remark,
            "Weight" => _Weight,
            "Volume" => _Volume,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "StoreId": _StoreId = value.ToLong(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Quantity": _Quantity = value.ToInt(); break;
                case "TemporaryQuantity": _TemporaryQuantity = value.ToInt(); break;
                case "Enabled": _Enabled = value.ToBoolean(); break;
                case "Status": _Status = value.ToInt(); break;
                case "Images": _Images = Convert.ToString(value); break;
                case "Cause": _Cause = Convert.ToString(value); break;
                case "Auditor": _Auditor = value.ToInt(); break;
                case "AuditTime": _AuditTime = value.ToLong(); break;
                case "Remark": _Remark = Convert.ToString(value); break;
                case "Weight": _Weight = Convert.ToDecimal(value); break;
                case "Volume": _Volume = Convert.ToDecimal(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static MerchantMaterial? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据店铺ID、名称查找</summary>
    /// <param name="storeId">店铺ID</param>
    /// <param name="name">名称</param>
    /// <returns>实体对象</returns>
    public static MerchantMaterial? FindByStoreIdAndName(Int64 storeId, String name)
    {
        if (storeId < 0) return null;
        if (name.IsNullOrEmpty()) return null;

        return Find(_.StoreId == storeId & _.Name == name);
    }

    /// <summary>根据店铺ID查找</summary>
    /// <param name="storeId">店铺ID</param>
    /// <returns>实体列表</returns>
    public static IList<MerchantMaterial> FindAllByStoreId(Int64 storeId)
    {
        if (storeId < 0) return [];

        return FindAll(_.StoreId == storeId);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得商家物料字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>店铺ID</summary>
        public static readonly Field StoreId = FindByName("StoreId");

        /// <summary>名称</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>总数量</summary>
        public static readonly Field Quantity = FindByName("Quantity");

        /// <summary>暂扣数量</summary>
        public static readonly Field TemporaryQuantity = FindByName("TemporaryQuantity");

        /// <summary>是否启用。默认启用</summary>
        public static readonly Field Enabled = FindByName("Enabled");

        /// <summary>状态 0:未审核,1:已审核 2:审核不通过</summary>
        public static readonly Field Status = FindByName("Status");

        /// <summary>上传图片</summary>
        public static readonly Field Images = FindByName("Images");

        /// <summary>审核原因</summary>
        public static readonly Field Cause = FindByName("Cause");

        /// <summary>审核者</summary>
        public static readonly Field Auditor = FindByName("Auditor");

        /// <summary>审核时间</summary>
        public static readonly Field AuditTime = FindByName("AuditTime");

        /// <summary>备注</summary>
        public static readonly Field Remark = FindByName("Remark");

        /// <summary>重量</summary>
        public static readonly Field Weight = FindByName("Weight");

        /// <summary>体积</summary>
        public static readonly Field Volume = FindByName("Volume");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得商家物料字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>店铺ID</summary>
        public const String StoreId = "StoreId";

        /// <summary>名称</summary>
        public const String Name = "Name";

        /// <summary>总数量</summary>
        public const String Quantity = "Quantity";

        /// <summary>暂扣数量</summary>
        public const String TemporaryQuantity = "TemporaryQuantity";

        /// <summary>是否启用。默认启用</summary>
        public const String Enabled = "Enabled";

        /// <summary>状态 0:未审核,1:已审核 2:审核不通过</summary>
        public const String Status = "Status";

        /// <summary>上传图片</summary>
        public const String Images = "Images";

        /// <summary>审核原因</summary>
        public const String Cause = "Cause";

        /// <summary>审核者</summary>
        public const String Auditor = "Auditor";

        /// <summary>审核时间</summary>
        public const String AuditTime = "AuditTime";

        /// <summary>备注</summary>
        public const String Remark = "Remark";

        /// <summary>重量</summary>
        public const String Weight = "Weight";

        /// <summary>体积</summary>
        public const String Volume = "Volume";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
