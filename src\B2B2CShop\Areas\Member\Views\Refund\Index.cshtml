@using B2B2CShop.Dto
@inject IWorkContext workContext
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/refund.css");
    var site = SiteInfo.GetDefaultSeo();
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
    DateTime dtStart = Model.buyStartDate;
    DateTime dtEnd = Model.buyEndDate;
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Refund")">@T("退货和退款")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <style>
        .layui-laypage a,
        .layui-laypage span {
            font-size: 14px;
        }
            .main {
            margin-left: 15%;
            width: 70%;
            }
            aside {
            padding: 10px 0px;
            width: 15%;
            min-width: 200px;
            height: fit-content;
            border: 1px solid #E2E3E9;
            /* background-color: red; */
            }

            aside>div:nth-child(1) {
            font-size: 20px;
            padding-top: 10px;
            text-indent: 15%;
            font-weight: 550;
            }

            aside>a:not(:first-child) {
            margin-bottom: .5vw;
            padding: 7px 0vw;
            font-size: 15px;
            display: flex;
            justify-content: left;
            place-items: center;
            width: 100%;
            cursor: pointer;
            }

            aside>a>div:nth-child(1) {
            margin-right: .5vw;
            margin-left: 15%;
            font-size: 20px;
            }

            ._line {
            width: 80% !important;
            height: 0px !important;
            border: none;
            border-bottom: 1px solid var(--line);
            margin: 0px 0px .7vw 10% !important;
            }

            aside>a:not(.bgSelect):hover {
            color: var(--blue-deep);
            }
        </style>
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
 @*            <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
  @*           <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content" data-show="true" style="display: block;">
            <!-- 过滤功能 -->
            <form action="@Url.Action("Index")">
                <div class="filterBox">
                    <div class="flex" style="width: 30%;margin: 0;">
                        <div class="label">@T("关键词"):</div>
                        <div class="layui-form"><input class="layui-input" placeholder="@T("订单号")/BOM @T("标识")" name="searchKey" value="@Model.searchKey"></div>
                    </div>
                    <div class="flex" style="margin-right: 0.2vw;">
                        <div class="label">@T("订单状态"): </div>
                        <div class="layui-form">
                            <select class="select" name="refundState" id="refundState" style="font-size: 1em;" lay-filter="refundState" title="@T("退款状态")">
                                <option value="-1" selected="@(Model.refundState == -1)">@T("全部类别")</option>
                                <option value="3" selected="@(Model.refundState == 3)">@T("退款成功")</option>
                                <option value="4" selected="@(Model.refundState == 4)">@T("退款失败")</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form flex" style="width: 50%;margin-right: 0;">
                        <div class="layui-inline" id="ID-laydate-rangeLinked">
                            <div class="layui-form-mid" style="white-space: nowrap;">@T("申请时间"):</div>
                            <div class="layui-input-inline" style="width: 35%;">
                                <input id="ID-laydate-start-date-1" class="layui-input" placeholder="@T("开始日期")" name="buyStartDate" value="@(dtStart==DateTime.MinValue?"": dtStart.ToString("yyyy-MM-dd"))">
                            </div>
                            -
                            <div class="layui-input-inline" style="width: 35%;">
                                <input id="ID-laydate-end-date-1" class="layui-input" placeholder="@T("结束日期")" name="buyEndDate" value="@(dtEnd==DateTime.MinValue?"":dtEnd.ToString("yyyy-MM-dd"))">
                            </div>
                        </div>

                        <script>
                            var laydate = layui.laydate;
                            // 日期范围 - 左右面板联动选择模式
                            laydate.render({
                                elem: '#ID-laydate-rangeLinked',
                                range: ['#ID-laydate-start-date-1', '#ID-laydate-end-date-1'],
                                lang: '@Language?.UniqueSeoCode',
                                rangeLinked: true, // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
                                done: function(value, date, endDate){
                                   // 选择日期后自动提交表单
                                   $('#ID-laydate-rangeLinked').closest('form').submit();
                                }
                            });
                        </script>
                    </div>

                    <div style="width: fit-content;margin: 0px;">
                        <button class="button button_blue" type="submit">@T("查询")</button>
                    </div>
                </div>
            </form>
            <!-- 所有订单-表格 -->
            <div class="optionsBox">
                <table class="layui-table" style="background-color: white;">
                <colgroup>
                    <col width="50%">
                    <col width="10%">
                    <col width="10%">
                    <col width="10%">
                    <col width="10%">
                    <col width="10%">
                </colgroup>
                <thead>
                    <tr style="background-color: var(--text-color4);color: var(--text-color);font-weight: 500;">
                        <th>@T("订单详情")</th>
                        <th>@T("退款金额")</th>
                        <th>@T("申请时间")</th>
                        <th>@T("服务类型")</th>
                        <th>@T("退款状态")</th>
                        <th>@T("交易操作")</th>
                    </tr>
                </thead>
            </table>
            </div>
            <div class="tablesBox">
                @foreach (RefundReturnDto item in Model.list)
                {
                    <table class="layui-table" lay-skin="line" style="background-color: white;" >
                        <colgroup>
                            <col width="50%">
                            <col width="10%">
                            <col width="10%">
                            <col width="10%">
                            <col width="10%">
                            <col width="10%">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>
                                    <b>@T("订单编号"):</b>
                                    <span id="orderId"> @item.OrderSn</span>
                                    <a class="textSelect pointer" style="margin-left: 6%;" href="@Url.Action("ProviderDetail", "Supplier", new { sid = item.StoreId, Area = "" })">
                                        @item.StoreName
                                    </a>
                                </th>
                                <th></th>
                                <th></th>
                                <th colspan="3">

                                    @* <i class="iconfont icon icon-zaixiankefu1" style="margin-left: 1vw;"
                                       onclick="toRouter(this)" data-link=""></i> *@
                                </th>
                            </tr>
                        </thead>
                        <tbody id="<EMAIL>">
                            @{var rowCount= item.OrderGoods.Count;}
                            @{
                                var i = 0;
                                foreach (OrderGoodsDto goods in item.OrderGoods)
                                {
                                    <tr class="@(i >= 2 ? "row-display" : "")">
                                        <td>
                                            <div class="goodsInfo" style="place-items: center;">
                                                <div style="width: 30%;padding: .5vw;border-radius: 5px;">
                                                    <img src="@goods.GoodsImage" alt="商品图片">
                                                </div>
                                                <div class="goodsInformation" style="width: 60%;margin-left: 5%;margin-right: auto;">
                                                    <div class="tdTitle"><span class="textOver2">@goods.GoodsName</span></div>
                                                    <div class="textOver"><p>@goods.SpecValue</p></div>
                                                    <div>@T("制造商"): <span class="name textOver">@goods.CompanyName</span></div>
                                                    <div class="textOver">@T("型号"):<span class="name textOver"></span></div>
                                                </div>
                                            </div>
                                        </td>
                                        @if (i == 0)
                                        {
                                        <td class="red2" style="font-size: .9vw;" rowspan="2">
                                            <b>@<EMAIL></b>
                                        </td>
                                        <td rowspan="2">
                                            @item.AddTime
                                        </td>
                                        <td rowspan="2">
                                            @if (item.RefundType == 1)
                                            {
                                                <b class="red2">@T("仅退款")</b>
                                            }
                                            else
                                            {
                                                <b class="red2">@T("退货退款")</b>
                                            }
                                        </td>
                                        <td rowspan="2">
                                            @if (item.RefundreturnSellerState == 1)
                                            {
                                                <b>@T("等待商家处理")</b>
                                            }
                                            else if (item.RefundreturnSellerState == 2)
                                            {
                                                switch (item.RefundreturnGoodsState)
                                                {
                                                    case 1:
                                                        <b>@T("待退货")</b>
                                                        break;
                                                    case 2:
                                                        <b>@T("待仓库收货")</b>
                                                        break;
                                                    case 3:
                                                        <b>@T("仓库未收到")</b>
                                                        break;
                                                    case 4:
                                                        <b>@T("待商家退款")</b>
                                                        break;
                                                }
                                            }
                                            else if (item.RefundreturnSellerState == 3)
                                            {
                                                <b class="success">@T("已拒绝")</b>
                                            }
                                            else if (item.RefundreturnSellerState == 4)
                                            {
                                                <b class="red">@T("退款成功")</b>
                                            }
                                        </td>
                                        <td class="primary pointer" rowspan="2">
                                            <p><a href="@Url.Action("Detail")?Id=@item.Id">@T("服务详情")</a></p>
                                            @if (item.RefundreturnGoodsState == 1)
                                            {
                                                <p><a onclick="openReturnDialog(@item.Id)" data-link="" class="btn btn-primary">@T("退货")</a></p>
                                            }
                                        </td>
                                    }

                                </tr>
                                    i++;
                                }

                                @if(item.OrderGoods.Count > 2)
                                {
                                    <tr id="<EMAIL>">
                                        <td class="hover" style="text-align: center;" colspan="6" onclick="rowShow('@item.Id', true)">
                                            --- @T("展开所有商品") (@item.OrderGoods.Count) ---
                                        </td>
                                    </tr>
                                    <tr class="row-display" id="<EMAIL>">
                                        <td class="hover" style="text-align: center;" colspan="6" onclick="rowShow('@item.Id', false)">
                                            --- @T("收起") ---
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                }
                <div id="pagingBox" style="text-align: right;"></div>
                <style>
                    .row-display { display: none; }
                </style>
                <script>
                    function select(dom) {
                        const type = dom.getAttribute("data-type");
                        const parentDom = dom.parentNode;
                        $(parentDom).children().attr('class', '')
                        // console.log(parentDom,$(parentDom));
                        if (type == 0) {
                            dom.className = "bgSelect";
                        } else if (type == 1) {
                            dom.className = "bgSelect";
                        } else if (type == 2) {
                            dom.className = "bgSelect";
                        } else if (type == 3) {
                            dom.className = "bgSelect";
                        }
                    }

                    function rowShow(id, isshow) {
                        if(isshow) {
                            // 展开所有商品
                            var rows = document.querySelectorAll('#goods-' + id + ' tr');
                            rows.forEach(function(row) {
                                row.classList.remove('row-display');
                            });

                            // 获取实际商品数量（不包括展开/收起按钮行）
                            var goodsCount = 0;
                            rows.forEach(function(row) {
                                if(!row.id) goodsCount++;
                            });

                            // 修改rowspan为实际商品数量
                            var firstRow = rows[0];
                            var cells = firstRow.querySelectorAll('td[rowspan="2"]');
                            cells.forEach(function(cell) {
                                cell.setAttribute('rowspan', goodsCount);
                            });

                            // 隐藏“展开”按钮，显示“收起”按钮
                            document.getElementById('open-' + id).classList.add('row-display');
                            document.getElementById('close-' + id).classList.remove('row-display');
                        } else {
                            // 只显示前两个商品
                            var rows = document.querySelectorAll('#goods-' + id + ' tr');
                            rows.forEach(function(row, index) {
                                if(index >= 2 && !row.id) {
                                    row.classList.add('row-display');
                                }
                            });

                            // 将rowspan改回2
                            var firstRow = rows[0];
                            var cells = firstRow.querySelectorAll('td[rowspan]');
                            cells.forEach(function(cell) {
                                cell.setAttribute('rowspan', 2);
                            });

                            // 显示“展开”按钮，隐藏“收起”按钮
                            document.getElementById('open-' + id).classList.remove('row-display');
                            document.getElementById('close-' + id).classList.add('row-display');
                        }
                    }
                    layui.use(function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'pagingBox',
                            count: @Model.total, // 数据总数
                            limit: @(Model.limit > 0 ? Model.limit : 10), // 每页显示条数
                            limits: [5,10, 20, 50, 100], // 每页条数的选择项
                            curr: @(Model.page > 0 ? Model.page : 1), // 当前页码
                            groups: 5, // 连续显示页码个数
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                            theme: '#2C79E8', // 自定义主题色
                            prev: '@T("上一页")',
                            next: '@T("下一页")',
                            first: '@T("首页")',
                            last: '@T("尾页")',
                            countText: ['@T("共") ',' @T("条")'],
                            skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                            limitTemplet: function(item) {
                              return item + ' @T("条/页")';
                            },
                            jump: function(obj, first) {
                                // 首次不执行（首次加载时不跳转）
                                if (!first) {
                                    // 创建一个隐藏的表单来提交所有参数
                                    var $form = $('<form></form>');
                                    $form.attr('action', '@Url.Action("Index")');
                                    $form.attr('method', 'get');
                                    $form.css('display', 'none');

                                    // 添加页码参数
                                    $form.append('<input type="hidden" name="page" value="' + obj.curr + '" />');

                                    // 添加每页条数参数
                                    $form.append('<input type="hidden" name="limit" value="' + obj.limit + '" />');

                                    // 获取当前表单中的所有参数
                                    $('form:first input, form:first select').each(function() {
                                        var name = $(this).attr('name');
                                        var value = $(this).val();

                                        // 如果存在名称和值，并且不是页码相关参数，则添加到隐藏表单中
                                        if (name && value && name !== 'page' && name !== 'limit') {
                                            $form.append('<input type="hidden" name="' + name + '" value="' + value + '" />');
                                        }
                                    });

                                    // 将表单添加到文档中并提交
                                    $('body').append($form);
                                    $form.submit();
                                }
                            }
                        });
                    });
                     function openReturnDialog(refundId) {
                        layer.open({
                            type: 2,
                            title: '@T("退货处理")',
                            area: ['500px', '450px'],
                            fixed: false,
                            maxmin: true,
                            content: '@Url.Action("Return")?id=' + refundId,
                            scrollbar: false,
                            shadeClose: true,

                        });
                    }
                </script>

            </div>
        </div>
    </div>
    <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>
</div>
<script asp-location="Footer">
    layui.use(['form'],function()
    {
        var form = layui.form;
        form.on('select(refundState)', function(data){
            $(data.elem).closest('form').submit();
        });
    });
</script>