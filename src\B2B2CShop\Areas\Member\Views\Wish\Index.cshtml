@using B2B2CShop.Dto
@inject IWorkContext workContext
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/wish.css");
    var site = SiteInfo.GetDefaultSeo();
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Wish")">@T("心愿清单")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <style>
            .layui-laypage a,
            .layui-laypage span {
                font-size: 14px;
            }
            .main {
            margin-left: 15%;
            width: 70%;
            }

            .userInfoBox {
            margin-bottom: 1vw;
            height: 70vh;
            display: flex;
            /* border: 1px solid ; */
            }

            aside {
            padding: 10px 0px;
            width: 15%;
            min-width: 200px;
            height: fit-content;
            border: 1px solid #E2E3E9;
            /* background-color: red; */
            }

            aside>div:nth-child(1) {
            font-size: 20px;
            padding-top: 10px;
            text-indent: 15%;
            font-weight: 550;
            }

            aside>a:not(:first-child) {
            margin-bottom: .5vw;
            padding: 7px 0vw;
            font-size: 15px;
            display: flex;
            justify-content: left;
            place-items: center;
            width: 100%;
            cursor: pointer;
            }

            aside>a>div:nth-child(1) {
            margin-right: .5vw;
            margin-left: 15%;
            font-size: 20px;
            }

            ._line {
            width: 80% !important;
            height: 0px !important;
            border: none;
            border-bottom: 1px solid var(--line);
            margin: 0px 0px .7vw 10% !important;
            }

            aside>a:not(.bgSelect):hover {
            color: var(--blue-deep);
            }

            .content {
            width: 80%;
            display: block;
            padding: 0vw 1vw 1vw 1vw;
            }
        </style>
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content" data-show="true">
            <form method="get">
                <div class="filterBox">
                <div class="flex" style="margin-right: 0.5vw;">
                    <div class="label">@T("商品分类"): </div>
                    <div class="layui-form">
                        <select class="select" name="CId" lay-filter="goodsClass" id="goodsClass" title="@T("商品分类")">
                            <option value="0" selected>@T("全部")</option>
                            @foreach (var item in Model.GoodsClassList)
                            {
                                <option value="@item.Id" selected="@(Model.CId==item.Id)">@item.Name</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
            </form>
            
            <!-- 所有订单-表格 -->
            <div class="tablesBox">
                <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0px;">
                    <colgroup>
                        <col width="60%">
                        <col width="15%">
                    </colgroup>
                    <tbody>
                        @foreach (WishDto item in Model.list)
                        {
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="layui-form" style="margin-right: .5vw;">
                                            <input type="checkbox" name="BBB" value="@item.Id">
                                        </div>
                                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                                            <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load goodsImg" alt="@T("商品图片")">
                                            </div>
                                        </a>
                                        <div class="goodsInformation"
                                        style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            @if (item.GoodsId == "0")
                                            {
                                                <div class="tdTitle">
                                                    <span class="textOver2">
                                                        @T("商品已下架")
                                                    </span>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="tdTitle">
                                                    <span class="textOver2">
                                                        @item.GoodsName
                                                    </span>
                                                </div>
                                                @*     <div>@T("原厂编号")<span class="name textOver">ACDC电源模块</span></div>*@
                                                <div>@T("制造商"): <span class="name textOver"></span> </div>
                                                @*<div class="textOver">
                                                    @T("制造商编号"): <span class="name textOver">727-S40FC008C3B1V000 </span>
                                                </div>*@
                                                <div class="textOver">
                                                    @T("型号"):<span class="name textOver"></span>
                                                </div>
                                            }
                                            <div>@T("收藏时间")：<span class="textOver">@item.AddDate.ToString("yyyy-MM-dd HH:mm:ss")</span></div>
                                        </div>
                                    </div>
                                </td>
                                <!-- Replace the existing static tiered price section with the dynamic one -->
                                <td class="fnTd">
                                    @if (item.GoodsId != "0")
                                    {
                                        <!-- 显示单个价格 -->
                                        <div style="margin-bottom: 8px; font-weight: bold;">
                                            @T("单价"): @<EMAIL>("F2")
                                            @if (item.GoodsMarketPrice > 0 && item.GoodsMarketPrice != item.GoodsPrice)
                                            {
                                                <span style="text-decoration: line-through; color: #999; margin-left: 5px;">
                                                    @<EMAIL>("F2")
                                                </span>
                                            }
                                        </div>

                                        <!-- 显示阶梯价格 -->
                                        @if (item.TieredPrices != null && item.TieredPrices.Any())
                                        {
                                            <div style="margin-top: 5px;">
                                                <div style="font-size: 0.9em; color: #666;">@T("阶梯价格"):</div>
                                                @{
                                                    var lastIndex = item.TieredPrices.Count() - 1;
                                                    int i = 0;
                                                    foreach (var tier in item.TieredPrices)
                                                    {
                                                        <div>@tier.MinQuantity@(i == lastIndex ? "+" : (tier.MaxQuantity > 0 ? " ~ " + tier.MaxQuantity : "")): @<EMAIL>("F2")</div>
                                                        i++;
                                                    }
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div style="color: #999; font-size: 0.9em;">@T("暂无阶梯价格")</div>
                                        }
                                    }
                                </td>
                                <td class="tableBtnBox">
                                    <div><button class="button button_red" onclick="delWishlist('@item.Id')">@T("取消收藏")</button></div>
                                    @if (item.GoodsId != "0")
                                    {
                                        <div><button class="button button_blue" onclick="addCart('@item.SkuId')">@T("添加购物车")</button></div>
                                    }

                                </td>
                                </tr>
                            }   
                        </tbody>
                    </table>
                    <div class="flex">
                        <div class="layui-form flex" style="place-items: top;">
                        <input type="checkbox" name="selectAll" id="selectAll" lay-filter="selectAll">
                            <label for="qx" class="pointer">@T("全选")</label>
                        <button class="button button_red" style="border-radius: 45px;margin-left: 1vw;" onclick="BatchCancelWish()">@T("取消收藏")</button>
                        </div>
                        <div id="pagingBox" style="text-align: right;margin:.8vw 0px 1vw auto;"></div>
                        <script>
                            function select(dom) {
                                const type = dom.getAttribute("data-type");
                                const parentDom = dom.parentNode;
                                $(parentDom).children().attr('class', '')
                                // console.log(parentDom,$(parentDom));
                                if (type == 0) {
                                    dom.className = "bgSelect";
                                } else if (type == 1) {
                                    dom.className = "bgSelect";
                                } else if (type == 2) {
                                    dom.className = "bgSelect";
                                } else if (type == 3) {
                                    dom.className = "bgSelect";
                                }
                            }
                            layui.use(function () {
                                var laypage = layui.laypage;
                                laypage.render({
                                    elem: 'pagingBox',
                                    limit:5,
                                    count: '@ViewBag.Total', // 数据总数
                                    curr: '@ViewBag.Page', // 数据总数
                                    theme: '#2C79E8',
                                    prev: '@T("上一页")',
                                    next: '@T("下一页")',
                                    first: '@T("首页")',
                                    last: '@T("尾页")',
                                    countText: ['@T("共") ',' @T("条")'],
                                    skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                                    limitTemplet: function(item) {
                                      return item + ' @T("条/页")';
                                    },
                                    jump:function(obj, first)
                                    {
                                        if(!first)
                                        {
                                            window.location.href = '@Url.Action("Index")?page='+obj.curr;
                                        }
                                    }
                                });
                            });
                        </script>
                    </div>
                </div>
            </div>
    </div>
        <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>
</div>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
    //批量取消心愿清单
    function BatchCancelWish()
    {
         var selectedValues = [];
         $('input[name="BBB"]:checked').each(function () 
         {
             selectedValues.push($(this).val());
          }); 
          var Ids = selectedValues.join(",");
          console.log(1,Ids);
          $.post("@Url.Action("BatchCancelWish")",{Ids},function(res)
          {
                layui.layer.msg(res.msg);
                if(res.success)
                {
                    location.reload();
                }
          })
    }

    layui.use(['form'],function()
    {
        var form = layui.form;
        form.on('select(goodsClass)', function(data){
            $(data.elem).closest('form').submit();
        });
        layui.form.on("checkbox(selectAll)",function(data)
        {
            var isChecked = this.checked;
            $('input[name="BBB"]').prop('checked', isChecked);
            layui.form.render('checkbox');
        })
    });

    //按分类查询
    function queryList()
    {
        var elements  =  document.getElementById('filterBoxContent').querySelector(".textSelect");
        if(elements)
        {
            window.location.href = '@Url.Action("Index")?CId='+elements.id;
        }
        else
        {
            window.location.href = '@Url.Action("Index")';
        }
    }

    //取消心愿清单
    function delWishlist(Ids)
    {
        $.post("@Url.Action("BatchCancelWish")",{Ids},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                location.reload();
            }
        })
    }

    //加入购物车
    function addCart(skuId)
    {
        var GoodsNum = 1;
        $.post("/CubeHome/AddCart",{skuId,GoodsNum},function(res)
        {
            layui.layer.msg(res.msg);
            if (res.success) {
                // 如果添加成功，获取当前购物车数量
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(res.data);
                }
            }
        })
    }
     //选择SKU加入购物车
    function OpenSkuInfo(GoodsId)
    {
        var GoodsNum = 1;
        if  (isNaN(GoodsNum) || GoodsNum <= 0) {
            layui.layer.msg('@T("商品数量必须大于0")');
            return;
        }
        layui.layer.open({
            type: 2, // iframe
            title: '@T("选择商品属性")',
            area: ['700px', '400px'],
            shadeClose: true,
            content: '/CubeHome/SkuSelector?goodsId=' + GoodsId + '&num=' + GoodsNum,
        });
    }
    ///获取弹窗参数更新购物车
    function getChildrenData(res)
    {
         if (res && res.success && typeof window.updateCartCount === 'function') {
            window.updateCartCount(res.data); // 更新购物车数量
         }
    }
</script>