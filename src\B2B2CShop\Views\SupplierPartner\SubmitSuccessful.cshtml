﻿@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*T("提交成功")*@
@model StoreJoinIn
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/submitSuccessful.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a class="textSelect" href="@Url.Action("Index")">
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("公司资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <div class="center-card layui-card">
            <div class="successBox">
                @if(Model.State<20)
                {
                    <img src="~/public/images/icons/chenggong.png" alt="">
                    <p>@T("入驻申请已经提交，请等待管理员审核")</p>
                }
                else if(Model.State == 30)
                {
                    <img src="~/public/images/icons/shibai.png" alt="">
                    <p>@T("审核失败")：@Model.Message</p>
                }
            </div>
            <form class="layui-form">
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("店铺信息列表")</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table first-table">
                                <tr>
                                    <td>@T("店铺名称")</td>
                                    <td>@Model.Name</td>
                                    <td>@T("店铺等级")</td>
                                    <td>@Model.StoreGradeName</td>
                                </tr>
                                <tr>
                                    <td>@T("店铺分类")</td>
                                    <td>@Model.StoreClassName</td>
                                    <td>@T("开店时间")</td>
                                    <td>@Model.Year@("年")</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("经营类目列表")</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table">
                                
                                <tr>
                                    <th>@T("一级类目")</th>
                                    <th>@T("二级类目")</th>
                                    <th>@T("分佣比例%")</th>
                                </tr>
                                @foreach (var item in Model.BusinessClasses)
                                {
                                    var goodsClasss1 = GoodsClass.FindById(item.CId1);
                                    var goodsClasss2 = GoodsClass.FindById(item.CId2);
                                    <tr>
                                        <td>@goodsClasss1?.Name</td>
                                        <td>@goodsClasss2?.Name</td>
                                        <td>@goodsClasss2?.CommisRate</td>
                                    </tr>
                                }
                            </table>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <a href="#">
                            <button id="submitBtn" class="layui-btn btn" lay-submit lay-filter="demo1">@T("下一步")</button>
                         </a>     
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="bug"></div>
<script>
   layui.use(function () {
        var form = layui.form;
        var layer = layui.layer;
        form.on('submit(demo1)', function (data) { 
            var field = data.field; 
            console.log("field", field);
            return false; 
        });
    }); 
</script>