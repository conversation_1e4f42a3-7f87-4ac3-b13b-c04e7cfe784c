﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*提交成功*@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/submitSuccessful.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">首页</a>
        <div>></div>
        <a class="textSelect" href="#">
            供应商合作
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            商家入驻申请
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>签订入驻协议</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>公司资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>财务资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>店铺经营信息</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>合同签订</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>店铺开通</p>
            </div>
        </div>

        <div class="center-card layui-card">
            <div class="successBox">
                <img src="~/public/images/icons/chenggong.png" alt="">
                <p>入驻申请已经提交，请等待管理员审核</p>
            </div>
            <form class="layui-form">
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label">店铺信息列表</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table first-table">
                                <tr>
                                    <td>店铺名称</td>
                                    <td>123456789</td>
                                    <td>店铺等级</td>
                                    <td>系统默认</td>
                                </tr>
                                <tr>
                                    <td>店铺分类</td>
                                    <td>工业品</td>
                                    <td>开店时间</td>
                                    <td>1年</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label">经营类目列表</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table">
                                <tr>
                                    <th>一级类目</th>
                                    <th>二级类目</th>
                                    <th>三级类目</th>
                                    <th>分佣比例%</th>
                                </tr>
                                <tr>
                                    <td>女装</td>
                                    <td>袜子配饰</td>
                                    <td>女袜</td>
                                    <td>1</td>
                                </tr>
                                <tr>
                                    <td>生活家电</td>
                                    <td>生活家电</td>
                                    <td>吸尘器</td>
                                    <td>5</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <a href="#">
                            <button id="submitBtn" class="layui-btn btn" lay-submit lay-filter="demo1">下一步</button>
                         </a>     
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="bug"></div>
<script>
   layui.use(function () {
        var form = layui.form;
        var layer = layui.layer;
        form.on('submit(demo1)', function (data) { 
            var field = data.field; 
            console.log("field", field);
            return false; 
        });
    }); 
</script>
