﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>商家物料</summary>
public partial interface IMerchantMaterial
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>店铺ID</summary>
    Int64 StoreId { get; set; }

    /// <summary>名称</summary>
    String Name { get; set; }

    /// <summary>总数量</summary>
    Int32 Quantity { get; set; }

    /// <summary>暂扣数量</summary>
    Int32 TemporaryQuantity { get; set; }

    /// <summary>是否启用。默认启用</summary>
    Boolean Enabled { get; set; }

    /// <summary>状态 0:未审核,1:已审核 2:审核不通过</summary>
    Int32 Status { get; set; }

    /// <summary>上传图片</summary>
    String? Images { get; set; }

    /// <summary>审核原因</summary>
    String? Cause { get; set; }

    /// <summary>审核者</summary>
    Int32 Auditor { get; set; }

    /// <summary>审核时间</summary>
    Int64 AuditTime { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>重量</summary>
    Decimal Weight { get; set; }

    /// <summary>体积</summary>
    Decimal Volume { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
