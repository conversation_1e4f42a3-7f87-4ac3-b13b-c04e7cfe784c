@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/modulesSearch.css");
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }
</style>
<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <a href="/ProductClass">@T("产品分类")</a>
            <div>></div>
            <a class="textSelect" href="/Products/Index?page=1&limit=3&cId=@Model.cId">
                @Model.Entity.Name
            </a>
        </div>
        <!-- 正片开始 -->
        <div class="filterBox">
            <!-- 搜索盒子 -->
            <div class="inputBox flex layui-form" style="padding: 1vw;">
                <input type="text" class="layui-input" style="margin-right: 20px;width: 270px;"
                    placeholder="@T("请输入物料编号")/@T("关键字")" id="goodskey" value="@Model.key">
                <!-- <input type="text" class="layui-input" style="margin-right: auto;" placeholder="所有制造商"> -->
                <select class="select" name="store" id="store">
                    <option value="-1">@T("所有制造商")</option>
                    @foreach (Store item in Model.Storelist)
                    {
                        <option value="@item.Id" selected="@(Model.storeId == item.Id)">@item.CompanyName</option>
                    }
                </select>
                <button style="margin-right: auto;" class="button button_blue"
                    onclick="queryGoodsList()">@T("查询")</button>
                <div class="checkBox_box flex" style="padding: .5vw 10px 20px 20px;">
                    <div class="flex textOver">
                        <input type="checkbox" name="" id="radio1" checked="@Model.inventory">
                        <label for="radio1" style="margin: -4px 5px;">@T("库存量")</label>
                    </div>
                    <div class="flex textOver">
                        <input type="checkbox" name="" id="radio2" checked="@Model.onSaleint">
                        <label for="radio2" style="margin: -4px 5px;">@T("在售商品")</label>
                    </div>
                    <div class="flex textOver">
                        <input type="checkbox" name="" id="radio3" checked="@Model.roSh">
                        <label for="radio3" style="margin: -4px 5px;">@T("符合RoSH")</label>
                    </div>
                    <div class="flex textOver">
                        <input type="checkbox" name="" id="radio4" checked="@Model.newGoods">
                        <label for="radio4" style="margin: -4px 5px;">@T("新产品")</label>
                    </div>
                </div>
            </div>
            <!-- 勾选盒子 -->
            @*    <div class="checkBox_box flex" style="padding: .5vw 10px 20px 20px;">
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio1">
                    <label for="radio1" style="margin: -4px 5px;">@T("库存量")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio2">
                    <label for="radio2" style="margin: -4px 5px;">@T("在售商品")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio3">
                    <label for="radio3" style="margin: -4px 5px;">@T("符合RoSH")</label>
                </div>
                <div class="flex textOver">
                    <input type="checkbox" name="" id="radio4">
                    <label for="radio4" style="margin: -4px 5px;">@T("新产品")</label>
                </div>
            </div> *@
            <!-- 精确筛选盒子 -->
            <div class="precisionBox">
                @if (Model.ClassAttributesList.Count > 0)
                {
                    <div style="width: 100%;">
                        <button class="button button_blue" onclick="resetAllSelects()" style="padding: 0vw 0.5vw;">
                            @T("重置所有")
                        </button>
                    </div>
                    <div class="container" id="container" style="overflow: auto;background-color: white;">
                        @foreach (ClassAttributes item in Model.ClassAttributesList)
                        {
                            var selectedClassValue = (Model.goodsClassArrs as List<CommonDto>)?.FirstOrDefault(x => x.id ==
                            item.MappingField)?.value;
                            var attrlist = GoodsClassExAttributes.FindAllByClassIdAndFlied(item.ClassId, item.MappingField);
                            <div class="contentBox">
                                <div class="filterName filterName2" title="@item.Name">@item.Name</div>
                                <div class="_content layui-form">
                                    <select class="paramterBox" lay-search id="@item.MappingField">
                                        <option value="">@T("请选择")</option>
                                        @foreach (var attr in attrlist)
                                        {

                                            <option selected='@(selectedClassValue == attr)'>@attr</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- table -->
                <div style="background-color: white;">
                    <table class="layui-table" lay-skin="line" style="background-color: white;">
                        <colgroup>
                            <col width="150">
                            <col width="100">
                            <col width="170">
                            <col width="150">
                            <col width="200">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>@T("型号")/@T("品牌")/@T("封装")</th>
                                <th>@T("库存")</th>
                                <th>@T("阶梯价格")</th>
                                <th>@T("货期")</th>
                                <th>@T("操作")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (GoodsDto item in Model.GoodsList)
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo">
                                            <div class="flex"
                                            @* width: 50%; *@
                                                style="flex-direction: column;min-width: fit-content;">
                                                <a href="@Url.Action("Index", "Goods", new { skuId = item.SkuId })">
                                                    <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")" style="width: 100%;margin-top: auto;">
                                                </a>
                                                @if (item.IsWish)
                                                {
                                                    <div style="margin-top: auto;cursor: pointer;" id="wish_@(item.SkuId)"
                                                        onclick="delWishlist('@item.SkuId') ">
                                                        <i class="iconfont icon-aixin" style="color: var(--blue-deep)"></i>
                                                        @T("取消心愿单")
                                                    </div>
                                                }
                                                else
                                                {

                                                    <div style="margin-top: auto;cursor: pointer;" id="wish_@(item.SkuId)"
                                                        onclick="addWishlist('@item.SkuId')">
                                                        <i class="iconfont icon-aixin2" style="color: var(--blue-deep)"></i>
                                                        @T("加入心愿单")
                                                    </div>
                                                }

                                            </div>
                                            <div style="width: 60%;margin-left: 10%;">
                                                <a href="@Url.Action("Index", "Goods", new { skuId = item.SkuId })">
                                                    <p class="every"> <span class="name textOver">@item.Name</span> </p>
                                                    <p class="every"> 
                                                      <span class="name2">@T("分类")：</span>
                                                       <span class="name textOver">@Model.Entity.Name</span>
                                                    </p>
                                                    <p class="every">
                                                      <span class="name2">@T("制造商"): </span>
                                                      <span class="name textOver"></span>
                                                    </p>
                                                    <p class="every">
                                                      <span class="name2">@T("封装"): </span>
                                                    </p>
                                                    <p class="every">
                                                      <span class="name2">@T("描述"): </span>
                                                       <span class="name textOver">@item.AdvWord</span>
                                                    </p>
                                                </a>
 @*                                                <div class="pointer" style="padding-top: 1vw;">
                                                    <i class="iconfont icon-pdf" style="color: var(--blue-deep)"></i>
                                                    @T("规格书")
                                                </div> *@
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @foreach (WareHouseMaterialDto temp in item.WareHouseMaterials)
                                        {
                                            <div class="textOver">@temp.WareHouseName: @temp.Quantity </div>
                                        }

                                    </td>
                                    <td>
                                        @foreach (GoodsTieredPrice prices in item.GoodsTieredPrices)
                                        {
                                            if (prices.MaxQuantity == 0)
                                            {
                                                <div>@prices.MinQuantity+: @<EMAIL> </div>
                                            }
                                            else
                                            {
                                                <div>@prices.MinQuantity+: @<EMAIL> </div>
                                            }
                                        }
                                    </td>
                                    <td>
                                        @if (item.WareHouseMaterials.Count > 0 && item.WareHouseMaterials.Sum(e => e.Quantity) > 0)
                                        {
                                            <div class="layui-form" style="">
                                                <div class="layui-bg-gray" style="width:50%">@T("仓库现货")</div>
                                                <div>@T("现在下单")</div>
                                                <div class="textOver">@item.DeliveryTime</div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="layui-form" style="">
                                                <div class="layui-bg-gray" style="width:50%">@T("需订货")</div>
                                                <div class="textOver">@item.DeliveryDate</div>
                                            </div>
                                        }
                                    </td>
                                    <td>
                                        <div>@T("合计")：<span class="money" id="@item.SkuId">@(symbolLeft + 0)</span> </div>
                                        <div class="layui-form-item">
                                            <div class="layui-input-group">
                                                
                                                @if (item.WareHouseMaterials.Count > 0 && item.WareHouseMaterials.Sum(e => e.Quantity) > 0)
                                                {
                                                    <input type="number" class="layui-input @item.SkuId" style="max-width: 100px;"
                                                           min="1" data-skuid="@item.SkuId" data-gid="@item.Id" data-price="@item.GoodsPrice" 
                                                           data-tiecount="@item.GoodsTieredPrices.Count">

                                                    <div class="layui-input-split layui-input-suffix bgSelect"
                                                         style="cursor: pointer;" onclick="addCart('@item.SkuId')">
                                                        @T("购物车")
                                                    </div>
                                                }
                                                else
                                                {
                                                    <input type="number" class="layui-input @item.Id" style="max-width: 100px;" data-tiecount="@item.GoodsTieredPrices.Count" style="cursor: pointer; opacity:0.5" disabled>

                                                    <div class="layui-input-split layui-input-suffix bgSelect" style="cursor: pointer; opacity:0.5" disabled>
                                                        @T("购物车")
                                                    </div>
                                                }
                                                
                                            </div>
                                        </div>
                                        @*                                         <div class="flex" style="justify-content: left;">
                                            <div>@T("起订量"):1000</div>
                                            <div style="margin-left: 1vw;">@T("增 量"):1000</div>
                                        </div> *@
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
            <div id="pagingBox"></div>
        </div>
        <script>
            function select(dom) {
                const type = dom.getAttribute("data-type");
                const parentDom = dom.parentNode;
                $(parentDom).children().attr('class', '')
                // console.log(parentDom,$(parentDom));
                if (type == 0) {
                    dom.className = "bgSelect";
                } else if (type == 1) {
                    dom.className = "bgSelect";
                } else if (type == 2) {
                    dom.className = "bgSelect";
                } else if (type == 3) {
                    dom.className = "bgSelect";
                }
            }
            layui.use(function () {
                var laypage = layui.laypage;
                laypage.render({
                    elem: 'pagingBox',
                    count: @Model.Total, // 数据总数
                    limit: @(Model.limit > 0 ? Model.limit : 10), // 每页显示条数
                    limits: [3, 5, 10, 20, 50, 100], // 每页条数的选择项
                    curr: @(Model.page > 0 ? Model.page : 1), // 当前页码
                    groups: 5, // 连续显示页码个数
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                    theme: '#2C79E8', // 自定义主题色
                    prev: '@T("上一页")',
                    next: '@T("下一页")',
                    first: '@T("首页")',
                    last: '@T("尾页")',
                    countText: ['@T("共") ', ' @T("条")'],
                    skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                    limitTemplet: function (item) {
                        return item + ' @T("条/页")';
                    },
                    jump: function (obj, first) {
                        // 首次不执行（首次加载时不跳转）
                        if (!first) {
                            var result = [];
                            if (@Model.ClassAttributesList.Count > 0) {
                                var selects = document.getElementById("container").getElementsByTagName("select");
                                for (var i = 0; i < selects.length; i++) {
                                    var selectId = selects[i].id;
                                    var selectVal = selects[i].value;
                                    if (selectVal == "") {
                                        continue;
                                    }
                                    result.push({
                                        id: selectId,
                                        value: selectVal
                                    });
                                }
                            }
                            var jsonString = JSON.stringify(result);
                            // 构造URL参数
                            var params = {
                                page: obj.curr,
                                limit: obj.limit,
                                inventory: @(Model.inventory ? "true" : "false"),
                                onSaleint: @(Model.onSaleint ? "true" : "false"),
                                roSh: @(Model.roSh ? "true" : "false"),
                                newGoods: @(Model.newGoods ? "true" : "false"),
                                cId: '@Model.cId',
                                storeId: '@Model.storeId',
                                goodskey:'@Model.key',
                                jsonKey: jsonString
                            };
                            
                            // 直接跳转URL
                            var url = '@Url.Action("Index")' + '?' + $.param(params);
                            window.location.href = url;
                        }
                    }
                });
            });
        </script>
    </div>
</body>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
    function queryGoodsList() {
        var radio1 = $("#radio1").is(":checked");
        var radio2 = $("#radio2").is(":checked");
        var radio3 = $("#radio3").is(":checked");
        var radio4 = $("#radio4").is(":checked");
        var key = $("#goodskey").val();
        var store = $("#store").val();
        var cId = '@Model.Entity.Id';
        var result = [];
        if (@Model.ClassAttributesList.Count > 0) {
            var selects = document.getElementById("container").getElementsByTagName("select");
            for (var i = 0; i < selects.length; i++) {
                var selectId = selects[i].id;
                var selectVal = selects[i].value;
                if (selectVal == "") {
                    continue;
                }
                result.push({
                    id: selectId,
                    value: selectVal
                });
            }
        }
        var jsonString = JSON.stringify(result);
        // 构造查询字符串
        var queryParams = {
            cId: cId,
            goodskey: key,
            inventory: radio1,
            onSaleint: radio2,
            roSh: radio3,
            newGoods: radio4,
            jsonKey: jsonString,
            storeId: store,
            page: 1,
            limit: 3
        };
        // 将查询参数对象转换为查询字符串
        var queryString = Object.keys(queryParams).map(function (key) {
            return encodeURIComponent(key) + '=' + encodeURIComponent(queryParams[key]);
        }).join('&');

        window.location.href = '/Products/Index' + '?' + queryString;

    }


    @* 加入心愿清单 *@
        function addWishlist(id) {
            $.post('@Url.Action("AddWishlist", "CubeHome")', { skuId: id }, function (res) {
                layui.layer.msg(res.msg);
                if (res.success) {
                    const wishlistBtn = document.querySelector("#wish_"+id);
                    wishlistBtn.innerHTML = `<i class="iconfont icon-aixin pointer" style="color: var(--blue-deep);"></i> @T("取消心愿单")`;
                    wishlistBtn.onclick = function() {
                        delWishlist(id);
                    };
                }
            })
        }
    @* 取消心愿清单 *@
        function delWishlist(id) {
            $.post('@Url.Action("DelWishlist", "CubeHome")', { skuId: id }, function (res) {
                layui.layer.msg(res.msg);
                if (res.success) {
                    const wishlistBtn = document.querySelector("#wish_"+id);
                    wishlistBtn.innerHTML = `<i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i> @T("加入心愿单")`;
                    wishlistBtn.onclick = function() {
                        addWishlist(id);
                    };
                }
            })
        }

    //加入购物车
    function addCart(skuId) {


        var GoodsNum = parseInt($("." + skuId).val());

        $.post("@Url.Action("AddCart", "CubeHome")", { skuId, GoodsNum }, function (res) {
            layui.layer.msg(res.msg);
            if (res.success) {
                // 如果添加成功，获取当前购物车数量
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(res.data);
                }
            }
        })
    }
    //选择SKU加入购物车
    function OpenSkuInfo(GoodsId) {
        var GoodsNum = parseInt($("." + GoodsId).val());
        if (isNaN(GoodsNum) || GoodsNum <= 0) {
            layui.layer.msg('@T("商品数量必须大于0")');
            return;
        }
        layui.layer.open({
            type: 2, // iframe
            title: '@T("选择商品属性")',
            area: ['700px', '400px'],
            shadeClose: true,
            content: '/CubeHome/SkuSelector?goodsId=' + GoodsId + '&num=' + GoodsNum,
        });
    }
    ///获取弹窗参数更新购物车
    function getChildrenData(res) {
        if (res && res.success && typeof window.updateCartCount === 'function') {
            window.updateCartCount(res.data); // 更新购物车数量
        }
    }
    $("input[type='number']").on('input', function () {
        var quantity = parseInt($(this).val());

        var goodsprice = $(this).data("price");
        var skuId = $(this).data("skuid");
        var goodsId = $(this).data("gid");
        var tieCount = parseInt($(this).data("tiecount"), 10);
        var pricePerItem = parseFloat(goodsprice);

        if (tieCount > 1) {
            $.post('@Url.Action("GetTreTieredPrices", "Goods")', { GoodsId: goodsId,SkuId:skuId, number: quantity }, function (res) {
                if (res.success) {
                    if (res.data.Price != 0) {
                        pricePerItem = res.data.Price;
                        // 检查是否为NaN
                        if (!isNaN(quantity) && !isNaN(pricePerItem)) {
                            var totalAmount = quantity * pricePerItem;
                            $("#" + skuId).text('@symbolLeft' + totalAmount.toFixed(2));
                        } else {
                            // 如果有NaN，重置显示或显示错误信息
                            $("#" + skuId).text('@symbolLeft 0');
                        }
                    }
                } else {
                    // 检查是否为NaN
                    if (!isNaN(quantity) && !isNaN(pricePerItem)) {
                        var totalAmount = quantity * pricePerItem;
                        $("#" + skuId).text('@symbolLeft' + totalAmount.toFixed(2));
                    } else {
                        // 如果有NaN，重置显示或显示错误信息
                        $("#" + skuId).text('@symbolLeft+‘0');
                    }
                }
            });
        } else {
            // 检查是否为NaN
            if (!isNaN(quantity) && !isNaN(pricePerItem)) {
                var totalAmount = quantity * pricePerItem;
                $("#" + skuId).text('@symbolLeft' + totalAmount.toFixed(2));
            } else {
                // 如果有NaN，重置显示或显示错误信息
                $("#" + skuId).text('@symbolLeft'+'0');
            }
        }
    })
    function resetAllSelects() {
        var selects = document.getElementById("container").getElementsByTagName("select");
        for (var i = 0; i < selects.length; i++) {
            selects[i].value = "@T("请选择")";
        }
        // 重新渲染 layui 表单
        layui.form.render('select');
    }
    layui.use(['form'], function () {
        var form = layui.form;
        // 监听所有复选框的选中/取消事件
        form.on('checkbox', function (data) {
            queryGoodsList();
        });
        // 监听搜索框回车事件
        $('#goodskey').on('keydown', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault(); // 阻止表单默认提交
                queryGoodsList();
            }
        });
        // 页面加载完后给文本框设置焦点并将光标移到最后
        var $input = $('#goodskey');
        $input.focus();
        var val = $input.val();
        if (val && $input[0].setSelectionRange) {
            $input[0].setSelectionRange(val.length, val.length);
        }
        // 监听 select 选择事件（适用于所有 select）
        form.on('select', function (data) {
            // data.elem 是 select 原生DOM
            // data.value 是选中的值
            // data.othis 是美化后的DOM
            queryGoodsList();
        });
    });
</script>
