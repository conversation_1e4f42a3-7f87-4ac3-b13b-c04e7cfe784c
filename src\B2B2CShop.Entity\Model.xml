﻿<?xml version="1.0" encoding="utf-8"?>
<EntityModel xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:schemaLocation="https://newlifex.com https://newlifex.com/Model202407.xsd" Version="11.3.2023.0307" Document="https://newlifex.com/xcode/model" xmlns="https://newlifex.com/Model202407.xsd">
  <Option>
    <!--类名模板。其中{name}替换为Table.Name，如{name}Model/I{name}Dto等-->
    <ClassNameTemplate />
    <!--显示名模板。其中{displayName}替换为Table.DisplayName-->
    <DisplayNameTemplate />
    <!--基类。可能包含基类和接口，其中{name}替换为Table.Name-->
    <BaseClass>DHEntityBase</BaseClass>
    <!--命名空间-->
    <Namespace>B2B2CShop.Entity</Namespace>
    <!--输出目录-->
    <Output>../B2B2CShop/Entity</Output>
    <!--是否使用中文文件名。默认false-->
    <ChineseFileName>False</ChineseFileName>
    <!--用于生成Copy函数的参数类型。例如{name}或I{name}-->
    <ModelNameForCopy />
    <!--带有索引器。实现IModel接口-->
    <HasIModel>False</HasIModel>
    <!--可为null上下文。生成String?等-->
    <Nullable>True</Nullable>
    <!--数据库连接名-->
    <ConnName>Pek</ConnName>
    <!--模型类模版。设置后生成模型类，用于接口数据传输，例如{name}Model-->
    <ModelClass>{name}Model</ModelClass>
    <!--模型类输出目录。默认当前目录的Models子目录-->
    <ModelsOutput>.\Models\</ModelsOutput>
    <!--模型接口模版。设置后生成模型接口，用于约束模型类和实体类，例如I{name}-->
    <ModelInterface>I{name}</ModelInterface>
    <!--模型接口输出目录。默认当前目录的Interfaces子目录-->
    <InterfacesOutput>.\Interfaces\</InterfacesOutput>
    <!--用户实体转为模型类的模型类。例如{name}或{name}DTO-->
    <ModelNameForToModel />
    <!--命名格式。Default/Upper/Lower/Underline-->
    <NameFormat>Default</NameFormat>
    <!--魔方区域显示名-->
    <DisplayName />
    <!--魔方控制器输出目录-->
    <CubeOutput />
  </Option>
  <Tables>
    <Table Name="UserEx" TableName="DH_UserEx" Description="用户扩展表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" PrimaryKey="True" Description="用户Id" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="Seller" TableName="DH_Seller" Description="卖家用户表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="卖家用户名" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="SellerGroupId" DataType="Int32" Description="卖家组ID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="IsAdmin" DataType="Boolean" Description="是否管理员" />
        <Column Name="LastLoginTime" DataType="DateTime" Description="最后登录时间" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" Unique="True" />
        <Index Columns="StoreId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SellerGroup" TableName="DH_SellerGroup" Description="卖家用户组表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="卖家组名称" />
        <Column Name="Limits" DataType="String" RawType="text" Length="0" Description="卖家组权限" />
        <Column Name="SmtLimits" DataType="String" RawType="text" Length="0" Description="卖家组消息权限范围" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SnsAlbumClass" TableName="DH_SnsAlbumClass" Description="相册表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="相册名称" />
        <Column Name="Des" DataType="String" Length="255" Description="相册描述" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="相册排序" />
        <Column Name="Cover" DataType="String" Length="255" Description="相册封面" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否为买家秀相册" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="StoreGrade" TableName="DH_StoreGrade" Description="店铺等级表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="店铺等级名称" />
        <Column Name="GoodsLimit" DataType="Int32" Description="允许发布商品数量" />
        <Column Name="AlbumLimit" DataType="Int32" Description="允许发布商品数量" />
        <Column Name="SpaceLimit" DataType="Int32" Description="允许上传空间大小,单位MB" />
        <Column Name="TemplateNumber" DataType="Int32" Description="店铺等级选择店铺模板套数" />
        <Column Name="Template" DataType="String" Length="255" Description="店铺等级模板内容" />
        <Column Name="Price" DataType="Int32" Description="店铺等级费用" />
        <Column Name="Confirm" DataType="Boolean" Description="店铺等级审核" />
        <Column Name="Description" DataType="String" RawType="text" Length="0" Description="店铺等级申请说明" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="店铺等级排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="PointsLog" TableName="DH_PointsLog" Description="会员积分日志表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Length="100" Description="会员名称" />
        <Column Name="AdminId" DataType="Int32" Description="管理员ID" />
        <Column Name="AdminName" DataType="String" Length="100" Description="管理员名称" />
        <Column Name="Points" DataType="Int32" Description="积分数,负数为扣除" />
        <Column Name="Desc" DataType="String" Length="100" Description="积分操作描述" />
        <Column Name="Stage" DataType="String" Description="积分操作阶段。regist注册,login登录,comments商品评论,order订单消费,system系统调整,pointorder礼品兑换,exchange积分兑换,signin签到,inviter推荐注册,rebate推荐返利" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="积分添加时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId,Stage" />
      </Indexes>
    </Table>
    <Table Name="PointsMoney" TableName="DH_PointsMoney" Description="积分金钱兑换表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Points" DataType="Int32" Description="积分" />
        <Column Name="Moneys" DataType="Decimal" Description="金额" />
        <Column Name="State" DataType="Boolean" Description="是否启用" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="State" />
        <Index Columns="Points" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="HelpsCategory" TableName="DH_HelpsCategory" Description="帮助分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Id,Level" />
        <Index Columns="Name,Level" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="HelpsCategoryLan" TableName="DH_HelpsCategoryLan" Description="帮助分类翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="HId" DataType="Int32" Description="帮助分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
      </Columns>
      <Indexes>
        <Index Columns="HId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Helps" TableName="DH_Helps" Description="帮助" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="HId" DataType="Int32" Description="帮助分类Id" />
        <Column Name="Url" DataType="String" Length="200" Description="帮助文章跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="帮助文章是否显示，0为否，1为是，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="帮助文章排序" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="帮助文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="Hits" DataType="Int32" Description="浏览次数" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="HId" />
        <Index Columns="Hits" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="HelpsLan" TableName="DH_HelpsLan" Description="帮助翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="HId" DataType="Int32" Description="帮助文章Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="帮助文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
      </Columns>
      <Indexes>
        <Index Columns="HId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="AlbumCategory" TableName="DH_AlbumCategory" Description="商品相册" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Nullable="False" Description="相册名称" />
        <Column Name="StoreId" DataType="Int64" Description="店铺Id" />
        <Column Name="Content" DataType="String" Length="255" Description="相册描述" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="相册排序" />
        <Column Name="Cover" DataType="String" Length="255" Description="相册封面" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否为默认相册" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name,StoreId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="AlbumPic" TableName="DH_AlbumPic" Description="商品相册图片" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Nullable="False" Description="图片名称" />
        <Column Name="Tag" DataType="String" Length="255" Description="图片标签" />
        <Column Name="AId" DataType="Int32" Description="相册Id" />
        <Column Name="Cover" DataType="String" Length="255" Nullable="False" Description="图片路径" />
        <Column Name="Size" DataType="Int32" Description="图片大小" />
        <Column Name="Spec" DataType="String" Length="100" Description="图片规格" />
        <Column Name="StoreId" DataType="Int64" Description="店铺Id" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="AId" />
        <Index Columns="StoreId,Cover" />
      </Indexes>
    </Table>
    <Table Name="GoodsImages" TableName="DH_GoodsImages" Description="商品图片表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="商品图片自增ID" />
        <Column Name="GoodsCommonId" DataType="Int64" Description="商品公共ID" />
        <Column Name="SkuId" DataType="Int64" Description="SKUID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="ColorId" DataType="Int64" DefaultValue="0" Description="颜色规格值ID" />
        <Column Name="ImageUrl" DataType="String" Length="1000" Nullable="False" Description="商品图片" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="商品图片排序" />
        <Column Name="IsDefault" DataType="Int32" DefaultValue="0" Description="商品图片默认主图，1是，0否" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsCommonId" />
        <Index Columns="SkuId" />
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="GoodsImagesLan" TableName="DH_GoodsImagesLan" Description="商品图片翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="商品图片翻译自增ID" />
        <Column Name="LId" DataType="Int32" Description="所属语言ID" />
        <Column Name="GoodsCommonId" DataType="Int64" Description="商品公共ID" />
        <Column Name="SkuId" DataType="Int64" Description="SKUID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="ColorId" DataType="Int64" DefaultValue="0" Description="颜色规格值ID" />
        <Column Name="ImageUrl" DataType="String" Length="1000" Nullable="False" Description="商品图片" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="商品图片排序" />
        <Column Name="IsDefault" DataType="Int32" DefaultValue="0" Description="商品图片默认主图，1是，0否" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsCommonId,LId" />
        <Index Columns="SkuId,LId" />
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="Store" TableName="DH_Store" Description="店铺数据表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Nullable="False" Description="店铺名称" />
        <Column Name="GradeId" DataType="Int32" Description="店铺等级ID" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Description="会员用户名" />
        <Column Name="SellerName" DataType="String" Nullable="False" Description="店主卖家用户名" />
        <Column Name="StoreClassId" DataType="Int32" Description="店铺分类ID" />
        <Column Name="CompanyName" DataType="String" Description="店铺公司名称" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="CountryCode" DataType="String" Length="2" Description="所在国家两字母ISO代码" />
        <Column Name="RegionId" DataType="Int32" Description="地区ID" />
        <Column Name="RegionCode" DataType="String" Description="地区代码" />
        <Column Name="AreaInfo" DataType="String" Length="100" Description="地区名称" />
        <Column Name="Address" DataType="String" Length="100" Description="店铺地址" />
        <Column Name="Zip" DataType="String" Length="100" Description="邮政编码" />
        <Column Name="State" DataType="Int16" DefaultValue="2" Description="店铺状态:0关闭，1开启，2审核中" />
        <Column Name="CloseInfo" DataType="Int32" DefaultValue="0" Description="店铺关闭原因" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="店铺排序" />
        <Column Name="AddTime" DataType="Int64" Description="店铺时间" />
        <Column Name="EndTime" DataType="Int64" Description="店铺关闭时间" />
        <Column Name="Logo" DataType="String" Length="255" Description="店铺LOGO" />
        <Column Name="Banner" DataType="String" Length="255" Description="店铺Banner" />
        <Column Name="Avatar" DataType="String" Length="255" Description="店铺头像" />
        <Column Name="PyAbbr" DataType="String" Description="店铺拼音简写" />
        <Column Name="Keywords" DataType="String" Length="255" Description="店铺SEO关键字" />
        <Column Name="Description" DataType="String" Length="255" Description="店铺SEO描述" />
        <Column Name="QQ" DataType="String" Length="255" Description="店铺QQ" />
        <Column Name="WX" DataType="String" Length="255" Description="店铺微信" />
        <Column Name="Skype" DataType="String" Length="255" Description="店铺Skype" />
        <Column Name="WhatsApp" DataType="String" Length="255" Description="店铺WhatsApp" />
        <Column Name="Phone" DataType="String" Length="255" Description="商家电话" />
        <Column Name="MainBusiness" DataType="String" Length="255" Description="主营商品" />
        <Column Name="Recommend" DataType="Boolean" Description="是否推荐" />
        <Column Name="Theme" DataType="String" Description="店铺当前主题" />
        <Column Name="Credit" DataType="Int32" DefaultValue="100" Description="店铺信用" />
        <Column Name="DescCredit" DataType="Double" DefaultValue="5" Description="描述相符度分数" />
        <Column Name="ServiceCredit" DataType="Double" DefaultValue="5" Description="服务态度分数" />
        <Column Name="DeliveryCredit" DataType="Double" DefaultValue="5" Description="发货速度分数" />
        <Column Name="Collect" DataType="Int32" Description="店铺收藏数量" />
        <Column Name="Slide" DataType="String" RawType="text" Length="0" Description="店铺幻灯片" />
        <Column Name="SlideUrl" DataType="String" RawType="text" Length="0" Description="店铺幻灯片链接" />
        <Column Name="Seal" DataType="String" Length="255" Description="店铺印章" />
        <Column Name="PrintExplain" DataType="String" Length="500" Description="打印订单页面下方说明文字" />
        <Column Name="Sales" DataType="Int32" Description="店铺销量" />
        <Column Name="PreSales" DataType="String" RawType="text" Length="0" Description="售前客服" />
        <Column Name="AfterSales" DataType="String" RawType="text" Length="0" Description="售后客服" />
        <Column Name="WorkingTime" DataType="String" Length="100" Description="工作时间" />
        <Column Name="FreePrice" DataType="Decimal" DefaultValue="0.00" Description="超出该金额免运费，大于0才表示该值有效" />
        <Column Name="DecorationSwitch" DataType="Boolean" Description="店铺装修开关" />
        <Column Name="DecorationOnly" DataType="Boolean" Description="开启店铺装修时，仅显示店铺装修" />
        <Column Name="DecorationImageCount" DataType="Int32" Description="店铺装修相册图片数量" />
        <Column Name="PlatformStore" DataType="Boolean" Description="是否自营店铺" />
        <Column Name="BindAllGc" DataType="Boolean" Description="自营店是否绑定全部分类" />
        <Column Name="VrCodePrefix" DataType="String" Length="3" Description="商家兑换码前缀" />
        <Column Name="BaoZh" DataType="Boolean" Description="保证服务开关" />
        <Column Name="QTian" DataType="Boolean" Description="7天退换" />
        <Column Name="ZhPing" DataType="Boolean" Description="正品保障" />
        <Column Name="ErXiaoShi" DataType="Boolean" Description="两小时发货" />
        <Column Name="TuiHuo" DataType="Boolean" Description="退货承诺" />
        <Column Name="ShiYong" DataType="Boolean" Description="试用中心" />
        <Column Name="ShiTi" DataType="Boolean" Description="实体验证" />
        <Column Name="XiaoXie" DataType="Boolean" Description="消协保证" />
        <Column Name="HuoDaoFK" DataType="Boolean" Description="货到付款" />
        <Column Name="FreeTime" DataType="String" Description="商家配送时间" />
        <Column Name="Longitude" DataType="String" Length="20" Description="经度" />
        <Column Name="Latitude" DataType="String" Length="20" Description="纬度" />
        <Column Name="MbTitleImg" DataType="String" Length="150" Description="手机店铺背景图" />
        <Column Name="MbSliders" DataType="String" RawType="text" Length="0" Description="手机店铺轮播图" />
        <Column Name="DeliverRegion" DataType="String" Description="店铺默认配送区域" />
        <Column Name="MgDiscount" DataType="String" RawType="text" Length="0" Description="序列化会员等级折扣(店铺)" />
        <Column Name="MgDiscountState" DataType="Boolean" Description="店铺是否开启序列化会员等级折扣" />
        <Column Name="AvaliableDeposit" DataType="Decimal" DefaultValue="0.00" Description="店铺已缴保证金" />
        <Column Name="FreezeDeposit" DataType="Decimal" DefaultValue="0.00" Description="店铺审核保证金" />
        <Column Name="PayableDeposit" DataType="Decimal" DefaultValue="0.00" Description="店铺应缴保证金" />
        <Column Name="AvaliableMoney" DataType="Decimal" DefaultValue="0.00" Description="店铺可用金额" />
        <Column Name="FreezeMoney" DataType="Decimal" DefaultValue="0.00" Description="店铺冻结金额" />
        <Column Name="KdnIfOpen" DataType="Boolean" Description="快递鸟开启" />
        <Column Name="KdnId" DataType="String" Length="20" Description="快递鸟-用户ID" />
        <Column Name="KdnKey" DataType="String" Length="20" Description="快递鸟-API key" />
        <Column Name="KdnPrinter" DataType="String" Length="20" Description="快递鸟-打印机" />
        <Column Name="TrackIfOpen" DataType="Boolean" Description="17Track开启" />
        <Column Name="TrackId" DataType="String" Description="17Track-用户ID" />
        <Column Name="TrackKey" DataType="String" Description="17Track-API key" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SellerName" />
        <Index Columns="CountryCode" />
        <Index Columns="RegionCode" />
        <Index Columns="PyAbbr" />
      </Indexes>
    </Table>
    <Table Name="StoreExtend" TableName="DH_StoreExtend" Description="店铺信息扩展表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="PriceRange" DataType="String" RawType="text" Length="0" Description="店铺统计设置的商品价格区间" />
        <Column Name="OrderPriceRange" DataType="String" RawType="text" Length="0" Description="店铺统计设置的订单价格区间" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="StoreJoinIn" TableName="DH_StoreJoinIn" Description="店铺入住表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Description="会员用户名" />
        <Column Name="Type" DataType="Int16" Description="店铺类型:0公司，1个人" />
        <Column Name="CompanyName" DataType="String" Description="公司名称" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="CountryCode" DataType="String" Length="2" Description="所在国家两字母ISO代码" />
        <Column Name="ProvinceId" DataType="Int32" Description="所在地省ID" />
        <Column Name="ProvinceCode" DataType="String" Description="地区代码" />
        <Column Name="CityId" DataType="Int32" Description="所在地市ID" />
        <Column Name="CountyId" DataType="Int32" Description="所在地县/区ID" />
        <Column Name="Address" DataType="String" Length="500" Description="公司地址" />
        <Column Name="AddressDetail" DataType="String" Description="公司详细地址" />
        <Column Name="RegisteredCapital" DataType="Decimal" Description="注册资金" />
        <Column Name="ContactsName" DataType="String" Description="联系人姓名" />
        <Column Name="ContactsPhone" DataType="String" Description="联系人电话" />
        <Column Name="ContactsEmail" DataType="String" Description="联系人邮箱" />
        <Column Name="PersonalIdentityNumber" DataType="String" Description="个人证件号" />
        <Column Name="BusinessLicenceNumber" DataType="String" Description="营业执照号" />
        <Column Name="BusinessLicenceProvinceId" DataType="Int32" Description="营业执所在省ID" />
        <Column Name="BusinessLicenceCityId" DataType="Int32" Description="营业执所在市ID" />
        <Column Name="BusinessLicenceCountyId" DataType="Int32" Description="营业执所在县/区ID" />
        <Column Name="BusinessLicenceAddress" DataType="String" Length="500" Description="营业执所在地" />
        <Column Name="BusinessLicenceStart" DataType="DateTime" Description="营业执照有效期开始" />
        <Column Name="BusinessLicenceEnd" DataType="DateTime" Description="营业执照有效期结束" />
        <Column Name="BusinessSphere" DataType="String" Length="1000" Description="法定经营范围" />
        <Column Name="BusinessLicenceNumberElectronic" DataType="String" Description="营业执照电子版" />
        <Column Name="BankAccountName" DataType="String" Description="银行开户名" />
        <Column Name="BankAccountNumber" DataType="String" Description="公司银行账号" />
        <Column Name="BankName" DataType="String" Description="开户银行支行名称" />
        <Column Name="BankAddress" DataType="String" Description="开户银行所在地" />
        <Column Name="IsSettlementAccount" DataType="Int16" Description="开户行账号是否为结算账号 1-开户行就是结算账号 2-独立的计算账号" />
        <Column Name="SettlementBankAccountName" DataType="String" Description="结算银行开户名" />
        <Column Name="SettlementBankAccountNumber" DataType="String" Description="结算公司银行账号" />
        <Column Name="SettlementBankName" DataType="String" Description="结算开户银行支行名称" />
        <Column Name="SettlementBankAddress" DataType="String" Description="结算开户银行所在地" />
        <Column Name="SellerName" DataType="String" Description="卖家帐号" />
        <Column Name="Name" DataType="String" Master="True" Description="店铺名称" />
        <Column Name="ClassIds" DataType="String" Length="1000" Description="店铺分类编号集合" />
        <Column Name="ClassNames" DataType="String" Length="1000" Description="店铺分类名称集合" />
        <Column Name="Longitude" DataType="String" Length="20" Description="经度" />
        <Column Name="Latitude" DataType="String" Length="20" Description="纬度" />
        <Column Name="State" DataType="Int16" Description="申请状态 10-已提交申请 11-缴费完成  20-审核成功 30-审核失败 31-缴费审核失败 40-审核通过开店" />
        <Column Name="Message" DataType="String" Length="200" Description="管理员审核信息" />
        <Column Name="Year" DataType="Int32" Description="开店时长(年)" />
        <Column Name="StoreGradeName" DataType="String" Description="店铺等级名称" />
        <Column Name="StoreGradeId" DataType="String" Description="店铺等级编号" />
        <Column Name="SgInfo" DataType="String" Length="200" Description="店铺等级下的收费等信息" />
        <Column Name="StoreClassName" DataType="String" Description="店铺分类名称" />
        <Column Name="StoreClassId" DataType="String" Description="店铺分类编号" />
        <Column Name="StoreclassBail" ColumnName="StoreClass_Bail" DataType="Int32" Description="店铺分类保证金" />
        <Column Name="StoreClassCommisRates" DataType="String" Length="200" Description="分类佣金比例" />
        <Column Name="PayingMoneyCertificate" DataType="String" Description="付款凭证" />
        <Column Name="PayingMoneyCertificateExplain" DataType="String" Length="200" Description="付款凭证说明" />
        <Column Name="PayingAmount" DataType="Decimal" Description="付款金额" />
        <Column Name="PaySn" DataType="String" Length="20" Description="支付单号" />
        <Column Name="PaymentCode" DataType="String" Length="20" Description="支付方式" />
        <Column Name="TradeSn" DataType="String" Description="第三方支付接口交易号" />
        <Column Name="RcbAmount" ColumnName="RCB_Amount" DataType="Decimal" Description="充值卡支付金额" />
        <Column Name="PDAmount" DataType="Decimal" Description="预存款支付金额" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="ZoneTime" TableName="DH_ZoneTime" Description="时区表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Times" DataType="String" Description="时区。相对于UTC0" />
        <Column Name="CreateTime" DataType="Int64" Description="创建时间" />
      </Columns>
    </Table>
    <Table Name="SingleArticle" TableName="DH_SingleArticle" Description="单页文章" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Url" DataType="String" Length="200" Description="文章跳转链接" />
        <Column Name="Show" DataType="Boolean" Description="文章是否显示。默认为是" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="文章排序" />
        <Column Name="Code" DataType="String" Nullable="False" Description="调用别名。不可重复" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="内容(移动端)" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="ViewName" DataType="String" Length="100" Description="模板名称" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
        <Column Name="Hits" DataType="Int32" Description="查看次数" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="Int64" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="Int64" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Code" Unique="True" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SingleArticleLan" TableName="DH_SingleArticleLan" Description="单页文章翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SId" DataType="Int32" Description="单页文章Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="内容(移动端)" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
      </Columns>
      <Indexes>
        <Index Columns="SId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="OtherMsgTpl" TableName="DH_OtherMsgTpl" Description="其他消息模板" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="MName" DataType="String" Master="True" Description="模板名称" />
        <Column Name="MTitle" DataType="String" Length="100" Description="模板标题" />
        <Column Name="MCode" DataType="String" Master="True" Description="模板调用代码" />
        <Column Name="MContent" DataType="String" RawType="text" Length="0" Description="模板内容" />
        <Column Name="SmsTplId" DataType="String" Description="短信模板Id" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MCode" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="OtherMsgTplLan" TableName="DH_OtherMsgTplLan" Description="其他消息模板翻译表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="OId" DataType="Int32" Description="关联其他消息模板Id" />
        <Column Name="LId" DataType="Int32" Description="关联所属语言Id" />
        <Column Name="MName" DataType="String" Master="True" Description="模板名称" />
        <Column Name="MTitle" DataType="String" Length="100" Description="模板标题" />
        <Column Name="MContent" DataType="String" RawType="text" Length="0" Description="模板内容" />
        <Column Name="SmsTplId" DataType="String" Description="短信模板Id" />
      </Columns>
      <Indexes>
        <Index Columns="OId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Brand" TableName="DH_Brand" Description="品牌表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="品牌名称" />
        <Column Name="Initial" DataType="String" Length="1" Description="品牌首字母" />
        <Column Name="Pic" DataType="String" Length="100" Description="品牌图片" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="GoodsClassId" DataType="Int64" Description="商品分类ID" />
        <Column Name="BClass" DataType="String" Description="类别名称" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="品牌排序" />
        <Column Name="Recommend" DataType="Boolean" Description="品牌推荐" />
        <Column Name="Apply" DataType="Int16" Description="品牌申请，0为申请中，1为通过，默认为1，申请功能是会员使用" />
        <Column Name="ShowType" DataType="Int16" Description="品牌展示类型 0表示图片 1表示文字" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="GoodsClassId" />
      </Indexes>
    </Table>
    <Table Name="GoodType" TableName="DH_GoodType" Description="商品类型表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="类型名称" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="类型排序" />
        <Column Name="GoodsClassId" DataType="Int64" Description="商品分类ID" />
        <Column Name="GoodsClassName" DataType="String" Length="100" Description="商品分类名称" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsClassId" />
      </Indexes>
    </Table>
    <Table Name="GoodTypeBrand" TableName="DH_GoodTypeBrand" Description="商品类型与品牌对应表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GoodTypeId" DataType="Int32" Description="类型编号" />
        <Column Name="BrandId" DataType="Int32" Description="品牌编号" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodTypeId" />
      </Indexes>
    </Table>
    <Table Name="GoodTypeSpec" TableName="DH_GoodTypeSpec" Description="商品类型与规格对应表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GoodTypeId" DataType="Int32" Description="类型编号" />
        <Column Name="GoodSpecId" DataType="Int32" Description="规格编号" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodTypeId" />
      </Indexes>
    </Table>
    <Table Name="GoodAttribute" TableName="DH_GoodAttribute" Description="商品属性表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="属性名称" />
        <Column Name="GoodTypeId" DataType="Int32" Description="所属类型编号" />
        <Column Name="AttrValue" DataType="String" RawType="text" Length="0" Description="属性值" />
        <Column Name="Show" DataType="Boolean" Description="属性是否显示" />
        <Column Name="Sort" DataType="Int32" Description="属性排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodTypeId" />
      </Indexes>
    </Table>
    <Table Name="GoodAttributeValue" TableName="DH_GoodAttributeValue" Description="商品属性值表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="属性值名称" />
        <Column Name="GoodAttributeId" DataType="Int32" Description="所属属性编号" />
        <Column Name="GoodTypeId" DataType="Int32" Description="所属类型编号" />
        <Column Name="Sort" DataType="Int32" Description="属性值排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodAttributeId" />
        <Index Columns="GoodTypeId" />
      </Indexes>
    </Table>
    <Table Name="GoodSpec" TableName="DH_GoodSpec" Description="商品规格表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="规格名称" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="规格排序" />
        <Column Name="GoodsClassId" DataType="Int64" Description="商品分类ID" />
        <Column Name="GoodsClassName" DataType="String" Length="100" Description="商品分类名称" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsClassId" />
      </Indexes>
    </Table>
    <Table Name="GoodsClass" TableName="DH_GoodsClass" Description="商品分类表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="商品分类名称" />
        <Column Name="Image" DataType="String" Length="100" Description="商品分类图" />
        <Column Name="GoodTypeId" DataType="Int32" Description="所属类型编号" />
        <Column Name="ParentId" DataType="Int64" Description="商品分类上级ID" />
        <Column Name="CommisRate" DataType="Double" Description="商品分类佣金比例" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="商品分类排序" />
        <Column Name="IsVirtual" DataType="Boolean" Description="是否允许发布虚拟商品" />
        <Column Name="Title" DataType="String" Length="200" Description="SEO商品分类名称" />
        <Column Name="Keywords" DataType="String" Length="255" Description="SEO商品分类关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO商品分类描述" />
        <Column Name="Show" DataType="Boolean" Description="商品分类前台显示" />
        <Column Name="AliasName" DataType="String" Length="100" Description="商品分类别名" />
        <Column Name="ClassIds" DataType="String" Length="200" Description="推荐子级分类" />
        <Column Name="BrandIds" DataType="String" Length="200" Description="推荐的品牌" />
        <Column Name="Pic" DataType="String" Length="100" Description="分类图片" />
        <Column Name="Adv1" DataType="String" Length="100" Description="广告图1" />
        <Column Name="Adv1Link" DataType="String" Length="100" Description="广告1链接" />
        <Column Name="Adv2" DataType="String" Length="100" Description="广告图2" />
        <Column Name="Adv2Link" DataType="String" Length="100" Description="广告2链接" />
        <Column Name="Enable" DataType="Boolean" DefaultValue="True" Description="是否启用1启用 0禁用" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="GoodsClassLan" TableName="DH_GoodsClassLan" Description="商品分类翻译表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int64" Description="关联商品分类Id" />
        <Column Name="LId" DataType="Int32" Description="关联所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="商品分类名称" />
        <Column Name="AliasName" DataType="String" Length="100" Description="商品分类别名" />
        <Column Name="Image" DataType="String" Length="100" Description="商品分类图" />
        <Column Name="Title" DataType="String" Length="200" Description="SEO商品分类名称" />
        <Column Name="Keywords" DataType="String" Length="255" Description="SEO商品分类关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO商品分类描述" />
      </Columns>
      <Indexes>
        <Index Columns="GId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="PdRecharge" TableName="DH_PdRecharge" Description="预存款充值表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Sn" DataType="String" Length="20" Description="记录唯一标示" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Nullable="False" Description="会员名称" />
        <Column Name="Amount" DataType="Decimal" Description="充值金额" />
        <Column Name="PCode" DataType="String" Length="20" Description="支付方式" />
        <Column Name="TradeSn" DataType="String" Description="第三方支付接口交易号" />
        <Column Name="State" DataType="Boolean" Description="支付状态。是否支付" />
        <Column Name="PayTime" DataType="DateTime" Description="支付时间" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建用户" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
        <Index Columns="Sn" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="Remittance" TableName="DH_Remittance" Description="线下汇款" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="RName" DataType="String" Nullable="False" Description="汇款户名" />
        <Column Name="Amount" DataType="Decimal" Description="汇款金额" />
        <Column Name="RNum" DataType="String" Description="汇款账号" />
        <Column Name="RBankName" DataType="String" Description="汇款银行" />
        <Column Name="ImgUrl" DataType="String" Description="汇款凭证图片路径" />
        <Column Name="State" DataType="Int32" Description="审核状态 0:审核中 1:审核成功 2:审核失败 3:取消审核" />
        <Column Name="Remark" DataType="String" Description="审核备注" />
        <Column Name="AuditUId" DataType="Int32" Description="审核人Id" />
        <Column Name="AuditDateTime" DataType="DateTime" Description="审核时间" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="State" />
      </Indexes>
    </Table>
    <Table Name="PdLog" TableName="DH_PdLog" Description="预付款变更日志表">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Length="100" Nullable="False" Description="会员名称" />
        <Column Name="AdminId" DataType="Int32" Description="管理员ID" />
        <Column Name="AdminName" DataType="String" Length="100" Description="管理员名称" />
        <Column Name="PdType" DataType="String" Length="100" Description="order_pay下单支付预存款,order_freeze下单冻结预存款,order_cancel取消订单解冻预存款,order_comb_pay下单支付被冻结的预存款,recharge充值,cash_apply申请提现冻结预存款,cash_pay提现成功,cash_del取消提现申请，解冻预存款,refund退款,sys_add_money管理员调节增加余额,sys_del_money管理员调节减少余额,order_points积分充值,sys_thaw_money管理员调整解冻余额,sys_freeze_money管理员调整冻结余额" />
        <Column Name="Amount" DataType="Decimal" Description="可用金额变更0:未变更" />
        <Column Name="FreezeAmount" DataType="Decimal" Description="冻结金额变更0:未变更" />
        <Column Name="Balance" DataType="Decimal" Description="可用金额总金额(总额)" />
        <Column Name="FreezeBalance" DataType="Decimal" Description="冻结金额总金额(总额)" />
        <Column Name="Desc" DataType="String" Length="100" Description="变更描述" />
        <Column Name="CreateUser" DataType="String" Description="变更者" />
        <Column Name="CreateUserID" DataType="Int32" Description="变更者" />
        <Column Name="CreateTime" DataType="DateTime" Description="变更添加时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="FriendLinks" TableName="DH_FriendLinks" Description="友情链接" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="友情链接标题" />
        <Column Name="FType" DataType="Int16" Description="类型。0为文字，1为图片" />
        <Column Name="JumpType" DataType="Int16" Description="类型。0为直接跳转，1为Jump跳转" />
        <Column Name="Url" DataType="String" Length="100" Description="友情链接地址" />
        <Column Name="Pic" DataType="String" Length="100" Description="友情链接图片" />
        <Column Name="Sort" DataType="Int32" Description="友情链接排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="FType" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="FriendLinksLan" TableName="DH_FriendLinksLan" Description="友情链接翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="FId" DataType="Int32" Description="友情链接Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="友情链接标题" />
        <Column Name="FType" DataType="Int16" Description="类型。0为文字，1为图片。" />
        <Column Name="JumpType" DataType="Int16" Description="类型。0为直接跳转，1为Jump跳转" />
        <Column Name="Url" DataType="String" Length="100" Description="友情链接地址" />
        <Column Name="Pic" DataType="String" Length="100" Description="友情链接图片" />
        <Column Name="Sort" DataType="Int32" Description="友情链接排序" />
        <Column Name="Enabled" DataType="Int32" Description="是否启用" />
      </Columns>
      <Indexes>
        <Index Columns="FId,LId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="ArticleCategory" TableName="DH_ArticleCategory" Description="文章分类" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="30" Description="分类名称" />
        <Column Name="ParentId" DataType="Int32" Description="所属父级Id" />
        <Column Name="ParentIdList" DataType="String" Description="父级Id集合" />
        <Column Name="Level" DataType="Int32" Description="当前层级" />
        <Column Name="DisplayOrder" DataType="Int16" Description="排序" />
        <Column Name="JumpUrl" DataType="String" Length="100" Description="跳转Url" />
        <Column Name="ViewName" DataType="String" Length="100" Description="模板名称" />
        <Column Name="InfoViewName" DataType="String" Length="100" Description="详情模板名称" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="ParentId" />
        <Index Columns="Level" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="ArticleCategoryLan" TableName="DH_ArticleCategoryLan" Description="文章分类翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AId" DataType="Int32" Description="文章分类Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Description="分类名称" />
        <Column Name="JumpUrl" DataType="String" Length="100" Description="跳转Url" />
        <Column Name="ViewName" DataType="String" Length="100" Description="模板名称" />
        <Column Name="InfoViewName" DataType="String" Length="100" Description="详情模板名称" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
      </Columns>
      <Indexes>
        <Index Columns="AId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="Article" TableName="DH_Article" Description="文章" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="MId" DataType="String" Description="产品型号Id" />
        <Column Name="AId" DataType="Int32" Description="文章分类Id" />
        <Column Name="Url" DataType="String" Length="200" Description="文章跳转链接" />
        <Column Name="Show" DataType="Boolean" DefaultValue="True" Description="文章是否显示，默认为1" />
        <Column Name="Sort" DataType="Int32" Description="文章排序" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MId" />
        <Index Columns="AId" />
        <Index Columns="Name" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="ArticleLan" TableName="DH_ArticleLan" Description="文章翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AId" DataType="Int32" Description="文章Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="文章标题" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="内容" />
        <Column Name="Summary" DataType="String" Length="512" Description="简介" />
        <Column Name="Pic" DataType="String" Length="255" Description="文章主图" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="Keys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="Description" DataType="String" Length="255" Description="SEO描述" />
      </Columns>
      <Indexes>
        <Index Columns="AId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="FeedBack" TableName="DH_FeedBack" Description="意见反馈" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="姓名" />
        <Column Name="Phone" DataType="String" Description="电话" />
        <Column Name="Mail" DataType="String" Description="邮箱" />
        <Column Name="CompanyName" DataType="String" Description="公司名称" />
        <Column Name="Theme" DataType="String" Length="200" Description="主题" />
        <Column Name="Country" DataType="String" Description="国家" />
        <Column Name="Content" DataType="String" Length="500" Description="反馈内容" />
        <Column Name="FType" DataType="Int16" Description="1:手机端 2:PC端" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="UName" DataType="String" Length="100" Nullable="False" Description="会员名称" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="Express" TableName="DH_Express" Description="快递物流公司。中国" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Nullable="False" Description="名称" />
        <Column Name="Code" DataType="String" Length="16" Description="编码。" />
        <Column Name="Status" DataType="Boolean" DefaultValue="True" Description="状态" />
        <Column Name="Letter" DataType="String" Description="首字母。" />
        <Column Name="OType" DataType="Int16" Description="1:常用2:不常用" />
        <Column Name="Url" DataType="String" Length="80" Description="网址" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
        <Index Columns="Code" Unique="True" />
        <Index Columns="Letter" />
      </Indexes>
    </Table>
    <Table Name="InternationalExpress" TableName="DH_InternationalExpress" Description="国际快递物流公司" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Nullable="False" Description="名称" />
        <Column Name="Key" DataType="String" Length="16" Description="编码" />
        <Column Name="Status" DataType="Boolean" DefaultValue="True" Description="状态" />
        <Column Name="Letter" DataType="String" Description="首字母。17Track的用名称的首字母" />
        <Column Name="OType" DataType="Int16" Description="1:常用2:不常用" />
        <Column Name="Url" DataType="String" Length="80" Description="网址" />
        <Column Name="Tel" DataType="String" Description="电话" />
        <Column Name="Email" DataType="String" Length="80" Description="邮箱" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="TwoLetterIsoCode" DataType="String" Length="2" Description="国家二字代码" />
        <Column Name="ParameterType" DataType="String" Description="参数类型" />
        <Column Name="ParameterExample" DataType="String" Description="参数示例" />
        <Column Name="Mandatory" DataType="Boolean" Description="是否必填" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
        <Index Columns="Key" Unique="True" />
        <Index Columns="Letter" />
        <Index Columns="TwoLetterIsoCode" />
      </Indexes>
    </Table>
    <Table Name="InternationalExpressLan" TableName="DH_InternationalExpressLan" Description="国际快递物流公司翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="EId" DataType="Int32" Description="国际快递物流公司Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="名称" />
      </Columns>
      <Indexes>
        <Index Columns="EId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="ExpressCountry" TableName="DH_ExpressCountry" Description="国家快递物流。指定国家使用默认快递物流公司，不指定则可选全部启用" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="TwoLetterIsoCode" DataType="String" Length="2" Description="国家二字代码" />
        <Column Name="Express" DataType="String" RawType="text" Length="0" Description="快递公司ID的组合" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="TwoLetterIsoCode" />
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="MerchantMaterial" TableName="DH_MerchantMaterial" Description="商家物料" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="Name" DataType="String" Master="True" Nullable="False" Description="名称" />
        <Column Name="Quantity" DataType="Int32" Description="总数量" />
        <Column Name="TemporaryQuantity" DataType="Int32" Description="暂扣数量" />
        <Column Name="Enabled" DataType="Boolean" Description="是否启用。默认启用" />
        <Column Name="Status" DataType="Int32" DefaultValue="0" Description="状态 0:未审核,1:已审核 2:审核不通过" />
        <Column Name="Images" DataType="String" Length="500" Description="上传图片" />
        <Column Name="Cause" DataType="String" Length="500" Description="审核原因" />
        <Column Name="Auditor" DataType="Int32" Description="审核者" />
        <Column Name="AuditTime" DataType="Int64" Description="审核时间" />
        <Column Name="Remark" DataType="String" Description="备注" />
        <Column Name="Weight" DataType="Decimal" DefaultValue="0.000" Description="重量" />
        <Column Name="Volume" DataType="Decimal" DefaultValue="0.000" Description="体积" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId,Name" Unique="True" />
        <Index Columns="Enabled" />
      </Indexes>
    </Table>
    <Table Name="MaterialIncoming" TableName="DH_MaterialIncoming" Description="物料入库" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="WareHouseId" DataType="Int32" Description="仓库ID" />
        <Column Name="MerchantMaterialId" DataType="Int64" Description="商家物料编号" />
        <Column Name="InvolvesCosts" DataType="Decimal" Description="涉及费用" />
        <Column Name="Quantity" DataType="Int32" Description="数量" />
        <Column Name="Remark" DataType="String" Description="备注" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="MerchantMaterialId" />
      </Indexes>
    </Table>
    <Table Name="MaterialOutbound" TableName="DH_MaterialOutbound" Description="物料出库" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="OrderId" DataType="Int64" Description="订单ID" />
        <Column Name="WareHouseId" DataType="Int32" Description="仓库ID" />
        <Column Name="MerchantMaterialId" DataType="Int64" Description="商家物料编号" />
        <Column Name="InvolvesCosts" DataType="Decimal" Description="涉及费用" />
        <Column Name="Quantity" DataType="Int32" Description="数量" />
        <Column Name="Remark" DataType="String" Description="备注" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="MerchantMaterialId" />
      </Indexes>
    </Table>
    <Table Name="WareHouse" TableName="DH_WareHouse" Description="仓库表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Nullable="False" Description="仓库名称" />
        <Column Name="Code" DataType="String" Nullable="False" Description="仓库编码" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="CountryCode" DataType="String" Length="2" Description="所在国家两字母ISO代码" />
        <Column Name="RegionId" DataType="Int32" Description="地区ID" />
        <Column Name="RegionCode" DataType="String" Description="地区代码" />
        <Column Name="Address" DataType="String" Length="100" Description="仓库地址" />
        <Column Name="UName" DataType="String" Description="联系人姓名" />
        <Column Name="UMobile" DataType="String" Description="联系人电话" />
        <Column Name="Enabled" DataType="Boolean" Description="是否启用。默认启用" />
        <Column Name="Remark" DataType="String" Description="备注" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" Unique="True" />
        <Index Columns="Code" Unique="True" />
        <Index Columns="Enabled" />
      </Indexes>
    </Table>
    <Table Name="WareHouseLan" TableName="DH_WareHouseLan" Description="仓库翻译" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="WId" DataType="Int32" Description="仓库Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="仓库名称" />
        <Column Name="Address" DataType="String" RawType="text" Length="0" Description="仓库地址" />
      </Columns>
      <Indexes>
        <Index Columns="WId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="WareHouseMaterial" TableName="DH_WareHouseMaterial" Description="仓库物料表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="WareHouseId" DataType="Int32" Description="仓库ID" />
        <Column Name="MerchantMaterialId" DataType="Int64" Description="商家物料编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="Quantity" DataType="Int32" Description="数量" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="WareHouseId,MerchantMaterialId" Unique="True" />
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="BuyerAddress" TableName="DH_BuyerAddress" Description="买家地址表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AddressType" DataType="Int32" Description="0为收货地址，1为发票地址，2为共用地址" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="RealName" DataType="String" Description="收货人姓名" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="TwoLetterIsoCode" DataType="String" Description="两个字母ISO代码" />
        <Column Name="RegionId" DataType="Int32" Description="地区ID/中国省ID" />
        <Column Name="CityId" DataType="Int32" Description="城市ID/中国市ID" />
        <Column Name="AreaId" DataType="Int32" Description="区县ID/中国区ID" />
        <Column Name="AreaInfo" DataType="String" Description="国省市区信息" />
        <Column Name="AddressDetail" DataType="String" Description="详细地址" />
        <Column Name="ZipCode" DataType="String" Description="邮编" />
        <Column Name="AreaCodeId" DataType="Int32" Description="区号Id" />
        <Column Name="AreaCode" DataType="String" Description="区号" />
        <Column Name="Phone" DataType="String" Description="电话号码" />
        <Column Name="Email" DataType="String" Description="电子邮箱" />
        <Column Name="CompanyName" DataType="String" Description="公司名称" />
        <Column Name="TaxIdType" DataType="String" Description="税号类型" />
        <Column Name="TaxId" DataType="String" Description="税号" />
        <Column Name="IsDefaultDelivery" DataType="Boolean" Description="是否默认收货地址" />
        <Column Name="IsDefaultInvoice" DataType="Boolean" Description="是否默认发票地址" />
        <Column Name="ChainId" DataType="Int32" Description="门店ID" />
        <Column Name="Longitude" DataType="String" Description="经度" />
        <Column Name="Latitude" DataType="String" Description="纬度" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="DeliveryAddress" TableName="DH_DeliveryAddress" Description="卖家发货地址表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="发货地址ID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="SellerName" DataType="String" Description="联系人" />
        <Column Name="CountryId" DataType="Int32" Description="所在国家ID" />
        <Column Name="TwoLetterIsoCode" DataType="String" Description="两个字母ISO代码" />
        <Column Name="RegionId" DataType="Int32" Description="地区ID/中国省ID" />
        <Column Name="CityId" DataType="Int32" Description="城市ID/中国市ID" />
        <Column Name="AreaId" DataType="Int32" Description="区县ID/中国区ID" />
        <Column Name="AreaInfo" DataType="String" Description="国省市区信息" />
        <Column Name="AddressDetail" DataType="String" Description="详细地址" />
        <Column Name="Phone" DataType="String" Length="40" Description="发货电话" />
        <Column Name="CompanyName" DataType="String" Description="发货公司" />
        <Column Name="IsDefault" DataType="Boolean" Description="是否默认发货地址" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="FreightAccount" TableName="DH_FreightAccount" Description="货运账户表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="ExpressId" DataType="Int32" Description="快递物流公司Id" />
        <Column Name="ExpressCode" DataType="String" Description="快递物流公司Code，目前仅使用DHL、FedEx、UPS、SFExpress" />
        <Column Name="AccountNumber" DataType="String" Description="账号" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="Order" TableName="DH_Order" Description="订单表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="订单自增id" />
        <Column Name="OrderSn" DataType="String" Length="30" Description="订单编号" />
        <Column Name="PaySn" DataType="String" Length="30" Description="支付单号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="卖家店铺名称" />
        <Column Name="BuyerId" DataType="Int32" Description="买家ID" />
        <Column Name="BuyerName" DataType="String" Description="买家姓名" />
        <Column Name="BuyerEmail" DataType="String" Length="80" Description="买家电子邮箱" />
        <Column Name="ChainId" DataType="Int32" Description="门店ID" />
        <Column Name="AddTime" DataType="Int64" Description="订单生成时间" />
        <Column Name="PaymentCode" DataType="String" Length="20" Description="支付方式名称代码" />
        <Column Name="PaymentTime" DataType="Int64" Description="支付(付款)时间" />
        <Column Name="TradeNo" DataType="String" Length="35" Description="第三方平台交易号" />
        <Column Name="FinnshedTime" DataType="Int64" Description="订单完成时间" />
        <Column Name="CurrencyCode" DataType="String" Length="10" Description="订单货币代码" />
        <Column Name="CurrencyRate" DataType="Decimal" DefaultValue="1.00" Description="订单货币汇率" />
        <Column Name="SymbolLeft" DataType="String" Length="10" Description="左标志" />
        <Column Name="SymbolRight" DataType="String" Length="10" Description="右标志" />
        <Column Name="CurGoodsAmount" DataType="Decimal" DefaultValue="0.00" Description="结算货币商品总价格" />
        <Column Name="GoodsAmount" DataType="Decimal" DefaultValue="0.00" Description="商品总价格" />
        <Column Name="CurOrderAmount" DataType="Decimal" DefaultValue="0.00" Description="结算货币订单总价格" />
        <Column Name="OrderAmount" DataType="Decimal" DefaultValue="0.00" Description="订单总价格" />
        <Column Name="RcbAmount" DataType="Decimal" DefaultValue="0.00" Description="充值卡支付金额" />
        <Column Name="PdAmount" DataType="Decimal" DefaultValue="0.00" Description="预存款支付金额" />
        <Column Name="PresellDepositAmount" DataType="Decimal" DefaultValue="0.00" Description="定金金额" />
        <Column Name="PresellRcbAmount" DataType="Decimal" DefaultValue="0.00" Description="定金充值卡支付金额" />
        <Column Name="PresellPdAmount" DataType="Decimal" DefaultValue="0.00" Description="定金预存款支付金额" />
        <Column Name="PresellTradeNo" DataType="String" Length="35" Description="定金第三方交易号" />
        <Column Name="PresellPaymentCode" DataType="String" Length="20" Description="定金支付方式名称代码" />
        <Column Name="PresellEndTime" DataType="Int64" Description="预售结束时间" />
        <Column Name="CurShippingFee" DataType="Decimal" DefaultValue="0.00" Description="结算货币运费" />
        <Column Name="ShippingFee" DataType="Decimal" DefaultValue="0.00" Description="运费" />
        <Column Name="EvaluationState" DataType="Int32" DefaultValue="0" Description="评价状态 0：未评价 1：已评价 2:已过期" />
        <Column Name="OrderState" DataType="Int32" DefaultValue="10" Description="订单状态：0:已取消 10:未付款 14:待付定金 15:待付尾款 20:待发货 30:已发货 35:待自提 40:已收货" />
        <Column Name="RefundState" DataType="Int32" DefaultValue="0" Description="退款状态 0:无退款 1:部分退款 2:全部退款" />
        <Column Name="OrderRefundLockState" DataType="Int32" DefaultValue="0" Description="锁定状态:0:正常,大于0:锁定" />
        <Column Name="RefundAmount" DataType="Decimal" DefaultValue="0.00" Description="退款金额" />
        <Column Name="DeleteState" DataType="Int32" DefaultValue="0" Description="删除状态 0:未删除 1:放入回收站 2:彻底删除" />
        <Column Name="DelayTime" DataType="Int64" DefaultValue="0" Description="延迟时间,默认为0" />
        <Column Name="OrderFrom" DataType="String" Length="15" Description="订单来源" />
        <Column Name="ShippingCode" DataType="String" Description="订单物流单号" />
        <Column Name="OrderType" DataType="Int32" Description="订单类型" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="BuyerId" />
      </Indexes>
    </Table>
    <Table Name="OrderCommon" TableName="DH_OrderCommon" Description="订单信息扩展表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="订单ID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="ShippingTime" DataType="Int64" Description="配送时间" />
        <Column Name="ShippingExpressId" DataType="Int32" DefaultValue="0" Description="配送公司ID" />
        <Column Name="EvaluationTime" DataType="Int64" Description="评价时间" />
        <Column Name="EvalsellerState" DataType="Int32" DefaultValue="0" Description="卖家是否已评价买家 0/1" />
        <Column Name="EvalsellerTime" DataType="Int64" Description="卖家评价买家的时间" />
        <Column Name="OrderMessage" DataType="String" Length="300" Description="订单留言" />
        <Column Name="OrderPointscount" DataType="Int32" DefaultValue="0" Description="订单赠送积分" />
        <Column Name="VoucherPrice" DataType="Int32" Description="店铺代金券面额" />
        <Column Name="VoucherCode" DataType="String" Length="32" Description="店铺代金券编码" />
        <Column Name="MallvoucherPrice" DataType="Int32" Description="平台代金券面额" />
        <Column Name="MallvoucherCode" DataType="String" Length="32" Description="平台代金券编码" />
        <Column Name="DeliverExplain" DataType="String" RawType="text" Length="0" Description="订单发货备注" />
        <Column Name="DaddressId" DataType="Int32" DefaultValue="0" Description="发货仓库ID" />
        <Column Name="ReciverName" DataType="String" Description="收货人姓名" />
        <Column Name="ZipCode" DataType="String" Description="收货人邮编" />
        <Column Name="Phone" DataType="String" Description="收货人电话号码" />
        <Column Name="Email" DataType="String" Description="收货人电子邮箱" />
        <Column Name="ReciverInfo" DataType="String" Length="500" Description="收货人其它信息" />
        <Column Name="ReciverCountryId" DataType="Int32" Description="收货人国家级ID" />
        <Column Name="ReciverTwoLetterIsoCode" DataType="String" Description="两个字母ISO代码" />
        <Column Name="ReciverProvinceId" DataType="Int32" Description="收货人省级ID" />
        <Column Name="ReciverCityId" DataType="Int32" Description="收货人市级ID" />
        <Column Name="InvoiceInfo" DataType="String" Length="500" Description="订单发票信息" />
        <Column Name="PromotionInfo" DataType="String" Length="500" Description="订单促销信息备注" />
        <Column Name="ActualSymbolLeft" DataType="String" Length="10" Description="实际支付货币左标志" />
        <Column Name="ActualCurrencyCode" DataType="String" Length="10" Description="实际支付货币代码" />
        <Column Name="ActualShippingFee" DataType="Decimal" DefaultValue="0.00" Description="实际支付运费" />
        <Column Name="ActualCurrencyRate" DataType="Decimal" DefaultValue="1.00" Description="实际支付货币汇率" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="OrderGoods" TableName="DH_OrderGoods" Description="订单商品表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="订单商品表自增ID" />
        <Column Name="OrderId" DataType="Int64" Description="订单ID" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="GoodsName" DataType="String" Length="200" Description="商品名称" />
        <Column Name="SKUId" DataType="Int64" Description="SKUId" />
        <Column Name="MaterialId" DataType="Int64" Description="物料编号" />
        <Column Name="CurrencyCode" DataType="String" Length="10" Description="订单货币代码" />
        <Column Name="CurrencyRate" DataType="Decimal" DefaultValue="1.00" Description="订单货币汇率" />
        <Column Name="SymbolLeft" DataType="String" Length="10" Description="左标志" />
        <Column Name="SymbolRight" DataType="String" Length="10" Description="右标志" />
        <Column Name="CurGoodsPrice" DataType="Decimal" DefaultValue="0.00" Description="结算货币商品价格" />
        <Column Name="GoodsPrice" DataType="Decimal" DefaultValue="0.00" Description="商品价格" />
        <Column Name="GoodsNum" DataType="Int32" DefaultValue="1" Description="商品数量" />
        <Column Name="GoodsImage" DataType="String" Length="100" Description="商品图片" />
        <Column Name="CurGoodsPayPrice" DataType="Decimal" DefaultValue="0.00" Description="商品实际成交价" />
        <Column Name="GoodsPayPrice" DataType="Decimal" Description="商品实际成交价" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="BuyerId" DataType="Int32" Description="买家ID" />
        <Column Name="GoodsType" DataType="Int32" DefaultValue="1" Description="1默认2抢购商品3限时折扣商品4组合套装5赠品6拼团7会员等级折扣8砍价9批发10预售" />
        <Column Name="PromotionsId" DataType="Int32" DefaultValue="0" Description="促销活动ID（抢购ID/限时折扣ID/优惠套装ID）与GoodsType搭配使用" />
        <Column Name="CommisRate" DataType="Int32" DefaultValue="0" Description="佣金比例" />
        <Column Name="GcId" DataType="Int64" DefaultValue="0" Description="商品最底级分类ID" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="OrderId" />
      </Indexes>
    </Table>
    <Table Name="OrderPay" TableName="DH_OrderPay" Description="订单支付表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="订单支付自增ID" />
        <Column Name="PaySn" DataType="String" Length="30" Description="支付单号" />
        <Column Name="PayOkSn" DataType="String" Length="30" Description="支付成功单号(用于退款)" />
        <Column Name="BuyerId" DataType="Int32" Description="买家ID" />
        <Column Name="ApiPaystate" DataType="Int32" DefaultValue="0" Description="0默认未支付1已支付(只有第三方支付接口通知到时才会更改此状态)" />
      </Columns>
      <Indexes>
        <Index Columns="PaySn" />
      </Indexes>
    </Table>
    <Table Name="GoodsCommon" TableName="DH_GoodsCommon" Description="商品公共表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Nullable="False" Description="商品名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="商品广告词" />
        <Column Name="CId" DataType="Int64" Description="商品分类ID" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int64" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int64" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int64" Description="三级分类ID" />
        <Column Name="GoodsClassName" DataType="String" Length="200" Description="商品分类名称" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="SpecName" DataType="String" Length="255" Description="规格名称" />
        <Column Name="SpecValue" DataType="String" RawType="text" Length="0" Description="规格值" />
        <Column Name="BrandId" DataType="Int32" DefaultValue="0" Description="商品品牌ID" />
        <Column Name="BrandName" DataType="String" Length="100" Description="商品品牌名称" />
        <Column Name="TypeId" DataType="Int32" DefaultValue="0" Description="类型ID" />
        <Column Name="GoodsImage" DataType="String" Length="100" Description="商品主图" />
        <Column Name="GoodsVideoName" DataType="String" Length="100" Description="商品视频名称" />
        <Column Name="GoodsAttr" DataType="String" RawType="text" Length="0" Description="商品属性" />
        <Column Name="GoodsBody" DataType="String" RawType="text" Length="0" Description="商品内容" />
        <Column Name="MobileBody" DataType="String" RawType="text" Length="0" Description="手机端商品描述" />
        <Column Name="GoodsState" DataType="Int32" DefaultValue="0" Description="商品状态 0:下架 1:正常 10:违规（禁售）" />
        <Column Name="GoodsSteteRemark" DataType="String" Length="255" Description="违规原因" />
        <Column Name="GoodsVerify" DataType="Int32" Description="商品审核 1:通过 0:未通过 10:审核中" />
        <Column Name="GoodsVerifyRemark" DataType="String" Length="255" Description="审核失败原因" />
        <Column Name="GoodsLock" DataType="Int32" DefaultValue="0" Description="商品锁定 0未锁，1已锁" />
        <Column Name="GoodsAddTime" DataType="Int64" Description="商品添加时间" />
        <Column Name="GoodsShelftime" DataType="Int64" Description="上架时间" />
        <Column Name="GoodsSpecName" DataType="String" RawType="text" Length="0" Description="规格名称序列化（下标为规格id）" />
        <Column Name="GoodsPrice" DataType="Decimal" Description="商品价格" />
        <Column Name="GoodsMarketPrice" DataType="Decimal" Description="商品市场价" />
        <Column Name="GoodsCostPrice" DataType="Decimal" Description="商品成本价" />
        <Column Name="GoodsDiscount" DataType="Double" Description="商品折扣" />
        <Column Name="GoodsSerial" DataType="String" Description="商家编号" />
        <Column Name="GoodsStorageAlarm" DataType="Int32" DefaultValue="0" Description="商品库存报警值" />
        <Column Name="TransportId" DataType="Int32" DefaultValue="0" Description="商品售卖区域id" />
        <Column Name="TransportTitle" DataType="String" Length="60" Description="商品售卖区域名称" />
        <Column Name="GoodsCommend" DataType="Int32" DefaultValue="0" Description="商品推荐 1:是 0:否" />
        <Column Name="GoodsFreight" DataType="Decimal" DefaultValue="0.00" Description="商品运费 0为免运费" />
        <Column Name="GoodsVat" DataType="Int32" DefaultValue="0" Description="商品是否开具增值税发票 1:是 0:否" />
        <Column Name="AreaId1" DataType="Int32" Description="一级地区id" />
        <Column Name="AreaId2" DataType="Int32" Description="二级地区id" />
        <Column Name="GoodsStcids" DataType="String" Length="255" Description="店铺分类id 首尾用,隔开" />
        <Column Name="PlateidTop" DataType="Int64" Description="顶部关联板式" />
        <Column Name="PlateidBottom" DataType="Int64" Description="底部关联板式" />
        <Column Name="IsVirtual" DataType="Int32" DefaultValue="0" Description="是否为虚拟商品 1:是 0:否" />
        <Column Name="VirtualType" DataType="Int32" DefaultValue="0" Description="虚拟商品类型 0:核销商品 1:卡券商品 2:网盘商品 3:下载商品" />
        <Column Name="VirtualIndate" DataType="Int64" Description="虚拟商品有效期" />
        <Column Name="VirtualLimit" DataType="Int32" Description="虚拟商品购买上限" />
        <Column Name="VirtualInvalidRefund" DataType="Int32" DefaultValue="1" Description="是否允许过期退款 1:是 0:否" />
        <Column Name="IsGoodsFCode" DataType="Int32" DefaultValue="0" Description="是否为F码商品 1:是 0:否" />
        <Column Name="IsAppoint" DataType="Int32" DefaultValue="0" Description="是否是预约商品 1:是 0:否" />
        <Column Name="AppointSatedate" DataType="Int64" DefaultValue="0" Description="预约商品出售时间" />
        <Column Name="IsPlatformStore" DataType="Int32" DefaultValue="0" Description="是否为平台自营" />
        <Column Name="GoodsMGDiscount" DataType="String" RawType="text" Length="0" Description="序列化会员等级折扣(商品)" />
        <Column Name="InviterRatio" DataType="Decimal" DefaultValue="0.00" Description="分销比例" />
        <Column Name="InviterTotalQuantity" DataType="Int64" DefaultValue="0" Description="已分销的商品数量" />
        <Column Name="InviterTotalAmount" DataType="Decimal" DefaultValue="0.00" Description="已分销的商品金额" />
        <Column Name="InviterAmount" DataType="Decimal" DefaultValue="0.00" Description="商品已结算的分销佣金" />
        <Column Name="InviterOpen" DataType="Int32" DefaultValue="0" Description="开启推广" />
        <Column Name="MallGoodsCommend" DataType="Int32" DefaultValue="0" Description="商品推荐 1:是 0:否" />
        <Column Name="MallGoodsSort" DataType="Int32" DefaultValue="255" Description="商品推荐排序" />
        <Column Name="GoodsSort" DataType="Int32" DefaultValue="255" Description="店铺排序" />
        <Column Name="StoreServiceIds" DataType="String" RawType="text" Length="0" Description="店铺服务ID列表 首尾用,隔开" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="CId" />
        <Index Columns="Cid1" />
        <Index Columns="Cid2" />
        <Index Columns="Cid3" />
      </Indexes>
    </Table>
    <Table Name="ClassAttributes" TableName="DH_ClassAttributes" Description="分类属性表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ClassId" DataType="Int64" Description="分类编号" />
        <Column Name="Name" DataType="String" Master="True" Description="属性名称" />
        <Column Name="MappingField" DataType="String" Description="商品扩展属性字段名。如Field1、Field2" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="ClassAttributesLan" TableName="DH_ClassAttributesLan" Description="分类属性翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="CId" DataType="Int64" Description="分类属性Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="分类属性名称" />
      </Columns>
      <Indexes>
        <Index Columns="CId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="GoodsExAttributes" TableName="DH_GoodsExAttributes" Description="商品扩展属性表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ClassId" DataType="Int64" Description="分类编号" />
        <Column Name="GoodsId" DataType="Int64" Description="商品编号" />
        <Column Name="Field1" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field2" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field3" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field4" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field5" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field6" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field7" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field8" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field9" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field10" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field11" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field12" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field13" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field14" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field15" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field16" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field17" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field18" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field19" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field20" DataType="String" Description="对应分类属性的字段" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="GoodsClassExAttributes" TableName="DH_GoodsClassExAttributes" Description="分类属性扩展表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ClassId" DataType="Int64" Description="分类编号" />
        <Column Name="Field1" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field2" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field3" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field4" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field5" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field6" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field7" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field8" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field9" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field10" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field11" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field12" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field13" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field14" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field15" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field16" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field17" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field18" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field19" DataType="String" Description="对应分类属性的字段" />
        <Column Name="Field20" DataType="String" Description="对应分类属性的字段" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="Goods" TableName="DH_Goods" Description="商品表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="GoodsCommonId" DataType="Int64" Description="商品公共表ID" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Nullable="False" Description="商品名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="商品广告词" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="CId" DataType="Int64" Description="商品分类ID" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int64" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int64" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int64" Description="三级分类ID" />
        <Column Name="BrandId" DataType="Int32" DefaultValue="0" Description="商品品牌ID" />
        <Column Name="GoodsPrice" DataType="Decimal" Description="商品价格" />
        <Column Name="GoodsPromotionPrice" DataType="Decimal" Description="商品促销价格" />
        <Column Name="GoodsPromotionType" DataType="Decimal" Description="促销类型 0:无促销 1:抢购 2:限时折扣" />
        <Column Name="GoodsMarketPrice" DataType="Decimal" Description="商品市场价" />
        <Column Name="GoodsSerial" DataType="String" Description="商家编号" />
        <Column Name="GoodsStorageAlarm" DataType="Int32" DefaultValue="0" Description="商品库存报警值" />
        <Column Name="GoodsClick" DataType="Int32" Description="商品点击数量" />
        <Column Name="GoodsSalenum" DataType="Int32" Description="商品销售数量" />
        <Column Name="GoodsBuynum" DataType="Int32" Description="商品购买人数" />
        <Column Name="GoodsCollect" DataType="Int32" Description="商品收藏数量" />
        <Column Name="GoodsSpec" DataType="Int32" Description="商品规格Id" />
        <Column Name="MerchantMaterial" DataType="Int64" Description="商家物料编号" />
        <Column Name="MaterialIds" DataType="String" Length="500" Description="物料编号集合" />
        <Column Name="GoodsStorage" DataType="Int32" Description="商品库存" />
        <Column Name="GoodsWeight" DataType="Decimal" DefaultValue="0.000" Description="商品重量" />
        <Column Name="GoodsImage" DataType="String" Length="100" Description="商品主图" />
        <Column Name="GoodsVideoName" DataType="String" Length="100" Description="商品视频名称" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="商品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端商品描述" />
        <Column Name="GoodsLock" DataType="Int32" DefaultValue="0" Description="商品锁定 0未锁，1已锁" />
        <Column Name="GoodsState" DataType="Int32" DefaultValue="0" Description="商品状态 0:下架 1:正常 10:违规（禁售）" />
        <Column Name="GoodsVerify" DataType="Int32" Description="商品审核 1:通过 0:未通过 10:审核中" />
        <Column Name="GoodsAddTime" DataType="Int64" Description="商品添加时间" />
        <Column Name="GoodsEditTime" DataType="Int64" Description="商品编辑时间" />
        <Column Name="AreaId1" DataType="Int32" Description="一级地区id" />
        <Column Name="AreaId2" DataType="Int32" Description="二级地区id" />
        <Column Name="RegionId" DataType="Int32" Description="一级地区id" />
        <Column Name="ColorId" DataType="String" Length="200" Description="颜色规格id" />
        <Column Name="TransportId" DataType="Int32" DefaultValue="0" Description="商品售卖区域id" />
        <Column Name="GoodsFreight" DataType="Decimal" DefaultValue="0.00" Description="商品运费 0为免运费" />
        <Column Name="GoodsVat" DataType="Int32" DefaultValue="0" Description="商品是否开具增值税发票 1:是 0:否" />
        <Column Name="GoodsCommend" DataType="Int32" DefaultValue="0" Description="商品推荐 1:是 0:否" />
        <Column Name="GoodsStcids" DataType="String" Length="255" Description="店铺分类id 首尾用,隔开" />
        <Column Name="EvaluationGoodStar" DataType="String" RawType="text" Length="0" Description="好评星级" />
        <Column Name="EvaluationCount" DataType="String" RawType="text" Length="0" Description="评价数" />
        <Column Name="IsVirtual" DataType="Int32" DefaultValue="0" Description="是否为虚拟商品 1:是 0:否" />
        <Column Name="VirtualIndate" DataType="Int64" Description="虚拟商品有效期" />
        <Column Name="VirtualLimit" DataType="Int32" Description="虚拟商品购买上限" />
        <Column Name="VirtualInvalidRefund" DataType="Int32" DefaultValue="1" Description="是否允许过期退款 1:是 0:否" />
        <Column Name="VirtualContent" DataType="String" Length="255" Description="虚拟商品内容" />
        <Column Name="IsGoodsFCode" DataType="Int32" DefaultValue="0" Description="是否为F码商品 1:是 0:否" />
        <Column Name="IsAppoint" DataType="Int32" DefaultValue="0" Description="是否是预约商品 1:是 0:否" />
        <Column Name="IsHaveGift" DataType="String" Length="255" Description="是否拥有赠品" />
        <Column Name="IsPlatformStore" DataType="Int32" DefaultValue="0" Description="是否为平台自营" />
        <Column Name="GoodsMGDiscount" DataType="String" RawType="text" Length="0" Description="序列化会员等级折扣(商品)" />
        <Column Name="GoodsSort" DataType="Int32" DefaultValue="255" Description="店铺排序" />
        <Column Name="Specification" DataType="String" Length="255" Description="商品规格书" />
        <Column Name="ProductManual" DataType="String" Length="255" Description="商品产品手册PDF" />
        <Column Name="DeliveryTime" DataType="String" Length="255" Description="出货货期" />
        <Column Name="DeliveryDate" DataType="String" Length="255" Description="到货货期" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="SeoKeys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="SeoDescription" DataType="String" Length="255" Description="SEO描述" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
        <Index Columns="CId" />
        <Index Columns="Cid1" />
        <Index Columns="Cid2" />
        <Index Columns="Cid3" />
        <Index Columns="BrandId" />
        <Index Columns="GoodsCommonId" />
      </Indexes>
    </Table>
    <Table Name="GoodsSKUDetail" TableName="DH_GoodsSKUDetail" Description="商品SKU属性表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" PrimaryKey="True" DataScale="time" Description="编号" />
        <Column Name="GoodsId" DataType="Int64" Description="商品编号" />
        <Column Name="SpecValue" DataType="String" Length="500" Description="规格值" />
        <Column Name="MaterialId" DataType="Int64" Description="物料编号" />
        <Column Name="GoodsMarketPrice" DataType="Decimal" Description="市场价" />
        <Column Name="GoodsPrice" DataType="Decimal" Description="单价" />
        <Column Name="GoodsClick" DataType="Int32" DefaultValue="0" Description="商品点击数量" />
        <Column Name="GoodsSalenum" DataType="Int32" DefaultValue="0" Description="商品销售数量" />
        <Column Name="GoodsBuynum" DataType="Int32" DefaultValue="0" Description="商品购买人数" />
        <Column Name="GoodsCollect" DataType="Int32" DefaultValue="0" Description="商品收藏数量" />
        <Column Name="EvaluationGoodStar" DataType="String" RawType="text" Length="0" Description="好评星级" />
        <Column Name="EvaluationCount" DataType="String" RawType="text" Length="0" Description="评价数" />
        <Column Name="GoodsState" DataType="Int32" DefaultValue="0" Description="商品状态 0:下架 1:正常 10:违规（禁售）" />
        <Column Name="GoodsVerify" DataType="Int32" DefaultValue="0" Description="商品审核 1:通过 0:未通过 10:审核中" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsId,MaterialId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="GoodsLan" TableName="DH_GoodsLan" Description="商品翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GId" DataType="Int64" Description="商品Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="商品名称" />
        <Column Name="AdvWord" DataType="String" Length="150" Description="商品广告词" />
        <Column Name="GoodsImage" DataType="String" Length="100" Description="商品主图" />
        <Column Name="GoodsVideoName" DataType="String" Length="100" Description="商品视频名称" />
        <Column Name="Content" DataType="String" RawType="text" Length="0" Description="商品内容" />
        <Column Name="MobileContent" DataType="String" RawType="text" Length="0" Description="手机端商品描述" />
        <Column Name="VirtualContent" DataType="String" Length="255" Description="虚拟商品内容" />
        <Column Name="Specification" DataType="String" Length="255" Description="商品规格书" />
        <Column Name="ProductManual" DataType="String" Length="255" Description="商品产品手册PDF" />
        <Column Name="DeliveryTime" DataType="String" Length="512" Description="出货货期" />
        <Column Name="DeliveryDate" DataType="String" Length="512" Description="到货货期" />
        <Column Name="SeoTitle" DataType="String" Length="255" Description="SEO标题" />
        <Column Name="SeoKeys" DataType="String" Length="255" Description="SEO关键词" />
        <Column Name="SeoDescription" DataType="String" Length="255" Description="SEO描述" />
      </Columns>
      <Indexes>
        <Index Columns="GId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="GoodsClassStaple" TableName="DH_GoodsClassStaple" Description="店铺常用分类表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="常用分类ID" />
        <Column Name="StapleCounter" DataType="Int32" DefaultValue="1" Description="计数器" />
        <Column Name="StapleName" DataType="String" Length="255" Description="常用分类名称" />
        <Column Name="Cid1" ColumnName="CId_1" DataType="Int64" Description="一级分类ID" />
        <Column Name="Cid2" ColumnName="CId_2" DataType="Int64" Description="二级分类ID" />
        <Column Name="Cid3" ColumnName="CId_3" DataType="Int64" Description="三级分类ID" />
        <Column Name="TypeId" DataType="Int32" Description="类型ID" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
      </Indexes>
    </Table>
    <Table Name="Transport" TableName="DH_Transport" Description="售卖区域表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="售卖区域自增ID" />
        <Column Name="AreaIds" DataType="String" Description="地区IDs" />
        <Column Name="IsLimited" DataType="Int32" DefaultValue="-1" Description="-1禁卖，限制配送 0:否" />
        <Column Name="TransportType" DataType="Int32" DefaultValue="0" Description="计费方式 0:按件 1:按重量" />
        <Column Name="Price" DataType="Decimal" DefaultValue="0.00" Description="运费" />
        <Column Name="WareHouseId" DataType="Int32" Description="仓库ID" />
      </Columns>
      <Indexes>
        <Index Columns="WareHouseId" />
      </Indexes>
    </Table>
    <Table Name="Cart" TableName="DH_Cart" Description="购物车数据表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="购物车自增ID" />
        <Column Name="BuyerId" DataType="String" Description="SID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="MaterialId" DataType="Int64" Description="物料编号" />
        <Column Name="SKUId" DataType="Int64" Description="SKUId" />
        <Column Name="GoodsPrice" DataType="Decimal" DefaultValue="0.00" Description="商品价格" />
        <Column Name="GoodsNum" DataType="Int32" DefaultValue="1" Description="商品数量" />
        <Column Name="BlId" DataType="Int32" DefaultValue="0" Description="组合套装ID" />
      </Columns>
      <Indexes>
        <Index Columns="BuyerId" />
      </Indexes>
    </Table>
    <Table Name="Wishlist" TableName="DH_Wishlist" Description="心愿清单数据表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="心愿清单自增ID" />
        <Column Name="BuyerId" DataType="Int64" Description="买家ID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="SkuId" DataType="Int64" Description="SKUID" />
        <Column Name="GoodsPrice" DataType="Decimal" DefaultValue="0.00" Description="商品价格" />
        <Column Name="GoodsNum" DataType="Int32" DefaultValue="1" Description="商品数量" />
        <Column Name="BlId" DataType="Int32" DefaultValue="0" Description="组合套装ID" />
        <Column Name="WishlistAddTime" DataType="Int64" Description="加入时间" />
      </Columns>
      <Indexes>
        <Index Columns="BuyerId" />
        <Index Columns="SkuId,BuyerId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="RefundReturn" TableName="DH_RefundReturn" Description="退款退货表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="退款退货记录自增ID" />
        <Column Name="OrderId" DataType="Int64" Description="订单ID" />
        <Column Name="OrderSn" DataType="String" Length="20" Description="订单编号" />
        <Column Name="RefundSn" DataType="String" Description="申请编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="BuyerId" DataType="Int32" Description="买家ID" />
        <Column Name="UName" DataType="String" Length="100" Description="会员名称" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID(0表示全部退款)" />
        <Column Name="OrderGoodsId" DataType="Int64" DefaultValue="0" Description="订单商品ID(0表示全部退款)" />
        <Column Name="GoodsName" DataType="String" Length="200" Description="商品名称" />
        <Column Name="GoodsNum" DataType="Int32" DefaultValue="1" Description="商品数量" />
        <Column Name="CurrencyCode" DataType="String" Length="10" Description="订单货币代码" />
        <Column Name="CurrencyRate" DataType="Decimal" DefaultValue="1.00" Description="订单货币汇率" />
        <Column Name="SymbolLeft" DataType="String" Length="10" Description="左标志" />
        <Column Name="SymbolRight" DataType="String" Length="10" Description="右标志" />
        <Column Name="RefundAmount" DataType="Decimal" DefaultValue="0.00" Description="退款金额" />
        <Column Name="RefundTime" DataType="Int64" DefaultValue="0" Description="退款时间" />
        <Column Name="GoodsImage" DataType="String" Length="100" Description="商品图片" />
        <Column Name="RefundType" DataType="Int32" DefaultValue="1" Description="申请类型:1退款,2退货" />
        <Column Name="ReturnType" DataType="Int32" DefaultValue="1" Description="退货类型:1不用退货,2需要退货" />
        <Column Name="ReturnState" DataType="Int32" DefaultValue="0" Description="订单收货状态:1已收到货,2未收到货" />
        <Column Name="RefundreturnGoodsState" DataType="Int32" DefaultValue="1" Description="退款物流状态:1待发货,2待收货,3未收到,4已收货" />
        <Column Name="RefundreturnSellerState" DataType="Int32" DefaultValue="1" Description="卖家处理状态:1待审核,2同意,3不同意,4已完成" />
        <Column Name="RefundreturnSellerTime" DataType="Int64" Description="卖家处理时间" />
        <Column Name="RefundreturnSellerMessage" DataType="String" Length="300" Description="卖家备注" />
        <Column Name="RefundreturnAdminState" DataType="Int32" DefaultValue="1" Description="申请状态:1处理中,2待管理员处理,3已完成,4已拒绝" />
        <Column Name="RefundreturnAdminTime" DataType="Int64" Description="管理员处理时间" />
        <Column Name="RefundreturnAdminMessage" DataType="String" Length="300" Description="管理员备注" />
        <Column Name="RefundreturnAddTime" DataType="Int64" Description="添加时间" />
        <Column Name="RefundreturnBuyerMessage" DataType="String" Length="300" Description="退款退货申请原因" />
        <Column Name="ReasonId" DataType="Int32" DefaultValue="0" Description="原因ID(0表示其它)" />
        <Column Name="ReasonInfo" DataType="String" Length="300" Description="原因内容" />
        <Column Name="PicInfo" DataType="String" Length="300" Description="退款退货图片" />
        <Column Name="ExpressId" DataType="Int32" DefaultValue="0" Description="物流公司编号" />
        <Column Name="ExpressName" DataType="String" Description="物流公司名称" />
        <Column Name="ExpressCode" DataType="String" Description="物流单号" />
        <Column Name="RefundreturnShipTime" DataType="Int64" DefaultValue="0" Description="发货时间" />
        <Column Name="RefundreturnDelayTime" DataType="Int64" DefaultValue="0" Description="收货延迟时间" />
        <Column Name="RefundreturnReceiveTime" DataType="Int64" DefaultValue="0" Description="收货时间" />
        <Column Name="RefundreturnReceiveMessage" DataType="String" Length="300" Description="收货备注" />
        <Column Name="RefundreturnMoneyState" DataType="Int32" DefaultValue="0" Description="支付状态:0未支付,1已支付" />
        <Column Name="CommisRate" DataType="Int32" DefaultValue="0" Description="佣金比例" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="OrderId" />
        <Index Columns="StoreId" />
        <Index Columns="BuyerId" />
        <Index Columns="OrderGoodsId" />
      </Indexes>
    </Table>
    <Table Name="RefundReason" TableName="DH_RefundReason" Description="退款退货原因表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="原因自增ID" />
        <Column Name="ReasonInfo" DataType="String" Description="原因内容" />
        <Column Name="ReasonSort" DataType="Int32" DefaultValue="255" Description="退款退货原因排序" />
        <Column Name="ReasonUpdateTime" DataType="Int64" Description="退款退货原因更新时间" />
      </Columns>
    </Table>
    <Table Name="Favorites" TableName="DH_Favorites" Description="收藏表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="收藏记录自增ID" />
        <Column Name="UId" DataType="Int32" Description="会员ID" />
        <Column Name="MemberName" DataType="String" Description="会员名" />
        <Column Name="FavId" DataType="Int64" Description="商品ID或店铺ID" />
        <Column Name="FavType" DataType="String" Length="5" DefaultValue="goods" Description="类型:goods为商品,store为店铺" />
        <Column Name="FavTime" DataType="Int64" Description="收藏时间" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="StoreName" DataType="String" Description="店铺名称" />
        <Column Name="StoreClassId" DataType="Int32" Description="店铺分类ID" />
        <Column Name="GcId" DataType="Int64" Description="商品分类ID" />
        <Column Name="FavlogPrice" DataType="Decimal" DefaultValue="0.00" Description="商品收藏时价格" />
        <Column Name="FavlogMsg" DataType="String" Length="20" Description="收藏备注" />
      </Columns>
      <Indexes>
        <Index Columns="UId" />
        <Index Columns="FavId,FavType" />
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="Freight" TableName="DH_Freight" Description="运费表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="LogisticsCompanyId" DataType="Int32" Description="物流公司ID" />
        <Column Name="WareHouseId" DataType="Int32" Description="发货仓库ID" />
        <Column Name="CountryCode" DataType="String" Length="2" Description="收货国家两字母ISO代码" />
        <Column Name="ShippingCost" DataType="Decimal" DefaultValue="0.00" Description="运费" />
        <Column Name="FirstWeight" DataType="Decimal" DefaultValue="0.00" Description="首重(KG)" />
        <Column Name="AdditionalWeight" DataType="Decimal" DefaultValue="0.00" Description="续重" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="LogisticsCompanyId,WareHouseId,CountryCode" Unique="True" />
        <Index Columns="CountryCode,WareHouseId" />
      </Indexes>
    </Table>
    <Table Name="GoodsTieredPrice" TableName="DH_GoodsTieredPrice" Description="商品阶梯价格表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="GoodsCommonId" DataType="Int64" Description="商品公共表ID" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="SkuId" DataType="Int64" Description="商品SkuID" />
        <Column Name="MinQuantity" DataType="Int32" Description="最小购买数量" />
        <Column Name="MaxQuantity" DataType="Int32" Description="最大购买数量" />
        <Column Name="OriginalPrice" DataType="Decimal" Description="原价" />
        <Column Name="Price" DataType="Decimal" Description="销售价" />
        <Column Name="Enabled" DataType="Boolean" DefaultValue="True" Description="是否启用" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsId" />
        <Index Columns="GoodsCommonId" />
        <Index Columns="StoreId" />
        <Index Columns="SkuId" />
        <Index Columns="SkuId,MinQuantity,GoodsId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="SearchKey" TableName="DH_SearchKey" Description="搜索关键词表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="关键词" />
        <Column Name="Color" DataType="String" Description="颜色代码" />
        <Column Name="Enabled" DataType="Boolean" DefaultValue="True" Description="是否启用" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
    <Table Name="SearchKeyLan" TableName="DH_SearchKeyLan" Description="搜索关键词翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SId" DataType="Int64" Description="搜索关键词Id" />
        <Column Name="LId" DataType="Int32" Description="所属语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="200" Description="关键词" />
      </Columns>
      <Indexes>
        <Index Columns="SId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="GoodsSpecValue" TableName="DH_GoodsSpecValue" Description="商品规格属性表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="ParentId" DataType="Int32" DefaultValue="0" Description="所属父级Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="规格名称" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="规格排序" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="GoodsName" DataType="String" Length="100" Description="商品名称" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="StoreId" />
      </Indexes>
    </Table>
    <Table Name="GoodsSpecValueLan" TableName="DH_GoodsSpecValueLan" Description="商品规格属性翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="SId" DataType="Int32" Description="关联规格Id" />
        <Column Name="LId" DataType="Int32" Description="语言Id" />
        <Column Name="Name" DataType="String" Master="True" Length="100" Description="规格名称" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SId,LId" Unique="True" />
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="TemporaryInventory" TableName="DH_TemporaryInventory" Description="库存暂扣表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="WareHouseId" DataType="Int32" Description="仓库ID" />
        <Column Name="OrderId" DataType="Int64" Description="订单ID" />
        <Column Name="OrderGoodsId" DataType="Int64" Description="订单商品ID" />
        <Column Name="MerchantMaterialId" DataType="Int64" Description="商家物料编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="Quantity" DataType="Int32" Description="数量" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="MerchantMaterialId" />
      </Indexes>
    </Table>
    <Table Name="EvaluateGoods" TableName="DH_EvaluateGoods" Description="信誉评价表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int64" Identity="True" PrimaryKey="True" Description="信誉评价自增ID" />
        <Column Name="OrderId" DataType="Int64" Description="订单ID" />
        <Column Name="OrderSn" DataType="String" Length="30" Nullable="False" Description="订单编号" />
        <Column Name="OrderGoodsId" DataType="Int64" Description="订单商品Id" />
        <Column Name="GoodsId" DataType="Int64" Description="商品Id" />
        <Column Name="SkuId" DataType="Int64" Description="SkuId" />
        <Column Name="GoodsName" DataType="String" Length="200" Description="商品名称" />
        <Column Name="GoodsPrice" DataType="Decimal" Description="商品价格" />
        <Column Name="GoodsImage" DataType="String" Length="255" Description="商品图片" />
        <Column Name="GoodsScore" DataType="Int32" Description="商品评分" />
        <Column Name="ShipScore" DataType="Int32" Description="发货评分" />
        <Column Name="ServiceScore" DataType="Int32" Description="服务评分" />
        <Column Name="Content" DataType="String" Length="255" Description="店铺信誉评价内容" />
        <Column Name="IsAnonymous" DataType="Boolean" DefaultValue="False" Description="是否匿名评价" />
        <Column Name="AddTime" DataType="Int64" Description="评价时间" />
        <Column Name="StoreId" DataType="Int64" Description="店铺Id" />
        <Column Name="StoreName" DataType="String" Length="100" Nullable="False" Description="店铺名称" />
        <Column Name="FromMemberId" DataType="Int32" Description="评价人Id" />
        <Column Name="FromMemberName" DataType="String" Description="评价人名称" />
        <Column Name="State" DataType="Int32" DefaultValue="0" Description="评价状态 0正常 1禁止显示" />
        <Column Name="Remark" DataType="String" Length="255" Description="管理员处理备注" />
        <Column Name="Explain" DataType="String" Length="255" Description="解释内容" />
        <Column Name="Image1" DataType="String" Length="255" Description="晒单图片1" />
        <Column Name="Image2" DataType="String" Length="255" Description="晒单图片2" />
        <Column Name="Image3" DataType="String" Length="255" Description="晒单图片3" />
        <Column Name="Image4" DataType="String" Length="255" Description="晒单图片4" />
        <Column Name="Image5" DataType="String" Length="255" Description="晒单图片5" />
        <Column Name="Image6" DataType="String" Length="255" Description="晒单图片6" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="SkuId" />
        <Index Columns="GoodsId" />
        <Index Columns="StoreId" />
        <Index Columns="FromMemberId" />
      </Indexes>
    </Table>
    <Table Name="TelAreaCode" TableName="DH_TelAreaCode" Description="电话区号表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AreaCode" DataType="String" Description="区号" />
        <Column Name="Description" DataType="String" Description="描述" />
        <Column Name="CountryId" DataType="String" Description="所属国家" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="AreaCode" />
      </Indexes>
    </Table>
    <Table Name="AdvPosition" TableName="DH_AdvPosition" Description="广告位置表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="广告位置名称" />
        <Column Name="Introduction" DataType="String" Description="广告位简介" />
        <Column Name="Enabled" DataType="Boolean" DefaultValue="True" Description="是否启用" />
        <Column Name="Width" DataType="Int32" DefaultValue="0" Description="广告位高度" />
        <Column Name="Height" DataType="Int32" DefaultValue="0" Description="广告位宽度" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="Name" />
      </Indexes>
    </Table>
    <Table Name="Advertising" TableName="DH_Advertising" Description="广告表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="APId" DataType="Int32" Description="广告位Id" />
        <Column Name="Title" DataType="String" Master="True" Description="广告位描述" />
        <Column Name="Link" DataType="String" Description="广告链接地址" />
        <Column Name="PicturePath" DataType="String" Description="广告图片地址" />
        <Column Name="StartTime" DataType="Int64" Description="广告开始时间" />
        <Column Name="EndTime" DataType="Int64" Description="广告结束时间" />
        <Column Name="Sort" DataType="Int32" DefaultValue="255" Description="排序" />
        <Column Name="Enabled" DataType="Boolean" DefaultValue="True" Description="是否启用" />
        <Column Name="ClickNumber" DataType="Int32" DefaultValue="0" Description="点击次数" />
        <Column Name="BgColor" DataType="String" Description="广告背景颜色" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="APId" />
        <Index Columns="Title" />
      </Indexes>
    </Table>
    <Table Name="AdvertisingLan" TableName="DH_AdvertisingLan" Description="广告翻译表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="AId" DataType="Int32" Description="关联广告Id" />
        <Column Name="LId" DataType="Int32" Description="语言Id" />
        <Column Name="Title" DataType="String" Master="True" Description="广告位描述" />
        <Column Name="Link" DataType="String" Description="广告链接地址" />
        <Column Name="PicturePath" DataType="String" Description="广告图片地址" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="AId,LId" Unique="True" />
        <Index Columns="Title" />
      </Indexes>
    </Table>
    <Table Name="GoodsBrowse" TableName="DH_GoodsBrowse" Description="商品浏览历史表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="商品ID" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="SkuId" DataType="Int64" Description="SkuID" />
        <Column Name="BuyerId" DataType="Int32" Description="买家ID" />
        <Column Name="SId" DataType="String" Description="SID" />
        <Column Name="BrowseTime" DataType="Int32" Description="浏览时间" />
        <Column Name="CId" DataType="Int64" Description="商品分类" />
        <Column Name="CId1" DataType="Int64" Description="商品一级分类" />
        <Column Name="CId2" DataType="Int64" Description="商品二级分类" />
        <Column Name="CId3" DataType="Int64" Description="商品三级分类" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="BuyerId" />
        <Index Columns="SId" />
      </Indexes>
    </Table>
    <Table Name="ArrivalNotice" TableName="DH_ArrivalNotice" Description="预约到货通知表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="StoreId" DataType="Int64" Description="店铺ID" />
        <Column Name="GoodsId" DataType="Int64" Description="商品ID" />
        <Column Name="SkuID" DataType="Int64" Description="SkuID" />
        <Column Name="MaterialId" DataType="Int64" Description="物料编号" />
        <Column Name="Email" DataType="String" Description="通知邮箱" />
        <Column Name="ReciverTwoLetterIsoCode" DataType="String" Description="两个字母ISO代码" />
        <Column Name="Address" DataType="String" RawType="text" Length="0" Description="地址" />
        <Column Name="LId" DataType="Int32" Description="语言ID" />
        <Column Name="CurrencyCode" DataType="String" Description="货币编码" />
        <Column Name="Status" DataType="Int32" DefaultValue="0" Description="状态" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
      <Indexes>
        <Index Columns="GoodsId" />
        <Index Columns="MaterialId" />
        <Index Columns="CreateUserID,MaterialId" Unique="True" />
      </Indexes>
    </Table>
    <Table Name="StoreClass" TableName="DH_StoreClass" Description="店铺分类表" IgnoreNameCase="False">
      <Columns>
        <Column Name="Id" DataType="Int32" Identity="True" PrimaryKey="True" Description="编号" />
        <Column Name="Name" DataType="String" Master="True" Description="名称" />
        <Column Name="Bail" DataType="Decimal" Description="保证金" />
        <Column Name="Sort" DataType="Int32" Description="排序" />
        <Column Name="CreateUser" DataType="String" Description="创建者" />
        <Column Name="CreateUserID" DataType="Int32" Description="创建者" />
        <Column Name="CreateTime" DataType="DateTime" Description="创建时间" />
        <Column Name="CreateIP" DataType="String" Description="创建地址" />
        <Column Name="UpdateUser" DataType="String" Description="更新者" />
        <Column Name="UpdateUserID" DataType="Int32" Description="更新者" />
        <Column Name="UpdateTime" DataType="DateTime" Description="更新时间" />
        <Column Name="UpdateIP" DataType="String" Description="更新地址" />
      </Columns>
    </Table>
  </Tables>
</EntityModel>