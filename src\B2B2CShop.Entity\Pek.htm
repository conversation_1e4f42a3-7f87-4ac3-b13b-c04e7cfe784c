﻿<style>
    table {
        border-collapse: collapse;
        border: 1px solid;
        border-color: rgb(211, 202, 221);
    }

    table thead,
    table tr {
        border-top-width: 1px;
        border-top-style: solid;
        border-top-color: rgb(211, 202, 221);
    }

    table {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: rgb(211, 202, 221);
    }

    table td,
    table th {
        padding: 5px 10px;
        font-size: 14px;
        font-family: Verdana;
        color: rgb(95, 74, 121);
    }

    table tr:nth-child(even) {
        background: rgb(223, 216, 232)
    }

    table tr:nth-child(odd) {
        background: #FFF
    }
</style>
<h3>用户扩展表（DH_UserEx）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>用户Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>卖家用户表（DH_Seller）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>卖家用户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SellerGroupId</td>
            <td>卖家组ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsAdmin</td>
            <td>是否管理员</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LastLoginTime</td>
            <td>最后登录时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>卖家用户组表（DH_SellerGroup）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>卖家组名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Limits</td>
            <td>卖家组权限</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SmtLimits</td>
            <td>卖家组消息权限范围</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>相册表（DH_SnsAlbumClass）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>相册名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Des</td>
            <td>相册描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>相册排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Cover</td>
            <td>相册封面</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefault</td>
            <td>是否为买家秀相册</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺等级表（DH_StoreGrade）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>店铺等级名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsLimit</td>
            <td>允许发布商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AlbumLimit</td>
            <td>允许发布商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SpaceLimit</td>
            <td>允许上传空间大小</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>,单位MB</td>
        </tr>

        <tr>
            <td>TemplateNumber</td>
            <td>店铺等级选择店铺模板套数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Template</td>
            <td>店铺等级模板内容</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Price</td>
            <td>店铺等级费用</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Confirm</td>
            <td>店铺等级审核</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>店铺等级申请说明</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>店铺等级排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>会员积分日志表（DH_PointsLog）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AdminId</td>
            <td>管理员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdminName</td>
            <td>管理员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Points</td>
            <td>积分数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>,负数为扣除</td>
        </tr>

        <tr>
            <td>Desc</td>
            <td>积分操作描述</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Stage</td>
            <td>积分操作阶段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>regist注册,login登录,comments商品评论,order订单消费,system系统调整,pointorder礼品兑换,exchange积分兑换,signin签到,inviter推荐注册,rebate推荐返利</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>积分添加时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>积分金钱兑换表（DH_PointsMoney）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Points</td>
            <td>积分</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Moneys</td>
            <td>金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>帮助分类（DH_HelpsCategory）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>分类名称</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ParentId</td>
            <td>所属父级Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ParentIdList</td>
            <td>父级Id集合</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Level</td>
            <td>当前层级</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DisplayOrder</td>
            <td>排序</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>帮助分类翻译表（DH_HelpsCategoryLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>HId</td>
            <td>帮助分类Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>分类名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>帮助（DH_Helps）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>HId</td>
            <td>帮助分类Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Url</td>
            <td>帮助文章跳转链接</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Show</td>
            <td>帮助文章是否显示</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为否，1为是，默认为1</td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>帮助文章排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>帮助文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Hits</td>
            <td>浏览次数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>帮助翻译（DH_HelpsLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>HId</td>
            <td>帮助文章Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>帮助文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品相册（DH_AlbumCategory）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>相册名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>相册描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>相册排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Cover</td>
            <td>相册封面</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefault</td>
            <td>是否为默认相册</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品相册图片（DH_AlbumPic）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>图片名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Tag</td>
            <td>图片标签</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AId</td>
            <td>相册Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Cover</td>
            <td>图片路径</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Size</td>
            <td>图片大小</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Spec</td>
            <td>图片规格</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品图片表（DH_GoodsImages）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>商品图片自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCommonId</td>
            <td>商品公共ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>SKUID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ColorId</td>
            <td>颜色规格值ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ImageUrl</td>
            <td>商品图片</td>
            <td>String</td>
            <td>1000</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>商品图片排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefault</td>
            <td>商品图片默认主图</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>1是，0否</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品图片翻译表（DH_GoodsImagesLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>商品图片翻译自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCommonId</td>
            <td>商品公共ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>SKUID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ColorId</td>
            <td>颜色规格值ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ImageUrl</td>
            <td>商品图片</td>
            <td>String</td>
            <td>1000</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>商品图片排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefault</td>
            <td>商品图片默认主图</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>1是，0否</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺数据表（DH_Store）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GradeId</td>
            <td>店铺等级ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员用户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SellerName</td>
            <td>店主卖家用户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClassId</td>
            <td>店铺分类ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CompanyName</td>
            <td>店铺公司名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryCode</td>
            <td>所在国家两字母ISO代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RegionId</td>
            <td>地区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RegionCode</td>
            <td>地区代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AreaInfo</td>
            <td>地区名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Address</td>
            <td>店铺地址</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Zip</td>
            <td>邮政编码</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>店铺状态</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:0关闭，1开启，2审核中</td>
        </tr>

        <tr>
            <td>CloseInfo</td>
            <td>店铺关闭原因</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>店铺排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AddTime</td>
            <td>店铺时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EndTime</td>
            <td>店铺关闭时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Logo</td>
            <td>店铺LOGO</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Banner</td>
            <td>店铺Banner</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Avatar</td>
            <td>店铺头像</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PyAbbr</td>
            <td>店铺拼音简写</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keywords</td>
            <td>店铺SEO关键字</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>店铺SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>QQ</td>
            <td>店铺QQ</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>WX</td>
            <td>店铺微信</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Skype</td>
            <td>店铺Skype</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>WhatsApp</td>
            <td>店铺WhatsApp</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Phone</td>
            <td>商家电话</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MainBusiness</td>
            <td>主营商品</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Recommend</td>
            <td>是否推荐</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Theme</td>
            <td>店铺当前主题</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Credit</td>
            <td>店铺信用</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DescCredit</td>
            <td>描述相符度分数</td>
            <td>Double</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ServiceCredit</td>
            <td>服务态度分数</td>
            <td>Double</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DeliveryCredit</td>
            <td>发货速度分数</td>
            <td>Double</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Collect</td>
            <td>店铺收藏数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Slide</td>
            <td>店铺幻灯片</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SlideUrl</td>
            <td>店铺幻灯片链接</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Seal</td>
            <td>店铺印章</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PrintExplain</td>
            <td>打印订单页面下方说明文字</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sales</td>
            <td>店铺销量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PreSales</td>
            <td>售前客服</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AfterSales</td>
            <td>售后客服</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>WorkingTime</td>
            <td>工作时间</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FreePrice</td>
            <td>超出该金额免运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>大于0才表示该值有效</td>
        </tr>

        <tr>
            <td>DecorationSwitch</td>
            <td>店铺装修开关</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DecorationOnly</td>
            <td>开启店铺装修时</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>仅显示店铺装修</td>
        </tr>

        <tr>
            <td>DecorationImageCount</td>
            <td>店铺装修相册图片数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PlatformStore</td>
            <td>是否自营店铺</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BindAllGc</td>
            <td>自营店是否绑定全部分类</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VrCodePrefix</td>
            <td>商家兑换码前缀</td>
            <td>String</td>
            <td>3</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BaoZh</td>
            <td>保证服务开关</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>QTian</td>
            <td>7天退换</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ZhPing</td>
            <td>正品保障</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ErXiaoShi</td>
            <td>两小时发货</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TuiHuo</td>
            <td>退货承诺</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShiYong</td>
            <td>试用中心</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShiTi</td>
            <td>实体验证</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>XiaoXie</td>
            <td>消协保证</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>HuoDaoFK</td>
            <td>货到付款</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FreeTime</td>
            <td>商家配送时间</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Longitude</td>
            <td>经度</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Latitude</td>
            <td>纬度</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MbTitleImg</td>
            <td>手机店铺背景图</td>
            <td>String</td>
            <td>150</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MbSliders</td>
            <td>手机店铺轮播图</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliverRegion</td>
            <td>店铺默认配送区域</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MgDiscount</td>
            <td>序列化会员等级折扣(店铺)</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MgDiscountState</td>
            <td>店铺是否开启序列化会员等级折扣</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AvaliableDeposit</td>
            <td>店铺已缴保证金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FreezeDeposit</td>
            <td>店铺审核保证金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PayableDeposit</td>
            <td>店铺应缴保证金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AvaliableMoney</td>
            <td>店铺可用金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FreezeMoney</td>
            <td>店铺冻结金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>KdnIfOpen</td>
            <td>快递鸟开启</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>KdnId</td>
            <td>快递鸟-用户ID</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>KdnKey</td>
            <td>快递鸟-APIkey</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td>快递鸟-API key</td>
        </tr>

        <tr>
            <td>KdnPrinter</td>
            <td>快递鸟-打印机</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TrackIfOpen</td>
            <td>17Track开启</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TrackId</td>
            <td>17Track-用户ID</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TrackKey</td>
            <td>17Track-APIkey</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>17Track-API key</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺信息扩展表（DH_StoreExtend）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PriceRange</td>
            <td>店铺统计设置的商品价格区间</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>OrderPriceRange</td>
            <td>店铺统计设置的订单价格区间</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺入住表（DH_StoreJoinIn）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员用户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Type</td>
            <td>店铺类型</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:0公司，1个人</td>
        </tr>

        <tr>
            <td>CompanyName</td>
            <td>公司名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryCode</td>
            <td>所在国家两字母ISO代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ProvinceId</td>
            <td>所在地省ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ProvinceCode</td>
            <td>地区代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CityId</td>
            <td>所在地市ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountyId</td>
            <td>所在地县_区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>所在地县/区ID</td>
        </tr>

        <tr>
            <td>Address</td>
            <td>公司地址</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AddressDetail</td>
            <td>公司详细地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RegisteredCapital</td>
            <td>注册资金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ContactsName</td>
            <td>联系人姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ContactsPhone</td>
            <td>联系人电话</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ContactsEmail</td>
            <td>联系人邮箱</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PersonalIdentityNumber</td>
            <td>个人证件号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceNumber</td>
            <td>营业执照号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceProvinceId</td>
            <td>营业执所在省ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceCityId</td>
            <td>营业执所在市ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceCountyId</td>
            <td>营业执所在县_区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>营业执所在县/区ID</td>
        </tr>

        <tr>
            <td>BusinessLicenceAddress</td>
            <td>营业执所在地</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceStart</td>
            <td>营业执照有效期开始</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceEnd</td>
            <td>营业执照有效期结束</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessSphere</td>
            <td>法定经营范围</td>
            <td>String</td>
            <td>1000</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BusinessLicenceNumberElectronic</td>
            <td>营业执照电子版</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BankAccountName</td>
            <td>银行开户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BankAccountNumber</td>
            <td>公司银行账号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BankName</td>
            <td>开户银行支行名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BankAddress</td>
            <td>开户银行所在地</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsSettlementAccount</td>
            <td>开户行账号是否为结算账号1-开户行就是结算账号2-独立的计算账号</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>开户行账号是否为结算账号 1-开户行就是结算账号 2-独立的计算账号</td>
        </tr>

        <tr>
            <td>SettlementBankAccountName</td>
            <td>结算银行开户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SettlementBankAccountNumber</td>
            <td>结算公司银行账号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SettlementBankName</td>
            <td>结算开户银行支行名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SettlementBankAddress</td>
            <td>结算开户银行所在地</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SellerName</td>
            <td>卖家帐号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ClassIds</td>
            <td>店铺分类编号集合</td>
            <td>String</td>
            <td>1000</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ClassNames</td>
            <td>店铺分类名称集合</td>
            <td>String</td>
            <td>1000</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Longitude</td>
            <td>经度</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Latitude</td>
            <td>纬度</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>申请状态10-已提交申请11-缴费完成20-审核成功30-审核失败31-缴费审核失败40-审核通过开店</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>申请状态 10-已提交申请 11-缴费完成  20-审核成功 30-审核失败 31-缴费审核失败 40-审核通过开店</td>
        </tr>

        <tr>
            <td>Message</td>
            <td>管理员审核信息</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Year</td>
            <td>开店时长(年)</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreGradeName</td>
            <td>店铺等级名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreGradeId</td>
            <td>店铺等级编号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SgInfo</td>
            <td>店铺等级下的收费等信息</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClassName</td>
            <td>店铺分类名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClassId</td>
            <td>店铺分类编号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClass_Bail</td>
            <td>店铺分类保证金</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClassCommisRates</td>
            <td>分类佣金比例</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PayingMoneyCertificate</td>
            <td>付款凭证</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PayingMoneyCertificateExplain</td>
            <td>付款凭证说明</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PayingAmount</td>
            <td>付款金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PaySn</td>
            <td>支付单号</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PaymentCode</td>
            <td>支付方式</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TradeSn</td>
            <td>第三方支付接口交易号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RCB_Amount</td>
            <td>充值卡支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PDAmount</td>
            <td>预存款支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>时区表（DH_ZoneTime）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Times</td>
            <td>时区</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>相对于UTC0</td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>单页文章（DH_SingleArticle）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Url</td>
            <td>文章跳转链接</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Show</td>
            <td>文章是否显示</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>默认为是</td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>文章排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Code</td>
            <td>调用别名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td>不可重复</td>
        </tr>

        <tr>
            <td>Name</td>
            <td>文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MobileContent</td>
            <td>内容(移动端)</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Summary</td>
            <td>简介</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ViewName</td>
            <td>模板名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Hits</td>
            <td>查看次数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>单页文章翻译（DH_SingleArticleLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SId</td>
            <td>单页文章Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MobileContent</td>
            <td>内容(移动端)</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Summary</td>
            <td>简介</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>其他消息模板（DH_OtherMsgTpl）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MName</td>
            <td>模板名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MTitle</td>
            <td>模板标题</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MCode</td>
            <td>模板调用代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MContent</td>
            <td>模板内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SmsTplId</td>
            <td>短信模板Id</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>其他消息模板翻译表（DH_OtherMsgTplLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OId</td>
            <td>关联其他消息模板Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>关联所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MName</td>
            <td>模板名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MTitle</td>
            <td>模板标题</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MContent</td>
            <td>模板内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SmsTplId</td>
            <td>短信模板Id</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>品牌表（DH_Brand）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>品牌名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Initial</td>
            <td>品牌首字母</td>
            <td>String</td>
            <td>1</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>品牌图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BClass</td>
            <td>类别名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>品牌排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Recommend</td>
            <td>品牌推荐</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Apply</td>
            <td>品牌申请</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为申请中，1为通过，默认为1，申请功能是会员使用</td>
        </tr>

        <tr>
            <td>ShowType</td>
            <td>品牌展示类型0表示图片1表示文字</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>品牌展示类型 0表示图片 1表示文字</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品类型表（DH_GoodType）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>类型名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>类型排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassName</td>
            <td>商品分类名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品类型与品牌对应表（DH_GoodTypeBrand）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodTypeId</td>
            <td>类型编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BrandId</td>
            <td>品牌编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品类型与规格对应表（DH_GoodTypeSpec）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodTypeId</td>
            <td>类型编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodSpecId</td>
            <td>规格编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品属性表（DH_GoodAttribute）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>属性名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodTypeId</td>
            <td>所属类型编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AttrValue</td>
            <td>属性值</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Show</td>
            <td>属性是否显示</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>属性排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品属性值表（DH_GoodAttributeValue）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>属性值名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodAttributeId</td>
            <td>所属属性编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodTypeId</td>
            <td>所属类型编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>属性值排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品规格表（DH_GoodSpec）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>规格名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>规格排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassName</td>
            <td>商品分类名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品分类表（DH_GoodsClass）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>商品分类名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image</td>
            <td>商品分类图</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodTypeId</td>
            <td>所属类型编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ParentId</td>
            <td>商品分类上级ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CommisRate</td>
            <td>商品分类佣金比例</td>
            <td>Double</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>商品分类排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsVirtual</td>
            <td>是否允许发布虚拟商品</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Title</td>
            <td>SEO商品分类名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keywords</td>
            <td>SEO商品分类关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO商品分类描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Show</td>
            <td>商品分类前台显示</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AliasName</td>
            <td>商品分类别名</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ClassIds</td>
            <td>推荐子级分类</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BrandIds</td>
            <td>推荐的品牌</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>分类图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Adv1</td>
            <td>广告图1</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Adv1Link</td>
            <td>广告1链接</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Adv2</td>
            <td>广告图2</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Adv2Link</td>
            <td>广告2链接</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Enable</td>
            <td>是否启用1启用0禁用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否启用1启用 0禁用</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品分类翻译表（DH_GoodsClassLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GId</td>
            <td>关联商品分类Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>关联所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>商品分类名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AliasName</td>
            <td>商品分类别名</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image</td>
            <td>商品分类图</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Title</td>
            <td>SEO商品分类名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keywords</td>
            <td>SEO商品分类关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO商品分类描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>预存款充值表（DH_PdRecharge）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sn</td>
            <td>记录唯一标示</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Amount</td>
            <td>充值金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PCode</td>
            <td>支付方式</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TradeSn</td>
            <td>第三方支付接口交易号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>支付状态</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否支付</td>
        </tr>

        <tr>
            <td>PayTime</td>
            <td>支付时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建用户</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>线下汇款（DH_Remittance）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RName</td>
            <td>汇款户名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Amount</td>
            <td>汇款金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RNum</td>
            <td>汇款账号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RBankName</td>
            <td>汇款银行</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ImgUrl</td>
            <td>汇款凭证图片路径</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>审核状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>审核状态 0:审核中 1:审核成功 2:审核失败 3:取消审核</td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>审核备注</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AuditUId</td>
            <td>审核人Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AuditDateTime</td>
            <td>审核时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>预付款变更日志表（DH_PdLog）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdminId</td>
            <td>管理员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdminName</td>
            <td>管理员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PdType</td>
            <td>order_pay下单支付预存款</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td>,order_freeze下单冻结预存款,order_cancel取消订单解冻预存款,order_comb_pay下单支付被冻结的预存款,recharge充值,cash_apply申请提现冻结预存款,cash_pay提现成功,cash_del取消提现申请，解冻预存款,refund退款,sys_add_money管理员调节增加余额,sys_del_money管理员调节减少余额,order_points积分充值,sys_thaw_money管理员调整解冻余额,sys_freeze_money管理员调整冻结余额</td>
        </tr>

        <tr>
            <td>Amount</td>
            <td>可用金额变更0</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:未变更</td>
        </tr>

        <tr>
            <td>FreezeAmount</td>
            <td>冻结金额变更0</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:未变更</td>
        </tr>

        <tr>
            <td>Balance</td>
            <td>可用金额总金额(总额)</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FreezeBalance</td>
            <td>冻结金额总金额(总额)</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Desc</td>
            <td>变更描述</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>变更者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>变更者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>变更添加时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>友情链接（DH_FriendLinks）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>友情链接标题</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FType</td>
            <td>类型</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为文字，1为图片</td>
        </tr>

        <tr>
            <td>JumpType</td>
            <td>类型</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为直接跳转，1为Jump跳转</td>
        </tr>

        <tr>
            <td>Url</td>
            <td>友情链接地址</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>友情链接图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>友情链接排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>友情链接翻译（DH_FriendLinksLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FId</td>
            <td>友情链接Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>友情链接标题</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FType</td>
            <td>类型</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为文字，1为图片。</td>
        </tr>

        <tr>
            <td>JumpType</td>
            <td>类型</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>0为直接跳转，1为Jump跳转</td>
        </tr>

        <tr>
            <td>Url</td>
            <td>友情链接地址</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>友情链接图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>友情链接排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>文章分类（DH_ArticleCategory）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>分类名称</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ParentId</td>
            <td>所属父级Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ParentIdList</td>
            <td>父级Id集合</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Level</td>
            <td>当前层级</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DisplayOrder</td>
            <td>排序</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>JumpUrl</td>
            <td>跳转Url</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ViewName</td>
            <td>模板名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>InfoViewName</td>
            <td>详情模板名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>文章分类翻译表（DH_ArticleCategoryLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AId</td>
            <td>文章分类Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>分类名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>JumpUrl</td>
            <td>跳转Url</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ViewName</td>
            <td>模板名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>InfoViewName</td>
            <td>详情模板名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>文章（DH_Article）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MId</td>
            <td>产品型号Id</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AId</td>
            <td>文章分类Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Url</td>
            <td>文章跳转链接</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Show</td>
            <td>文章是否显示</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>默认为1</td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>文章排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Summary</td>
            <td>简介</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>文章翻译（DH_ArticleLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AId</td>
            <td>文章Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>文章标题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Summary</td>
            <td>简介</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Pic</td>
            <td>文章主图</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Keys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>意见反馈（DH_FeedBack）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Phone</td>
            <td>电话</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Mail</td>
            <td>邮箱</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CompanyName</td>
            <td>公司名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Theme</td>
            <td>主题</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Country</td>
            <td>国家</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>反馈内容</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FType</td>
            <td>1</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:手机端 2:PC端</td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>快递物流公司（DH_Express）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Code</td>
            <td>编码</td>
            <td>String</td>
            <td>16</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Status</td>
            <td>状态</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Letter</td>
            <td>首字母</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>OType</td>
            <td>1</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:常用2:不常用</td>
        </tr>

        <tr>
            <td>Url</td>
            <td>网址</td>
            <td>String</td>
            <td>80</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>国际快递物流公司（DH_InternationalExpress）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Key</td>
            <td>编码</td>
            <td>String</td>
            <td>16</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Status</td>
            <td>状态</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Letter</td>
            <td>首字母</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>17Track的用名称的首字母</td>
        </tr>

        <tr>
            <td>OType</td>
            <td>1</td>
            <td>Int16</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:常用2:不常用</td>
        </tr>

        <tr>
            <td>Url</td>
            <td>网址</td>
            <td>String</td>
            <td>80</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Tel</td>
            <td>电话</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Email</td>
            <td>邮箱</td>
            <td>String</td>
            <td>80</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TwoLetterIsoCode</td>
            <td>国家二字代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ParameterType</td>
            <td>参数类型</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ParameterExample</td>
            <td>参数示例</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Mandatory</td>
            <td>是否必填</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>国际快递物流公司翻译（DH_InternationalExpressLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EId</td>
            <td>国际快递物流公司Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>国家快递物流（DH_ExpressCountry）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TwoLetterIsoCode</td>
            <td>国家二字代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Express</td>
            <td>快递公司ID的组合</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商家物料（DH_MerchantMaterial）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Quantity</td>
            <td>总数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TemporaryQuantity</td>
            <td>暂扣数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>默认启用</td>
        </tr>

        <tr>
            <td>Status</td>
            <td>状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>状态 0:未审核,1:已审核 2:审核不通过</td>
        </tr>

        <tr>
            <td>Images</td>
            <td>上传图片</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Cause</td>
            <td>审核原因</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Auditor</td>
            <td>审核者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AuditTime</td>
            <td>审核时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>备注</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Weight</td>
            <td>重量</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Volume</td>
            <td>体积</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>物料入库（DH_MaterialIncoming）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MerchantMaterialId</td>
            <td>商家物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InvolvesCosts</td>
            <td>涉及费用</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Quantity</td>
            <td>数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>备注</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>物料出库（DH_MaterialOutbound）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderId</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MerchantMaterialId</td>
            <td>商家物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InvolvesCosts</td>
            <td>涉及费用</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Quantity</td>
            <td>数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>备注</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>仓库表（DH_WareHouse）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>仓库名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Code</td>
            <td>仓库编码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td title="唯一索引">UQ</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryCode</td>
            <td>所在国家两字母ISO代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RegionId</td>
            <td>地区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RegionCode</td>
            <td>地区代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Address</td>
            <td>仓库地址</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>联系人姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UMobile</td>
            <td>联系人电话</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>默认启用</td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>备注</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>仓库翻译（DH_WareHouseLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WId</td>
            <td>仓库Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>仓库名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Address</td>
            <td>仓库地址</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>仓库物料表（DH_WareHouseMaterial）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MerchantMaterialId</td>
            <td>商家物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Quantity</td>
            <td>数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>买家地址表（DH_BuyerAddress）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AddressType</td>
            <td>0为收货地址</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>1为发票地址，2为共用地址</td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RealName</td>
            <td>收货人姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TwoLetterIsoCode</td>
            <td>两个字母ISO代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RegionId</td>
            <td>地区ID_中国省ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>地区ID/中国省ID</td>
        </tr>

        <tr>
            <td>CityId</td>
            <td>城市ID_中国市ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>城市ID/中国市ID</td>
        </tr>

        <tr>
            <td>AreaId</td>
            <td>区县ID_中国区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>区县ID/中国区ID</td>
        </tr>

        <tr>
            <td>AreaInfo</td>
            <td>国省市区信息</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AddressDetail</td>
            <td>详细地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ZipCode</td>
            <td>邮编</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AreaCodeId</td>
            <td>区号Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaCode</td>
            <td>区号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Phone</td>
            <td>电话号码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Email</td>
            <td>电子邮箱</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CompanyName</td>
            <td>公司名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TaxIdType</td>
            <td>税号类型</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TaxId</td>
            <td>税号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefaultDelivery</td>
            <td>是否默认收货地址</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefaultInvoice</td>
            <td>是否默认发票地址</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ChainId</td>
            <td>门店ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Longitude</td>
            <td>经度</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Latitude</td>
            <td>纬度</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>卖家发货地址表（DH_DeliveryAddress）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>发货地址ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SellerName</td>
            <td>联系人</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所在国家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TwoLetterIsoCode</td>
            <td>两个字母ISO代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RegionId</td>
            <td>地区ID_中国省ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>地区ID/中国省ID</td>
        </tr>

        <tr>
            <td>CityId</td>
            <td>城市ID_中国市ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>城市ID/中国市ID</td>
        </tr>

        <tr>
            <td>AreaId</td>
            <td>区县ID_中国区ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>区县ID/中国区ID</td>
        </tr>

        <tr>
            <td>AreaInfo</td>
            <td>国省市区信息</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AddressDetail</td>
            <td>详细地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Phone</td>
            <td>发货电话</td>
            <td>String</td>
            <td>40</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CompanyName</td>
            <td>发货公司</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsDefault</td>
            <td>是否默认发货地址</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>货运账户表（DH_FreightAccount）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ExpressId</td>
            <td>快递物流公司Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ExpressCode</td>
            <td>快递物流公司Code</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>目前仅使用DHL、FedEx、UPS、SFExpress</td>
        </tr>

        <tr>
            <td>AccountNumber</td>
            <td>账号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>订单表（DH_Order）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>订单自增id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderSn</td>
            <td>订单编号</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PaySn</td>
            <td>支付单号</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>卖家店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerName</td>
            <td>买家姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerEmail</td>
            <td>买家电子邮箱</td>
            <td>String</td>
            <td>80</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ChainId</td>
            <td>门店ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AddTime</td>
            <td>订单生成时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PaymentCode</td>
            <td>支付方式名称代码</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PaymentTime</td>
            <td>支付(付款)时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TradeNo</td>
            <td>第三方平台交易号</td>
            <td>String</td>
            <td>35</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FinnshedTime</td>
            <td>订单完成时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyCode</td>
            <td>订单货币代码</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyRate</td>
            <td>订单货币汇率</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolLeft</td>
            <td>左标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolRight</td>
            <td>右标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurGoodsAmount</td>
            <td>结算货币商品总价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsAmount</td>
            <td>商品总价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurOrderAmount</td>
            <td>结算货币订单总价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderAmount</td>
            <td>订单总价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RcbAmount</td>
            <td>充值卡支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PdAmount</td>
            <td>预存款支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PresellDepositAmount</td>
            <td>定金金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PresellRcbAmount</td>
            <td>定金充值卡支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PresellPdAmount</td>
            <td>定金预存款支付金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PresellTradeNo</td>
            <td>定金第三方交易号</td>
            <td>String</td>
            <td>35</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PresellPaymentCode</td>
            <td>定金支付方式名称代码</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PresellEndTime</td>
            <td>预售结束时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurShippingFee</td>
            <td>结算货币运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShippingFee</td>
            <td>运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EvaluationState</td>
            <td>评价状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>评价状态 0：未评价 1：已评价 2:已过期</td>
        </tr>

        <tr>
            <td>OrderState</td>
            <td>订单状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>：0:已取消 10:未付款 14:待付定金 15:待付尾款 20:待发货 30:已发货 35:待自提 40:已收货</td>
        </tr>

        <tr>
            <td>RefundState</td>
            <td>退款状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>退款状态 0:无退款 1:部分退款 2:全部退款</td>
        </tr>

        <tr>
            <td>OrderRefundLockState</td>
            <td>锁定状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:0:正常,大于0:锁定</td>
        </tr>

        <tr>
            <td>RefundAmount</td>
            <td>退款金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>DeleteState</td>
            <td>删除状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>删除状态 0:未删除 1:放入回收站 2:彻底删除</td>
        </tr>

        <tr>
            <td>DelayTime</td>
            <td>延迟时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>,默认为0</td>
        </tr>

        <tr>
            <td>OrderFrom</td>
            <td>订单来源</td>
            <td>String</td>
            <td>15</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ShippingCode</td>
            <td>订单物流单号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>OrderType</td>
            <td>订单类型</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>订单信息扩展表（DH_OrderCommon）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShippingTime</td>
            <td>配送时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShippingExpressId</td>
            <td>配送公司ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EvaluationTime</td>
            <td>评价时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EvalsellerState</td>
            <td>卖家是否已评价买家0_1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>卖家是否已评价买家 0/1</td>
        </tr>

        <tr>
            <td>EvalsellerTime</td>
            <td>卖家评价买家的时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderMessage</td>
            <td>订单留言</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>OrderPointscount</td>
            <td>订单赠送积分</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VoucherPrice</td>
            <td>店铺代金券面额</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VoucherCode</td>
            <td>店铺代金券编码</td>
            <td>String</td>
            <td>32</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MallvoucherPrice</td>
            <td>平台代金券面额</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MallvoucherCode</td>
            <td>平台代金券编码</td>
            <td>String</td>
            <td>32</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliverExplain</td>
            <td>订单发货备注</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DaddressId</td>
            <td>发货仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverName</td>
            <td>收货人姓名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ZipCode</td>
            <td>收货人邮编</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Phone</td>
            <td>收货人电话号码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Email</td>
            <td>收货人电子邮箱</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverInfo</td>
            <td>收货人其它信息</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverCountryId</td>
            <td>收货人国家级ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverTwoLetterIsoCode</td>
            <td>两个字母ISO代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverProvinceId</td>
            <td>收货人省级ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverCityId</td>
            <td>收货人市级ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InvoiceInfo</td>
            <td>订单发票信息</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PromotionInfo</td>
            <td>订单促销信息备注</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ActualSymbolLeft</td>
            <td>实际支付货币左标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ActualCurrencyCode</td>
            <td>实际支付货币代码</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ActualShippingFee</td>
            <td>实际支付运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ActualCurrencyRate</td>
            <td>实际支付货币汇率</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>订单商品表（DH_OrderGoods）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>订单商品表自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderId</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsName</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SKUId</td>
            <td>SKUId</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MaterialId</td>
            <td>物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyCode</td>
            <td>订单货币代码</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyRate</td>
            <td>订单货币汇率</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolLeft</td>
            <td>左标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolRight</td>
            <td>右标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurGoodsPrice</td>
            <td>结算货币商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsNum</td>
            <td>商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurGoodsPayPrice</td>
            <td>商品实际成交价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPayPrice</td>
            <td>商品实际成交价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsType</td>
            <td>1默认2抢购商品3限时折扣商品4组合套装5赠品6拼团7会员等级折扣8砍价9批发10预售</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PromotionsId</td>
            <td>促销活动ID（抢购ID_限时折扣ID_优惠套装ID）与GoodsType搭配使用</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>促销活动ID（抢购ID/限时折扣ID/优惠套装ID）与GoodsType搭配使用</td>
        </tr>

        <tr>
            <td>CommisRate</td>
            <td>佣金比例</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GcId</td>
            <td>商品最底级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>订单支付表（DH_OrderPay）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>订单支付自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PaySn</td>
            <td>支付单号</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PayOkSn</td>
            <td>支付成功单号(用于退款)</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ApiPaystate</td>
            <td>0默认未支付1已支付(只有第三方支付接口通知到时才会更改此状态)</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品公共表（DH_GoodsCommon）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdvWord</td>
            <td>商品广告词</td>
            <td>String</td>
            <td>150</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_1</td>
            <td>一级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_2</td>
            <td>二级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_3</td>
            <td>三级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClassName</td>
            <td>商品分类名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SpecName</td>
            <td>规格名称</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SpecValue</td>
            <td>规格值</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BrandId</td>
            <td>商品品牌ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BrandName</td>
            <td>商品品牌名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TypeId</td>
            <td>类型ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品主图</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsVideoName</td>
            <td>商品视频名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsAttr</td>
            <td>商品属性</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsBody</td>
            <td>商品内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MobileBody</td>
            <td>手机端商品描述</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsState</td>
            <td>商品状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品状态 0:下架 1:正常 10:违规（禁售）</td>
        </tr>

        <tr>
            <td>GoodsSteteRemark</td>
            <td>违规原因</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsVerify</td>
            <td>商品审核1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品审核 1:通过 0:未通过 10:审核中</td>
        </tr>

        <tr>
            <td>GoodsVerifyRemark</td>
            <td>审核失败原因</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsLock</td>
            <td>商品锁定0未锁</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品锁定 0未锁，1已锁</td>
        </tr>

        <tr>
            <td>GoodsAddTime</td>
            <td>商品添加时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsShelftime</td>
            <td>上架时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSpecName</td>
            <td>规格名称序列化（下标为规格id）</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsMarketPrice</td>
            <td>商品市场价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCostPrice</td>
            <td>商品成本价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsDiscount</td>
            <td>商品折扣</td>
            <td>Double</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSerial</td>
            <td>商家编号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsStorageAlarm</td>
            <td>商品库存报警值</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TransportId</td>
            <td>商品售卖区域id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TransportTitle</td>
            <td>商品售卖区域名称</td>
            <td>String</td>
            <td>60</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCommend</td>
            <td>商品推荐1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品推荐 1:是 0:否</td>
        </tr>

        <tr>
            <td>GoodsFreight</td>
            <td>商品运费0为免运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品运费 0为免运费</td>
        </tr>

        <tr>
            <td>GoodsVat</td>
            <td>商品是否开具增值税发票1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品是否开具增值税发票 1:是 0:否</td>
        </tr>

        <tr>
            <td>AreaId1</td>
            <td>一级地区id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaId2</td>
            <td>二级地区id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsStcids</td>
            <td>店铺分类id首尾用</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td>店铺分类id 首尾用,隔开</td>
        </tr>

        <tr>
            <td>PlateidTop</td>
            <td>顶部关联板式</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>PlateidBottom</td>
            <td>底部关联板式</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsVirtual</td>
            <td>是否为虚拟商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否为虚拟商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>VirtualType</td>
            <td>虚拟商品类型0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>虚拟商品类型 0:核销商品 1:卡券商品 2:网盘商品 3:下载商品</td>
        </tr>

        <tr>
            <td>VirtualIndate</td>
            <td>虚拟商品有效期</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VirtualLimit</td>
            <td>虚拟商品购买上限</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VirtualInvalidRefund</td>
            <td>是否允许过期退款1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否允许过期退款 1:是 0:否</td>
        </tr>

        <tr>
            <td>IsGoodsFCode</td>
            <td>是否为F码商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否为F码商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>IsAppoint</td>
            <td>是否是预约商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否是预约商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>AppointSatedate</td>
            <td>预约商品出售时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>IsPlatformStore</td>
            <td>是否为平台自营</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsMGDiscount</td>
            <td>序列化会员等级折扣(商品)</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>InviterRatio</td>
            <td>分销比例</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InviterTotalQuantity</td>
            <td>已分销的商品数量</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InviterTotalAmount</td>
            <td>已分销的商品金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InviterAmount</td>
            <td>商品已结算的分销佣金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>InviterOpen</td>
            <td>开启推广</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MallGoodsCommend</td>
            <td>商品推荐1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品推荐 1:是 0:否</td>
        </tr>

        <tr>
            <td>MallGoodsSort</td>
            <td>商品推荐排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSort</td>
            <td>店铺排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreServiceIds</td>
            <td>店铺服务ID列表首尾用</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td>店铺服务ID列表 首尾用,隔开</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>分类属性表（DH_ClassAttributes）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ClassId</td>
            <td>分类编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>属性名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MappingField</td>
            <td>商品扩展属性字段名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td>如Field1、Field2</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>分类属性翻译表（DH_ClassAttributesLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId</td>
            <td>分类属性Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>分类属性名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品扩展属性表（DH_GoodsExAttributes）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ClassId</td>
            <td>分类编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Field1</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field2</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field3</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field4</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field5</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field6</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field7</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field8</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field9</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field10</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field11</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field12</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field13</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field14</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field15</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field16</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field17</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field18</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field19</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field20</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>分类属性扩展表（DH_GoodsClassExAttributes）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ClassId</td>
            <td>分类编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Field1</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field2</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field3</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field4</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field5</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field6</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field7</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field8</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field9</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field10</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field11</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field12</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field13</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field14</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field15</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field16</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field17</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field18</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field19</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Field20</td>
            <td>对应分类属性的字段</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品表（DH_Goods）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCommonId</td>
            <td>商品公共表ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdvWord</td>
            <td>商品广告词</td>
            <td>String</td>
            <td>150</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_1</td>
            <td>一级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_2</td>
            <td>二级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_3</td>
            <td>三级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BrandId</td>
            <td>商品品牌ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPromotionPrice</td>
            <td>商品促销价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPromotionType</td>
            <td>促销类型0</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>促销类型 0:无促销 1:抢购 2:限时折扣</td>
        </tr>

        <tr>
            <td>GoodsMarketPrice</td>
            <td>商品市场价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSerial</td>
            <td>商家编号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsStorageAlarm</td>
            <td>商品库存报警值</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClick</td>
            <td>商品点击数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSalenum</td>
            <td>商品销售数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsBuynum</td>
            <td>商品购买人数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCollect</td>
            <td>商品收藏数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSpec</td>
            <td>商品规格Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MerchantMaterial</td>
            <td>商家物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MaterialIds</td>
            <td>物料编号集合</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsStorage</td>
            <td>商品库存</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsWeight</td>
            <td>商品重量</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品主图</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsVideoName</td>
            <td>商品视频名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>商品内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MobileContent</td>
            <td>手机端商品描述</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsLock</td>
            <td>商品锁定0未锁</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品锁定 0未锁，1已锁</td>
        </tr>

        <tr>
            <td>GoodsState</td>
            <td>商品状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品状态 0:下架 1:正常 10:违规（禁售）</td>
        </tr>

        <tr>
            <td>GoodsVerify</td>
            <td>商品审核1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品审核 1:通过 0:未通过 10:审核中</td>
        </tr>

        <tr>
            <td>GoodsAddTime</td>
            <td>商品添加时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsEditTime</td>
            <td>商品编辑时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaId1</td>
            <td>一级地区id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaId2</td>
            <td>二级地区id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RegionId</td>
            <td>一级地区id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ColorId</td>
            <td>颜色规格id</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>TransportId</td>
            <td>商品售卖区域id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsFreight</td>
            <td>商品运费0为免运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品运费 0为免运费</td>
        </tr>

        <tr>
            <td>GoodsVat</td>
            <td>商品是否开具增值税发票1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品是否开具增值税发票 1:是 0:否</td>
        </tr>

        <tr>
            <td>GoodsCommend</td>
            <td>商品推荐1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品推荐 1:是 0:否</td>
        </tr>

        <tr>
            <td>GoodsStcids</td>
            <td>店铺分类id首尾用</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td>店铺分类id 首尾用,隔开</td>
        </tr>

        <tr>
            <td>EvaluationGoodStar</td>
            <td>好评星级</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>EvaluationCount</td>
            <td>评价数</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsVirtual</td>
            <td>是否为虚拟商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否为虚拟商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>VirtualIndate</td>
            <td>虚拟商品有效期</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VirtualLimit</td>
            <td>虚拟商品购买上限</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>VirtualInvalidRefund</td>
            <td>是否允许过期退款1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否允许过期退款 1:是 0:否</td>
        </tr>

        <tr>
            <td>VirtualContent</td>
            <td>虚拟商品内容</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsGoodsFCode</td>
            <td>是否为F码商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否为F码商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>IsAppoint</td>
            <td>是否是预约商品1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>是否是预约商品 1:是 0:否</td>
        </tr>

        <tr>
            <td>IsHaveGift</td>
            <td>是否拥有赠品</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsPlatformStore</td>
            <td>是否为平台自营</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsMGDiscount</td>
            <td>序列化会员等级折扣(商品)</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSort</td>
            <td>店铺排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Specification</td>
            <td>商品规格书</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ProductManual</td>
            <td>商品产品手册PDF</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliveryTime</td>
            <td>出货货期</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliveryDate</td>
            <td>到货货期</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoKeys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoDescription</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品SKU属性表（DH_GoodsSKUDetail）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="主键">PK</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SpecValue</td>
            <td>规格值</td>
            <td>String</td>
            <td>500</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MaterialId</td>
            <td>物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsMarketPrice</td>
            <td>市场价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>单价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsClick</td>
            <td>商品点击数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsSalenum</td>
            <td>商品销售数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsBuynum</td>
            <td>商品购买人数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCollect</td>
            <td>商品收藏数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EvaluationGoodStar</td>
            <td>好评星级</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>EvaluationCount</td>
            <td>评价数</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsState</td>
            <td>商品状态0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品状态 0:下架 1:正常 10:违规（禁售）</td>
        </tr>

        <tr>
            <td>GoodsVerify</td>
            <td>商品审核1</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>商品审核 1:通过 0:未通过 10:审核中</td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品翻译表（DH_GoodsLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GId</td>
            <td>商品Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>AdvWord</td>
            <td>商品广告词</td>
            <td>String</td>
            <td>150</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品主图</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsVideoName</td>
            <td>商品视频名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>商品内容</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>MobileContent</td>
            <td>手机端商品描述</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>VirtualContent</td>
            <td>虚拟商品内容</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Specification</td>
            <td>商品规格书</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ProductManual</td>
            <td>商品产品手册PDF</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliveryTime</td>
            <td>出货货期</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>DeliveryDate</td>
            <td>到货货期</td>
            <td>String</td>
            <td>512</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoTitle</td>
            <td>SEO标题</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoKeys</td>
            <td>SEO关键词</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SeoDescription</td>
            <td>SEO描述</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺常用分类表（DH_GoodsClassStaple）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>常用分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StapleCounter</td>
            <td>计数器</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StapleName</td>
            <td>常用分类名称</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CId_1</td>
            <td>一级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_2</td>
            <td>二级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId_3</td>
            <td>三级分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>TypeId</td>
            <td>类型ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>售卖区域表（DH_Transport）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>售卖区域自增ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaIds</td>
            <td>地区IDs</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsLimited</td>
            <td>-1禁卖</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>限制配送 0:否</td>
        </tr>

        <tr>
            <td>TransportType</td>
            <td>计费方式0</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>计费方式 0:按件 1:按重量</td>
        </tr>

        <tr>
            <td>Price</td>
            <td>运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>购物车数据表（DH_Cart）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>购物车自增ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>SID</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MaterialId</td>
            <td>物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SKUId</td>
            <td>SKUId</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsNum</td>
            <td>商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BlId</td>
            <td>组合套装ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>心愿清单数据表（DH_Wishlist）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>心愿清单自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>SKUID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsNum</td>
            <td>商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BlId</td>
            <td>组合套装ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WishlistAddTime</td>
            <td>加入时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>退款退货表（DH_RefundReturn）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>退款退货记录自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderId</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderSn</td>
            <td>订单编号</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundSn</td>
            <td>申请编号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UName</td>
            <td>会员名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID(0表示全部退款)</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderGoodsId</td>
            <td>订单商品ID(0表示全部退款)</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsName</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsNum</td>
            <td>商品数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyCode</td>
            <td>订单货币代码</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyRate</td>
            <td>订单货币汇率</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolLeft</td>
            <td>左标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>SymbolRight</td>
            <td>右标志</td>
            <td>String</td>
            <td>10</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundAmount</td>
            <td>退款金额</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundTime</td>
            <td>退款时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品图片</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundType</td>
            <td>申请类型</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1退款,2退货</td>
        </tr>

        <tr>
            <td>ReturnType</td>
            <td>退货类型</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1不用退货,2需要退货</td>
        </tr>

        <tr>
            <td>ReturnState</td>
            <td>订单收货状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1已收到货,2未收到货</td>
        </tr>

        <tr>
            <td>RefundreturnGoodsState</td>
            <td>退款物流状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1待发货,2待收货,3未收到,4已收货</td>
        </tr>

        <tr>
            <td>RefundreturnSellerState</td>
            <td>卖家处理状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1待审核,2同意,3不同意,4已完成</td>
        </tr>

        <tr>
            <td>RefundreturnSellerTime</td>
            <td>卖家处理时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnSellerMessage</td>
            <td>卖家备注</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnAdminState</td>
            <td>申请状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:1处理中,2待管理员处理,3已完成,4已拒绝</td>
        </tr>

        <tr>
            <td>RefundreturnAdminTime</td>
            <td>管理员处理时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnAdminMessage</td>
            <td>管理员备注</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnAddTime</td>
            <td>添加时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnBuyerMessage</td>
            <td>退款退货申请原因</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReasonId</td>
            <td>原因ID(0表示其它)</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReasonInfo</td>
            <td>原因内容</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PicInfo</td>
            <td>退款退货图片</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ExpressId</td>
            <td>物流公司编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ExpressName</td>
            <td>物流公司名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ExpressCode</td>
            <td>物流单号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnShipTime</td>
            <td>发货时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnDelayTime</td>
            <td>收货延迟时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnReceiveTime</td>
            <td>收货时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnReceiveMessage</td>
            <td>收货备注</td>
            <td>String</td>
            <td>300</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>RefundreturnMoneyState</td>
            <td>支付状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>:0未支付,1已支付</td>
        </tr>

        <tr>
            <td>CommisRate</td>
            <td>佣金比例</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>退款退货原因表（DH_RefundReason）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>原因自增ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReasonInfo</td>
            <td>原因内容</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReasonSort</td>
            <td>退款退货原因排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ReasonUpdateTime</td>
            <td>退款退货原因更新时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>收藏表（DH_Favorites）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>收藏记录自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UId</td>
            <td>会员ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MemberName</td>
            <td>会员名</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>FavId</td>
            <td>商品ID或店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FavType</td>
            <td>类型</td>
            <td>String</td>
            <td>5</td>
            <td></td>
            <td></td>
            <td></td>
            <td>:goods为商品,store为店铺</td>
        </tr>

        <tr>
            <td>FavTime</td>
            <td>收藏时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StoreClassId</td>
            <td>店铺分类ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GcId</td>
            <td>商品分类ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FavlogPrice</td>
            <td>商品收藏时价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FavlogMsg</td>
            <td>收藏备注</td>
            <td>String</td>
            <td>20</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>运费表（DH_Freight）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LogisticsCompanyId</td>
            <td>物流公司ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>发货仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CountryCode</td>
            <td>收货国家两字母ISO代码</td>
            <td>String</td>
            <td>2</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ShippingCost</td>
            <td>运费</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FirstWeight</td>
            <td>首重(KG)</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AdditionalWeight</td>
            <td>续重</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品阶梯价格表（DH_GoodsTieredPrice）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsCommonId</td>
            <td>商品公共表ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>商品SkuID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MinQuantity</td>
            <td>最小购买数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MaxQuantity</td>
            <td>最大购买数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OriginalPrice</td>
            <td>原价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Price</td>
            <td>销售价</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>搜索关键词表（DH_SearchKey）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>关键词</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Color</td>
            <td>颜色代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>搜索关键词翻译表（DH_SearchKeyLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SId</td>
            <td>搜索关键词Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>所属语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>关键词</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品规格属性表（DH_GoodsSpecValue）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ParentId</td>
            <td>所属父级Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>规格名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>规格排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsName</td>
            <td>商品名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品规格属性翻译表（DH_GoodsSpecValueLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SId</td>
            <td>关联规格Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>规格名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>库存暂扣表（DH_TemporaryInventory）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>WareHouseId</td>
            <td>仓库ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderId</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderGoodsId</td>
            <td>订单商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MerchantMaterialId</td>
            <td>商家物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Quantity</td>
            <td>数量</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>信誉评价表（DH_EvaluateGoods）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>信誉评价自增ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderId</td>
            <td>订单ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderSn</td>
            <td>订单编号</td>
            <td>String</td>
            <td>30</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>OrderGoodsId</td>
            <td>订单商品Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>SkuId</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsName</td>
            <td>商品名称</td>
            <td>String</td>
            <td>200</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsPrice</td>
            <td>商品价格</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsImage</td>
            <td>商品图片</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsScore</td>
            <td>商品评分</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ShipScore</td>
            <td>发货评分</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ServiceScore</td>
            <td>服务评分</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Content</td>
            <td>店铺信誉评价内容</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>IsAnonymous</td>
            <td>是否匿名评价</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AddTime</td>
            <td>评价时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺Id</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreName</td>
            <td>店铺名称</td>
            <td>String</td>
            <td>100</td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FromMemberId</td>
            <td>评价人Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>FromMemberName</td>
            <td>评价人名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>State</td>
            <td>评价状态0正常1禁止显示</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td>评价状态 0正常 1禁止显示</td>
        </tr>

        <tr>
            <td>Remark</td>
            <td>管理员处理备注</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Explain</td>
            <td>解释内容</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image1</td>
            <td>晒单图片1</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image2</td>
            <td>晒单图片2</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image3</td>
            <td>晒单图片3</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image4</td>
            <td>晒单图片4</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image5</td>
            <td>晒单图片5</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Image6</td>
            <td>晒单图片6</td>
            <td>String</td>
            <td>255</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>电话区号表（DH_TelAreaCode）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AreaCode</td>
            <td>区号</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Description</td>
            <td>描述</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CountryId</td>
            <td>所属国家</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>广告位置表（DH_AdvPosition）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>广告位置名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Introduction</td>
            <td>广告位简介</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Width</td>
            <td>广告位高度</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Height</td>
            <td>广告位宽度</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>广告表（DH_Advertising）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>APId</td>
            <td>广告位Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Title</td>
            <td>广告位描述</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Link</td>
            <td>广告链接地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PicturePath</td>
            <td>广告图片地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>StartTime</td>
            <td>广告开始时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>EndTime</td>
            <td>广告结束时间</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Enabled</td>
            <td>是否启用</td>
            <td>Boolean</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>ClickNumber</td>
            <td>点击次数</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BgColor</td>
            <td>广告背景颜色</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>广告翻译表（DH_AdvertisingLan）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>AId</td>
            <td>关联广告Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>语言Id</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Title</td>
            <td>广告位描述</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Link</td>
            <td>广告链接地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>PicturePath</td>
            <td>广告图片地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>商品浏览历史表（DH_GoodsBrowse）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuId</td>
            <td>SkuID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>BuyerId</td>
            <td>买家ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SId</td>
            <td>SID</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>BrowseTime</td>
            <td>浏览时间</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId</td>
            <td>商品分类</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId1</td>
            <td>商品一级分类</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId2</td>
            <td>商品二级分类</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CId3</td>
            <td>商品三级分类</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>预约到货通知表（DH_ArrivalNotice）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>StoreId</td>
            <td>店铺ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>GoodsId</td>
            <td>商品ID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>SkuID</td>
            <td>SkuID</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>MaterialId</td>
            <td>物料编号</td>
            <td>Int64</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Email</td>
            <td>通知邮箱</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>ReciverTwoLetterIsoCode</td>
            <td>两个字母ISO代码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Address</td>
            <td>地址</td>
            <td>text</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>LId</td>
            <td>语言ID</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CurrencyCode</td>
            <td>货币编码</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Status</td>
            <td>状态</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
<h3>店铺分类表（DH_StoreClass）</h3>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>显示名</th>
            <th>类型</th>
            <th>长度</th>
            <th>精度</th>
            <th>主键</th>
            <th>允许空</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Id</td>
            <td>编号</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td title="自增">AI</td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Name</td>
            <td>名称</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>Bail</td>
            <td>保证金</td>
            <td>Decimal</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>Sort</td>
            <td>排序</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUser</td>
            <td>创建者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateUserID</td>
            <td>创建者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>CreateTime</td>
            <td>创建时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>CreateIP</td>
            <td>创建地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUser</td>
            <td>更新者</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateUserID</td>
            <td>更新者</td>
            <td>Int32</td>
            <td></td>
            <td></td>
            <td></td>
            <td>N</td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateTime</td>
            <td>更新时间</td>
            <td>DateTime</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>

        <tr>
            <td>UpdateIP</td>
            <td>更新地址</td>
            <td>String</td>
            <td>50</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </tbody>
</table>
<br></br>
