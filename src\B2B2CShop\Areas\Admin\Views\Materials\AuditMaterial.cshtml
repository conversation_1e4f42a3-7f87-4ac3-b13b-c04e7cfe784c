﻿@model MerchantMaterial
@{

}

<style>
    .w80 {
        width: 80px;
    }
    /* 添加图片容器样式 */
    .material-image-container {
        display: flex;
        flex-wrap: wrap; /* 图片过多时换行 */
        gap: 10px; /* 图片之间的间距 */
    }
</style>
<div class="page" id="material-list">

    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("物料管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index", "Materials")"><span>@T("管理")</span></a></li>
                <li><a href="JavaScript:void(0);" class="current"><span>@T("审核")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="page">
        <table border="0" cellpadding="0" cellspacing="0" class="ds-default-table">
            <thead>
                <tr>
                    <th colspan="20">@T("物料信息")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th class="w80">@T("名称")</th>
                    <td colspan="20">@Model.Name</td>
                </tr>
                <tr>
                    <th class="w80">@T("备注")</th>
                    <td colspan="20">@Model.Remark</td>
                </tr>
                <tr>
                    <th class="w80">@T("店铺名称")</th>
                    <td colspan="20">@Model.StoreName</td>
                </tr>
                <tr>
                    <th class="w80">@T("是否启用")</th>
                    <td colspan="20">@(Model.Enabled? T("是") : T("否"))</td>
                </tr>
                <tr>
                    <th class="w80">@T("创建者")</th>
                    <td colspan="20">@Model.CreateUser</td>
                </tr>
                <tr>
                    <th class="w80">@T("创建日期")</th>
                    <td colspan="20">@Model.UpdateTime</td>
                </tr>
                <tr>
                    <th class="w80">@T("物料图片")</th>
                    <td colspan="20">
                        <div class="material-image-container">
                            @{
                                var images = Model.Images?.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();
                                if (images.Count > 0)
                                {
                                    foreach (var item in images)
                                    {
                                        var imageUrl = AlbumPic.FindByNameAndSId(item, Model.StoreId)?.Cover;
                                        if (imageUrl != null)
                                        {
                                            <a href="@imageUrl" data-lightbox="material-images" class="">
                                                <img src="@imageUrl" alt="@Model.Name" style="max-width: 100px; max-height: 100px;">
                                            </a>
                                        }
                                    }
                                }
                            }
                        </div>
                        <p style="margin: 10px; color: #999;">@T("点击图片进行预览")</p>
                    </td>
                </tr>
                <tr>
                    <th class="w80">@T("审核")</th>
                    <td colspan="20">
                        <input type="radio" id="auditPass" name="auditResult" value="true"  onclick="toggleReasonInput()">
                        <label for="auditPass">@T("通过")</label>
                        <input type="radio" id="auditFail" name="auditResult" value="false" onclick="toggleReasonInput()">
                        <label for="auditFail">@T("不通过")</label>
                    </td>
                </tr>
                <tr id="failReason" style="display: none;">
                    <th class="w80">@T("原因")</th>
                    <td colspan="20">
                        <textarea id="reasonInput" name="reasonInput" class="w500" style="height: 100px;" placeholder="@T("请输入不通过原因")"></textarea>
                    </td>
                </tr>
            </tbody>
            <tfoot></tfoot>
                <tr>
                    <td colspan="20" class="text-center">
                        <button class="btn btn-primary" onclick="submitAudit()">@T("提交审核")</button>
                    </td>
                </tr>
        </table>

    </div>
</div>
<link rel="stylesheet" href="~/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="~/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
<script>
    function toggleReasonInput() {
        const failRadio = document.getElementById('auditFail');
        const reasonDiv = document.getElementById('failReason');
        reasonDiv.style.display = failRadio.checked ? '' : 'none';
    }

    function submitAudit() {
        // 获取选中的审核选项
        const auditPass = document.getElementById('auditPass');
        const auditFail = document.getElementById('auditFail');
        
        // 判断是否选择了审核选项
        if (!auditPass.checked && !auditFail.checked) {
            layui.layer.msg('@T("请选择审核结果")');
            return;
        }

        const isPass = auditPass.checked;
        const reasonInput = document.getElementById('reasonInput');
        const reason = isPass ? '' : reasonInput.value;

        if (!isPass && reason.trim() === '') {
            layui.layer.msg('@T("请输入原因")');
            return;
        }

        const message = isPass ? '@T("确认通过此物料审核吗？")' : '@T("确认不通过此物料审核吗？")';
        
        layui.layer.confirm(message, {
            icon: 3,
            title: '@T("审核确认")',
            btn: ['@T("确定")', '@T("取消")']
        }, function(index) {
            const materialId = '@Model.Id';
            const url = '@Url.Action("AuditMaterial", "Materials")';
            const data = { 
                id: materialId, 
                status: isPass ? 1 : 2,
                cause: reason
            };
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                success: function(response) {
                    if (response.success) {
                        layui.layer.msg(isPass ? '@T("物料审核已通过")' : '@T("物料审核未通过")', {icon: 1});
                         setTimeout(function() {
                            window.location.href = '@Url.Action("Index", "Materials")';
                        }, 1500); // 1.5秒后跳转

                    } else {
                        layui.layer.msg('@T("审核操作失败，请重试")', {icon: 2});
                    }
                },
                error: function() {
                    layui.layer.msg('@T("网络错误，请重试")', {icon: 2});
                }
            });
            layui.layer.close(index);
        });
    }
</script>