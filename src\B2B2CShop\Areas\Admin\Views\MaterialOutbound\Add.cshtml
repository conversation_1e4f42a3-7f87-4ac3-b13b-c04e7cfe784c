﻿@model Currencies
<style asp-location="true">
    .red {
        color: red
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("物料出库")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Add")" class="current"><span>@T("出库")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="post" action="@Url.Action("Add")" id="doc_form">
        <div class="ds-default-form">
            <br />
            <dl>
                <dd>
                    @T("仓库：")
                    <select name="WareHouseId" class="txt" id="WareHouseDropDown">
                        <option value=0>@T("请选择仓库")</option>
                        @foreach (var item in (IEnumerable<WareHouse>)ViewBag.WareHouseList)
                        {
                            <option value=@item.Id>@T(item.Name)</option>
                        }
                    </select>
                </dd>
            </dl>
            <br />
            <dl>
                <dd>
                    @T("物料：")
                    <select name="MerchantMaterialId" class="select txt" id="MerchantMaterialDropDown" style="width:200px">
                        <option value=0>@T("请选择物料")</option>
                    </select>
                </dd>
            </dl>
            <br />
            <dl>
                <dd>@T("数量：")<input type="text" name="Quantity" class="txt"></dd>
            </dl>
            <br />
            <div class="btn_group">
                <button type="submit" class="btn">@T("保存")</button>
            </div>
        </div>
    </form>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script asp-location="Footer">
    $('#WareHouseDropDown').change(function (){
        var WareHouseId = $(this).val();
        $('#MerchantMaterialDropDown').empty();
        $('#MerchantMaterialDropDown').append('<option value=0>请选择物料</option>');
        $.get('@Url.Action("QueryWareHouseByMerchantMaterial")',{WareHouseId:WareHouseId},function(res){
            $.each(res.data,function(index,data){
                $('#MerchantMaterialDropDown').append($('<option>',{value:data.Id,text:data.Name}))
            })
        })
    })

    $(document).ready(function () {
        $('#MerchantMaterialDropDown').select2({
            placeholder: "@T("请选择物料")",
            allowClear: true
        });
    });
</script>