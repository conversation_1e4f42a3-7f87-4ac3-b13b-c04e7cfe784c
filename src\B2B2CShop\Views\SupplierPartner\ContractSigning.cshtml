﻿@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*合同签订*@
@model StoreJoinIn
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/contractSigning.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("公司资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <div class="center-card layui-card">
            <table class="category-table">
                <tr>
                    <th colspan="6">@T("店铺及联系人信息")</th>
                </tr>
                <tr>
                    <td>@T("店铺名称")</td>
                    <td>@Model.Name</td>
                    <td>@T("所在地")</td>
                    <td>
                        @Model.ProvinceName @Model.CityName @Model.CountyName
                    </td>
                    <td>@T("详细地址")</td>
                    <td>@Model.Address</td>
                </tr>
                <tr>
                    <td>@T("联系人姓名")</td>
                    <td>@Model.ContactsName</td>
                    <td>@T("联系人电话")</td>
                    <td>@Model.ContactsPhone</td>
                    <td>@T("电子邮箱")</td>
                    <td>@Model.ContactsEmail</td>
                </tr>
            </table>
            <table class="category-table">
                <tr>
                    <th colspan="2">@T("证件信息")</th>
                </tr>
                <tr>
                    @if (Model.Type==1)
                    {
                        <td>@T("证件号码")</td>
                        <td>@Model.PersonalIdentityNumber</td>
                    }
                    else
                    {
                        <td>@T("营业执照")</td>
                        <td>@Model.BusinessLicenceNumber</td>
                    }
                </tr>
                <tr>
                    <td>@T("证件照片")</td>
                    <td>
                        <div class="material-image-container">
                            <a href="@(Model.BusinessLicenceNumberElectronic)" data-lightbox="material-images" class="">
                                <img class="pic" src="@(Model.BusinessLicenceNumberElectronic)" alt="@T("证件照片")" style="max-width: 100px; max-height: 100px;">
                            </a>
                        </div>
                        
                    </td>
                </tr>
            </table>

            <table class="category-table">
                <tr>
                    <th colspan="2">@T("结算账号信息")</th>
                </tr>
                <tr>
                    <td>@T("银行账户名")</td>
                    <td>@Model.BankAccountName</td>
                </tr>
                <tr>
                    <td>@T("银行账号")</td>
                    <td>@Model.BankAccountNumber</td>
                </tr>
            </table>

            <table class="category-table">
                <tr>
                    <th colspan="2">@T("结算账号信息")</th>
                </tr>
                <tr>
                    <td>@T("卖家账号")</td>
                    <td>@Model.SellerName</td>
                </tr>
                <tr>
                    <td>@T("店铺名称")</td>
                    <td>@Model.Name</td>
                </tr>
                <tr>
                    <td>@T("店铺等级")</td>
                    <td>@Model.StoreGradeName</td>
                </tr>
                <tr>
                    <td>@T("开店时长")</td>
                    <td>@Model.Year @T("年")</td>
                </tr>
                <tr>
                    <td>@T("店铺分类")</td>
                    <td>@Model.StoreClassName</td>
                </tr>
                <tr>
                    <td>@T("经营类目")</td>
                    <td>
                        <table class="inside">
                            <tr>
                                <th>@T("一级类目")</th>
                                <th>@T("二级类目")</th>
                                <th>@T("分佣比例%")</th>
                            </tr>
                            @foreach (var item in Model.BusinessClasses)
                            {
                                var goodsClasss1 = GoodsClass.FindById(item.CId1);
                                var goodsClasss2 = GoodsClass.FindById(item.CId2);
                                <tr>
                                    <td>@goodsClasss1?.Name</td>
                                    <td>@goodsClasss2?.Name</td>
                                    <td>@goodsClasss2?.CommisRate</td>
                                </tr>
                            }
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>@T("审核意见")</td>
                    <td>@Model.Message</td>
                </tr>
            </table>

            <div class="layui-form-item btnBox">
                <a href="@Url.Action("SubmitSuccessful", "SupplierPartner")">
                    <button id="lastStep" class="layui-btn layui-btn-primary layui-border-blue btn">@T("上一步")</button>
                </a>
                <a href="#">
                    <button id="openStore" class="layui-btn btn" onclick="openstore()">@T("开通店铺")</button>
                </a>
            </div>

        </div>
    </div>
</div>
<div class="bug"></div>
<script>
    function openstore() {
        layer.confirm('@T("确定要开通店铺吗？")', {
            btn: ['@T("确定")', '@T("取消")'],
            title: '@T("开通店铺")',
            icon: 3,
            shade: 0.3,
            anim: 1
        }, function(index) {
            var id = '@Model.Id';
            var loading = layer.load(2, {
                shade: [0.3, '#000']
            });
            
            $.ajax({
                url: '@Url.Action("OpenStore", "SupplierPartner")',
                type: 'POST',
                data: { id: id },
                success: function (res) {
                    layer.close(loading);
                    if (res.success) {
                        layer.msg(res.msg, {
                            icon: 1,
                            time: 1500,
                            shade: 0.3
                        }, function() {
                            window.location.href = '@Url.Action("ActivationSuccessful", "SupplierPartner", new { id = Model.Id })';
                        });
                    } else {
                        layer.msg(res.msg, {
                            icon: 2,
                            time: 2000,
                            shade: 0.3
                        });
                    }
                },
                error: function () {
                    layer.close(loading);
                    layer.msg('@T("网络错误，请重试")', {
                        icon: 2,
                        time: 2000,
                        shade: 0.3
                    });
                }
            });
            layer.close(index);
        });
    }
</script>
<link rel="stylesheet" href="/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>