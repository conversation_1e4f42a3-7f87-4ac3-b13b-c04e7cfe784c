﻿namespace B2B2CShop.Dto
{
    public class ManageGoodsDto
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string GoodsId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string GoodsName { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public string GoodsImage { get; set; }

        /// <summary>
        /// 商品分类
        /// </summary>
        public string GoodsClass { get; set; }

        /// <summary>
        /// 商品价格
        /// </summary>
        public Decimal GoodsPrice { get; set; }

        /// <summary>
        /// 库存
        /// </summary>
        public int Inventory  { get; set; }

        /// <summary>
        /// 商品状态 0:下架 1:正常 10:违规（禁售）
        /// </summary>
        public int GoodsState { get; set; }
        /// <summary>
        /// 商品状态
        /// </summary>
        public string GoodsStateName { get; set; }

        /// <summary>
        /// 商品下架原因
        /// </summary>
        public string GoodsStateRemark { get; set; }

        /// <summary>
        /// 商品审核 1:通过 0:未通过 10:审核中
        /// </summary>
        public int GoodsVerify { get; set; }

        /// <summary>
        /// 商品审核状态
        /// </summary>
        public string GoodsVerifyName { get; set; }

        /// <summary>
        /// 商品审核不通过原因
        /// </summary>
        public string GoodsVerifyRemark { get; set; }


        /// <summary>
        /// 是否推荐商品
        /// </summary>
        public bool GoodsCommend { get; set; }

        /// <summary>
        /// 店铺名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
    }
}
