﻿﻿using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using Pek;
using Pek.Models;
using Pek.NCube;
using Pek.Seo;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using XCode.Membership;

namespace B2B2CShop.Controllers;

/// <summary>产品详情</summary>
[DHSitemap(IsUse = true)]
public class GoodsController : PekBaseControllerX {
    public IActionResult Index(long skuId)
    {
        var goodsSku = GoodsSKUDetail.FindById(skuId);
        if (goodsSku == null)
        {
            return Content(GetResource("商品Sku不存在"));
        }
        var goods = Goods.FindById(goodsSku.GoodsId);
        if (goods == null)
        {
            return Content(GetResource("商品不存在"));
        }
        if (goods.GoodsState!=1)
        {
            return Content(GetResource("商品已下架"));
        }
        int lId = WorkingLanguage.Id;
        var materialModel = MerchantMaterial.FindById(goods.MerchantMaterial);
        if (materialModel == null)
        {
            return Content(GetResource("商品物料不存在"));
        }
        goodsSku.GoodsClick += 1;
        goodsSku.Update();
        goods.GoodsClick += 1;
        goods.Update();
        goods = goods.CloneEntity();
        var goodslan = GoodsLan.FindByGIdAndLId(goods.Id, WorkingLanguage.Id);
        var userId = ManageProvider.User?.ID ?? 0;
        goods.Name = (goodslan?.LanName ?? goods.Name) + " "+ goodsSku.SpecValueDetail(lId);
        var goodsImage = GoodsImages.FindDefaultBySkuId(skuId, lId);
        if (goodsImage.IsNullOrEmpty())
        {
            goodsImage = GoodsLan.FindByGIdAndLId(skuId, lId)?.LanGoodsImage ?? goodsSku.Goods.GoodsImage ?? "";
        }
        goods.GoodsImage = AlbumPic.FindByNameAndSId(goodsImage, goods.StoreId)?.Cover;
        goods.GoodsVideoName = (goodslan?.GoodsVideoName).IsNullOrWhiteSpace() ? goods.GoodsVideoName : goodslan.GoodsVideoName;
        goods.SeoTitle = goodslan?.SeoTitle ?? goods.SeoTitle;
        goods.SeoKeys = goodslan?.SeoKeys ?? goods.SeoKeys;
        goods.SeoDescription = goodslan?.SeoDescription ?? goods.SeoDescription;
        var imgs = GoodsImagesLan.FindAllBySkuIdAndLId(skuId, WorkingLanguage.Id).Select(e => e.ImageUrl).ToList();
        if (imgs.Count == 0)
        {
            imgs = GoodsImages.FindAllBySkuId(skuId).Select(e => e.ImageUrl).ToList();
            if (imgs.Count==0)
            {
                imgs = GoodsImages.FindAllByGoodsCommonId(goods.Id).Select(e => e.ImageUrl).ToList();
            }
        }
        List<string> list = new();
        foreach (var item in imgs)
        {
            var album = AlbumPic.FindByName(item);
            if (album != null)
            {
                list.Add(album.Cover);
            }
        }
        ViewBag.Images = list;
        bool IsWish = Wishlist.FindBySkuIdAndBuyerId(skuId,userId) != null;
        ViewBag.IsWish = IsWish;
        goods.GoodsPrice = (goodsSku.GoodsPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        goods.GoodsMarketPrice = (goodsSku.GoodsMarketPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();

        // 获取阶梯价格数据
        var tieredPrices = GoodsTieredPrice.FindAllBySkuId(skuId)
            .Where(tp => tp.Enabled)
            .OrderBy(tp => tp.MinQuantity)
            .ToList();

        var curTieredPrice = new List<GoodsTieredPrice> { };

        // 转换货币价格
        foreach (var tier in tieredPrices)
        {
            var tierTemp = tier.CloneEntity();
            tierTemp.Price = (tierTemp.Price * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
            tierTemp.OriginalPrice = (tierTemp.OriginalPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
            curTieredPrice.Add(tierTemp);
        }

        var goodsProgram = GoodsExAttributes.FindByGId(goods.Id);
        var classAttributes = ClassAttributes.FindAllByClassIdLan(goods.CId, WorkingLanguage.Id);
        List<GoodsAttributesDto> goodsAttributes = new();
        foreach (PropertyInfo property in goodsProgram.GetType().GetProperties())
        {
            var attName = classAttributes.FirstOrDefault(e => e.MappingField == property.Name)?.Name;
            if (!attName.IsNullOrEmpty())
            {
                var attValue = property.GetValue(goodsProgram)?.ToString() ?? string.Empty;
                if (!attValue.IsNullOrEmpty())
                {
                    var goodsAtt = new GoodsAttributesDto()
                    {
                        Field = property.Name,
                        Attributes = attName,
                        Value = attValue
                    };
                    goodsAttributes.Add(goodsAtt);
                }
            }
        }
        var goodsClass = GoodsClass.FindById(goods.CId);

        ViewBag.GoodsSku = goodsSku;

        ViewBag.GoodsClassName = GoodsClassLan.FindByGIdAndLId(goodsClass.Id, WorkingLanguage.Id)?.RealName ?? goodsClass.Name;

        ViewBag.GoodsAttributes = goodsAttributes;

        ViewBag.TieredPrices = curTieredPrice;

        ViewBag.CompanyName = Store.FindById(goods.StoreId)?.CompanyName;

        //获取仓库 库存数据
        var listWareHouseMaterial = WareHouseMaterial.FindAllByMaterialIdLan(goodsSku.MaterialId, WorkingLanguage.Id);
        ViewBag.WareHouses = listWareHouseMaterial;


        //获取商品相关推荐
        ViewBag.Randomlist = GoodsSKUDetail.FindAllByRandomLan(6,WorkingLanguage.Id,WorkingCurrencies.ExchangeRate);

        ViewBag.Evaluatelist = EvaluateGoods.FindAllBySkuId(skuId).OrderByDescending(e => e.AddTime).Select(e => new EvaluateGoodsDto
        {
            AddTime = e.AddTime,
            GoodsName = e.GoodsName,
            Content = e.Content,
            FromMemberName = e.FromMemberName,
            SpecValue = OrderGoods.FindByOrderIdAndGoodsId(e.OrderId,e.GoodsId)?.SkuDetail?.SpecValueDetail(WorkingLanguage.Id),
            Image1 = e.Image1,
            Image2 = e.Image2,
            Image3 = e.Image3,
            Image4 = e.Image4,
            Image5 = e.Image5,
            Image6 = e.Image6,
            IsAnonymous = e.IsAnonymous,
        });

        var lan = GoodsLan.FindByGIdAndLId(goods.Id, WorkingLanguage.Id);
        if(lan != null)
        {
            if (!lan.Specification.IsNullOrWhiteSpace()) goods.Specification = lan.Specification;
            if (!lan.ProductManual.IsNullOrWhiteSpace()) goods.ProductManual = lan.ProductManual;
        }
        AddGoodsBrowse(goodsSku);


        return PekView(goods, DHSetting.Current.AllowMobileTemp, DHSetting.Current.AllowLanguageTemp);
    }
    public void AddGoodsBrowse(GoodsSKUDetail goodsSku)
    {
        #region 记录买家浏览记录

        var buyerId = ManageProvider.User?.ID.SafeString() ?? SId;
        var browse = GoodsBrowse.GetGoodsBrowse(buyerId, goodsSku.GoodsId,goodsSku.Id);//查看当前时间是否有浏览记录
        var cachaBrowse = Cache.Default.Get<string>("GoodsBrowse_" + goodsSku.Id + "_" + SId);

        if (browse == null && cachaBrowse != "true")
        {
            Cache.Default.Set("GoodsBrowse_" + goodsSku.Id + "_" + SId, "true", 5);
            browse = new GoodsBrowse
            {
                BuyerId = ManageProvider.User?.ID ?? 0,
                StoreId = goodsSku.Goods.StoreId,
                GoodsId = goodsSku.GoodsId,
                SkuId = goodsSku.Id,
                SId = SId,
                CId = goodsSku.Goods.CId,
                CId1 = goodsSku.Goods.Cid1,
                CId2 = goodsSku.Goods.Cid2,
                CId3 = goodsSku.Goods.Cid3,
            };

            browse.Insert();
        }
        #endregion
    }

    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        return [];
    }
    [HttpPost]
    [DisplayName("获取商品SKU属性")]
    public IActionResult GetGoodsSkuDetail(int[] specValueIds, Int64 goodsId, int number = 1)
    {
        var result = new DResult();
        string[] values = specValueIds.Select(id => $"\"VId\":{id}").ToArray();
        var sku = GoodsSKUDetail.FindBySpecvalueAndGId(values, goodsId);
        if (sku == null) return Json(result);
        var goods = Goods.FindById(goodsId);
        if (goods == null) return Json(result);

        var tieredPrices = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(goodsId, sku.Id, number);//获取阶梯价格

        sku.GoodsPrice = (tieredPrices?.Price ?? goods.GoodsPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        sku.GoodsMarketPrice = (tieredPrices?.OriginalPrice ?? goods.GoodsPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        result.success = true;
        result.data = new { Id = sku.Id.SafeString(), sku.GoodsPrice, sku.GoodsMarketPrice };

        var wareHouseMaterials = WareHouseMaterial.FindAllByMaterialIdLan(sku.MaterialId, WorkingLanguage.Id);
        result.extdata = new {
            wareHouseMaterials = wareHouseMaterials, //获取库存总量
            materialNum = wareHouseMaterials.Sum(s=>s.Quantity),//库存总数
            tieredPrices = GoodsTieredPrice.GetRatePriceByGoodsIdAndSkuId(goodsId,sku.Id, WorkingCurrencies.ExchangeRate)//获取阶梯价
        };
        return Json(result);
    }
    [HttpPost]
    [DisplayName("获取商品阶梯价")]
    public IActionResult GetTreTieredPrices(long GoodsId, long SkuId, int number)
    {
        var result = new DResult();
        var tieredPrices = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(GoodsId, SkuId, number);
        if (tieredPrices == null)
        {
            result.success = false;
            result.msg = GetResource("没有找到阶梯价格");
            return Json(result);
        }
        tieredPrices.OriginalPrice = (tieredPrices.OriginalPrice*WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        tieredPrices.Price = (tieredPrices.Price*WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        result.data = tieredPrices;
        result.success = true;
        return Json(result);
    }
    /// <summary>
    /// 获取相似商品
    /// </summary>
    /// <param name="cId"></param>
    /// <param name="storeId"></param>
    /// <param name="jsonKey"></param>
    /// <returns></returns>
    public IActionResult GetSimilarProducts(long cId,long storeId, string jsonKey)
    {
        var result = new DResult();
        var pages = new PageParameter()
        {
            PageIndex = 1,
            PageSize = 0,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        var modal = GoodsClass.FindById(cId);
        if (modal == null)
        {
            result.success = false;
            result.msg = GetResource("商品分类不存在");
            return Json(result);
        }

        var list = GoodsSKUDetail.SearchLan(cId, false, false, false, false, jsonKey, "", WorkingLanguage.Id, 0, WorkingCurrencies.ExchangeRate, storeId, pages);
        result.success = true;
        result.msg = list.Count.SafeString();//相似分类数量
        result.data = Url.Action("Index", "Products", new { cId = modal.Id, inventory = false, onSaleint = false, roSh = false, newGoods = false, jsonKey = jsonKey, key = "", storeId = storeId, page = 1 });//返回跳转链接
        return Json(result);
    }
    [HttpPost]
    [DisplayName("更新浏览时间")]
    public IActionResult RecordViewDuration(int duration, long goodsId,long skuId)
    {
        if (duration <= 0) return Json(new DResult { success = false, msg = GetResource("浏览时间不合法") });
        if (goodsId<=0) return Json(new DResult { success = false, msg = GetResource("商品记录为空") });
        var buyerId = ManageProvider.User?.ID.SafeString() ?? SId;
        var browse = GoodsBrowse.GetGoodsBrowse(buyerId, goodsId, skuId);//查看当前时间是否有浏览记录
        if (browse == null)
        {
            return Json(new DResult { success = false, msg = GetResource("没有找到浏览记录") });
        }
        browse.BrowseTime = duration;
        browse.Update();
        return Json(new DResult { success = true, msg = GetResource("操作成功") });
    }

    /// <summary>
    /// 获取商品库存数量
    /// </summary>
    /// <returns></returns>
    public IActionResult GetGoodsNum(Int64 gid,Int64 skuid)
    {
        DResult res = new();
        if(skuid > 0)
        {
            var sku = GoodsSKUDetail.FindById(skuid);
            res.data = WareHouseMaterial.FindAllByMaterialIdLan(sku?.MaterialId ?? 0, WorkingLanguage.Id).Sum(e => e.Quantity);
        }
        else
        {
            var goods = Goods.FindById(gid);
            res.data = WareHouseMaterial.FindAllByMaterialIdLan(goods?.MerchantMaterial ?? 0, WorkingLanguage.Id).Sum(e=>e.Quantity);
        }
        return Json(res);
    }
}