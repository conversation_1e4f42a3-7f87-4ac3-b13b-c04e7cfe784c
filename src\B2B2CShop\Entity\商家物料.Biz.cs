﻿using iTextSharp.text;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Pek;
using Pek.Helpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class MerchantMaterial : DHEntityBase<MerchantMaterial>
{
    #region 对象操作
    static MerchantMaterial()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        var df = Meta.Factory.AdditionalFields;
        df.Add(_.TemporaryQuantity);
        df.Add(_.Quantity);

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (Name.IsNullOrEmpty()) throw new ArgumentNullException(nameof(Name), "名称不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(StoreId), nameof(Name));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化MerchantMaterial[商家物料]数据……");

    //    var entity = new MerchantMaterial();
    //    entity.Id = 0;
    //    entity.StoreId = 0;
    //    entity.Name = "abc";
    //    entity.Enabled = true;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化MerchantMaterial[商家物料]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>店铺ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Store? Store => Extends.Get(nameof(Store), k => Store.FindById(StoreId));

    /// <summary>店铺ID</summary>
    [Map(nameof(StoreId), typeof(Store), "Id")]
    public String? StoreName => Store?.Name;

    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Int32 RealQuantity => Quantity-TemporaryQuantity;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="storeId">店铺ID</param>
    /// <param name="name">名称</param>
    /// <param name="enabled">是否启用。默认启用</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<MerchantMaterial> Search(Int64 storeId, String name, Boolean? enabled, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (storeId >= 0) exp &= _.StoreId == storeId;
        if (!name.IsNullOrEmpty()) exp &= _.Name == name;
        if (enabled != null) exp &= _.Enabled == enabled;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>根据店铺ID、物料名称、备注查找</summary>
    /// <param name="storeId">店铺ID</param>
    /// <param name="materialName">物料名称</param>
    /// <param name="status">状态</param>
    /// <returns>实体列表</returns>
    public static IList<MerchantMaterial> FindByStoreIdAndMaterialNameAndRemark(long storeId, string materialName, int status, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var List = FindAllWithCache();
            if (storeId > 0)
            {
                List = List.Where(e => e.StoreId == storeId).ToList();
            }
            if (!materialName.IsNullOrWhiteSpace())
            {
                List = List.Where(e => e.Name?.Contains(materialName) == true).ToList();
            }
            if (status >= 0)
            {
                List = List.Where(e => e.Status == status).ToList();
            }
            // 动态排序
            if (!string.IsNullOrEmpty(page.Sort))
            {
                var propertyInfo = typeof(MerchantMaterial).GetProperty(page.Sort, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (propertyInfo != null)
                {
                    if (page.Desc)
                    {
                        List = List.OrderByDescending(e => propertyInfo.GetValue(e)).ToList();
                    }
                    else
                    {
                        List = List.OrderBy(e => propertyInfo.GetValue(e)).ToList();
                    }
                }
            }
            page.TotalCount = List.Count;
            return List.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
        }

        var exp = new WhereExpression();
        if (storeId > 0) exp &= _.StoreId == storeId;
        if (!materialName.IsNullOrWhiteSpace()) exp &= _.Name.Contains(materialName);
        if (status >= 0) exp &= _.Status == status;
        return FindAll(exp, page);
    }
    /// <summary>
    /// 获取店铺所有已经启用的物料并且通过审核的列表
    /// </summary>
    /// <returns></returns>
    public static IList<MerchantMaterial> GetEnableList(Int64 storeId)
    {
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(x => x.Enabled == true && x.StoreId == storeId && x.Status == 1);
        return FindAll(_.Enabled == true& _.StoreId == storeId & _.Status == 1);
    }

    public static IList<MerchantMaterial> FindAllByIds(string ids)
    {
        if (ids.IsNullOrEmpty()) return [];

        return FindAll(_.Id.In(ids.Split(",", StringSplitOptions.RemoveEmptyEntries)));
    }

    /// <summary>
    /// 查询商品物料剩余数量
    /// </summary>
    /// <param name="ClassId1"></param>
    /// <param name="ClassId2"></param>
    /// <returns></returns>
    public static int GetQuantityByClassId(Int64 ClassId1,Int64 ClassId2)
    {
        if (ClassId1 > 0)
        {
            var mIds = Goods.FindAllByCid1(ClassId1).Select(e => e.MaterialIds).ToList();
            if (mIds.Count == 0) return 0;
            var ids = new List<string>();
            foreach (var item in mIds)
            {
                if(item.IsNullOrEmpty()) continue;
                ids.AddRange(item.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
            }
            var data = FindAll(_.Id.In(ids));
            var sum1 = data.Sum(e => e.Quantity);
            var sum2 = data.Sum(e => e.TemporaryQuantity);
            return sum1 - sum2;
        }
        else if (ClassId2 > 0)
        {
            var mIds = Goods.FindAllByCid2(ClassId2).Select(e => e.MaterialIds).ToList();
            if (mIds.Count == 0) return 0;
            var ids = new List<string>();
            foreach (var item in mIds)
            {
                if(item.IsNullOrEmpty()) continue;
                ids.AddRange(item.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
            }
            var data = FindAll(_.Id.In(ids));
            var sum1 = data.Sum(e => e.Quantity);
            var sum2 = data.Sum(e => e.TemporaryQuantity);
            return sum1 - sum2;
        }
        else
        {
            var mIds = Goods.FindAll().Select(e => e.MaterialIds).ToList();
            if (mIds.Count == 0) return 0; var ids = new List<string>();
            foreach (var item in mIds)
            {
                if(item.IsNullOrEmpty()) continue;
                ids.AddRange(item.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
            }
            var data = FindAll(_.Id.In(ids));
            var sum1 = data.Sum(e => e.Quantity);
            var sum2 = data.Sum(e => e.TemporaryQuantity);
            return sum1 - sum2;
        }
    }
    /// <summary>
    /// 获取所有有库存的物料
    /// </summary>
    /// <returns>返回实际可用库存（数量减去暂扣库存数量）大于0的物料</returns>
    public static IList<MerchantMaterial> FindAllInStock()
    {
        return FindAll(_.Quantity > _.TemporaryQuantity);
    }

    // Select Count(Id) as Id,Category From DH_MerchantMaterial Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<MerchantMaterial> _CategoryCache = new FieldCache<MerchantMaterial>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IMerchantMaterial ToModel()
    {
        var model = new MerchantMaterial();
        model.Copy(this);

        return model;
    }
    public static void DelByIds(string Ids)
    {
        if (Entity<MerchantMaterial>.Delete(@_.Id.In(Ids.Trim(','))) > 0)
        {
            Entity<MerchantMaterial>.Meta.Cache.Clear("");
        }
    }

    /// <summary>
    /// 根据订单修改暂存数量
    /// </summary>
    /// <param name="OrderId"></param>
    public static void UpdateTemporaryQuantity(Int64 OrderId)
    {
        var data = OrderGoods.FindAllByOrderId(OrderId);
        foreach (var ordergoods in data)
        {
            var modal = MerchantMaterial.FindById(ordergoods.MaterialId);
            if (modal != null)
            {
                modal.TemporaryQuantity = modal.TemporaryQuantity - ordergoods.GoodsNum;
                modal.Update();
            }
        }
    }
    /// <summary>
    /// 获取物料剩余数量
    /// </summary>
    /// <param name="Id">商家物料Id</param>
    /// <returns></returns>
    public static int GetQuantity(Int64 Id)
    {
        var modal = FindById(Id);
        if (modal == null) return 0;
        return modal.Quantity - modal.TemporaryQuantity;
    }
    /// <summary>
    /// 获取物料剩余数量
    /// </summary>
    /// <param name="wIds">商家物料Id集合</param>
    /// <returns>剩余数量总和</returns>
    public static int GetQuantityByWIds(string wIds)
    {
        if (string.IsNullOrEmpty(wIds)) return 0;

        var models = FindAll(_.Id.In(wIds.Split(',', StringSplitOptions.RemoveEmptyEntries)));
        if (models == null || models.Count == 0) return 0;

        var zk = TemporaryInventory.FindAllByMIds(wIds).Sum(e=>e.Quantity);

        return models.Sum(e => e.Quantity) - zk;
    }
    #endregion
}
