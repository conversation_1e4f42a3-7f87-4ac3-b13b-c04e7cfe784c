﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>商家物料</summary>
public partial class MerchantMaterialModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>店铺ID</summary>
    public Int64 StoreId { get; set; }

    /// <summary>名称</summary>
    public String Name { get; set; } = null!;

    /// <summary>总数量</summary>
    public Int32 Quantity { get; set; }

    /// <summary>暂扣数量</summary>
    public Int32 TemporaryQuantity { get; set; }

    /// <summary>是否启用。默认启用</summary>
    public Boolean Enabled { get; set; }

    /// <summary>状态 0:未审核,1:已审核 2:审核不通过</summary>
    public Int32 Status { get; set; }

    /// <summary>上传图片</summary>
    public String? Images { get; set; }

    /// <summary>审核原因</summary>
    public String? Cause { get; set; }

    /// <summary>审核者</summary>
    public Int32 Auditor { get; set; }

    /// <summary>审核时间</summary>
    public Int64 AuditTime { get; set; }

    /// <summary>备注</summary>
    public String? Remark { get; set; }

    /// <summary>重量</summary>
    public Decimal Weight { get; set; }

    /// <summary>体积</summary>
    public Decimal Volume { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IMerchantMaterial model)
    {
        Id = model.Id;
        StoreId = model.StoreId;
        Name = model.Name;
        Quantity = model.Quantity;
        TemporaryQuantity = model.TemporaryQuantity;
        Enabled = model.Enabled;
        Status = model.Status;
        Images = model.Images;
        Cause = model.Cause;
        Auditor = model.Auditor;
        AuditTime = model.AuditTime;
        Remark = model.Remark;
        Weight = model.Weight;
        Volume = model.Volume;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
