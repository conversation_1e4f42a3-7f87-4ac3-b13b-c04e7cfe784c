﻿<script asp-location="Head">
</script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3 class="ny-panel-title">@("店铺管理")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="#" class="current"><span>@T("开店申请")</span></a></li>
                <li><a href="javascript:void(0)" onclick="openAddStoreModal()"><span>@T("新增外驻店铺")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("所属等级")：</dt>
                <dd>
                    <select name="belongingLevel">
                        <option value="-1">@T("请选择")</option>
                        @foreach (var item in ViewBag.StoreGradeList)
                        {
                            <!option value="@item.Value" @(Model.belongingLevel==item.Value?"selected":"")>@item.Text</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("状态")：</dt>
                <dd>
                    <select name="state">
                        <option value="-1">@T("请选择")</option>
                        <!option value="10"  @(Model.state=="10" ?"selected":"")>@T("新申请")</!option>
                        <!option value="11"  @(Model.state == "11" ? "selected" : "")>@T("已付款")</!option>
                        <!option value="20"  @(Model.state == "20" ? "selected" : "")>@T("审核通过")</!option>
                        <!option value="30"  @(Model.state == "30" ? "selected" : "")>@T("审核失败")</!option>
                        <!option value="31" @(Model.state == "31" ? "selected" : "")>@T("付款审核失败")</!option>
                        <!option value="40"  @(Model.state == "40" ? "selected" : "")>@T("开店成功")</!option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("店主")</dt>
                <dd>
                    <input type="text" value="@Model.shopkeeper" id="shopkeeper" name="shopkeeper">
                </dd>
            </dl>
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <input type="text" value="@Model.shopNames" id="shopNames" name="shopNames">
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr style="text-align:center;">
                <th>@T("店铺名称")</th>
                <th>@T("店主账号")</th>
                <th>@T("所在地")</th>
                <th>@T("所属等级")</th>
                <th>@T("状态")</th>
                <th style="text-align:center;">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (StoreJoinIn item in Model.list)
            {
                <tr id="<EMAIL>">
                    <td>@item.Name</td>
                    <td>@item.UName</td>
                    <td>@(item.ProvinceName +" "+ item.CityName + " " +item.CountyName)</td>
                    <td>@item.StoreGradeName</td>
                    <td>
                        @(
                            item.State switch
                            {
                                10=>"新申请",
                                11=>"已付款",
                                20=>"审核通过",
                                30=>"审核失败",
                                31=>"付款审核失败",
                                40=>"开店成功",
                                _=>"未知"
                            }
                        )
                    </td>
                    <td>
                        @if (item.State == 10)
                        {
                            <a class="dsui-btn-edit" href="javascript:void(0)" onclick="openAuditView('@item.Id')"><i class="iconfont"></i>@T("审核")</a>
                        }
                        else
                        {
                            <a class="dsui-btn-view" href="javascript:void(0)" onclick="openView('@item.Id')"><i class="iconfont"></i>@T("查看")</a>
                        }
                        @if (item.State!=40)
                        {
                            <a class="dsui-btn-del" href="javascript:dsLayerConfirm('@Url.Action("DeleteStoreJoinIn", new { Id = item.Id })','@T("您确定要删除吗?")')"><i class="iconfont"></i>@T("删除")</a>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script type="text/javascript" src="~/static/admin/js/jquery.edit.js" charset="utf-8"></script>

<script asp-location="Footer">
    function openAuditView(id) {
        layui.use('layer', function(){
            var layer = layui.layer;
            layer.open({
                type: 2,
                title: '@T("入驻审核")',
                shadeClose: true,
                shade: 0.8,
                area: ['900px', '800px'],
                fixed: false, //不固定
                maxmin: true, // 启用最大化最小化按钮
                content: '@Url.Action("AuditStoreJoinIn")?id='+ id // iframe的url
            });
        });
    }

    function openView(id) {
        layui.use('layer', function(){
            var layer = layui.layer;
            layer.open({
                type: 2,
                title: '@T("查看入驻信息")',
                shadeClose: true,
                shade: 0.8,
                area: ['900px', '800px'],
                fixed: false, //不固定
                maxmin: true, // 启用最大化最小化按钮
                content: '@Url.Action("StoreJoinInDetail")?id='+ id // iframe的url
            });
        });
    }

     function openAddStoreModal() {
        layui.use('layer', function(){
            var layer = layui.layer;
            layer.open({
                type: 2,
                title: '新增外驻店铺',
                shadeClose: true,
                shade: 0.8,
                area: ['900px', '500px'],
                fixed: false, //不固定
                maxmin: true, // 启用最大化最小化按钮
                content: '@Url.Action("AddStore")' // iframe的url
            });
        });
    }
</script>