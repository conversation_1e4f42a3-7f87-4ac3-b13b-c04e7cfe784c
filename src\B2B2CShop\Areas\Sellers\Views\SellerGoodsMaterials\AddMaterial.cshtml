﻿@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsMaterials";

    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
}
@await Html.PartialAsync("_Left")
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li><a href="@Url.Action("Index")">@T("物料列表")</a></li>
        </ul>

    </div>
    <div class="p20">
        <div class="dssc-form-default">
            <form id="chain_form" enctype="multipart/form-data" method="post" action="@Url.Action("AddMaterial")">
                <dl>
                    <dt><i class="required">*</i>@T("物料名称")：</dt>
                    <dd>
                        <input type="text" value="" name="name" id="chain_name" class="w200 text" required><span></span>
                        <p class="hint"></p>
                    </dd>
                </dl>
                <dl>
                    <dt ds_type="no_spec"><i class="required">*</i>@T("重量")：</dt>
                    <dd ds_type="no_spec">
                        <input id="weight" name="weight" class="text w60" type="number" step="any" min="0" required><span> kg</span>
                        <p class="hint"></p>
                    </dd>
                </dl>
                <dl>
                    <dt ds_type="no_spec"><i class="required">*</i>@T("体积")：</dt>
                    <dd ds_type="no_spec">
                        <input id="volume" name="volume" class="text w60" type="number" step="any" min="0" required><span> m²</span>
                        <p class="hint"></p>
                    </dd>
                </dl>
                <dl>
                    <dt>@T("是否启用"): </dt>
                    <dd>
                        <input id="chain_state1" name="enabled" checked="checked" value="true" type="radio">
                        <label for="chain_state1" class="cb-enable selected" title="是"><span>是</span></label>
                        <input id="chain_state0" name="enabled" value="false" type="radio">
                        <label for="chain_state0" class="cb-disable " title="否"><span>否</span></label>
                        <p class="hint"></p>
                    </dd>
                </dl>
                <dl>
                    <dt>@T("上传图片"): </dt>
                    <dd>
                        <div class="dssc-form-goods-pic">
                            <div class="container" style="width:790px">
                                <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                                    <div class="dssc-goodspic-list">
                                        <div class="title">
                                        </div>
                                        <ul dstype="ul0">
                                            @{
                                                for (int i = 0; i < 5; i++)
                                                {
                                                    string dstypeStr = "file_0" + i;

                                                    <li class="dssc-goodspic-upload">
                                                        <div class="upload-thumb">
                                                            <img src="/uploads/common/default_goods_image.jpg" dstype="@dstypeStr">
                                                            <input type="hidden" name="img[0][@i][name]" value="" dstype="@dstypeStr">
                                                        </div>
                                                        <div class="show-default" dstype="@dstypeStr">
                                                            <a href="javascript:void(0)" dstype="del" class="del" title="移除">X</a>
                                                        </div>
                                                        <div class="dssc-upload-btn">
                                                            <a href="javascript:void(0);">
                                                                <span>
                                                                    <input type="file" hidefocus="true" size="1" class="input-file" name="@dstypeStr" id="@dstypeStr">
                                                                </span>
                                                                <p>
                                                                    <i class="iconfont">&#xe733;</i>
                                                                    上传
                                                                </p>
                                                            </a>
                                                        </div>
                                                    </li>

                                                }
                                            }
                                        </ul>
                                        <div class="dssc-select-album">
                                            <a class="dssc-btn" href="/Sellers/SellerGoodsAdd/GoodsAlbumPic?demo=2" dstype="select-0">
                                                <i class="iconfont">&#xe72a;</i>
                                                从图片空间选择
                                            </a>
                                            <a href="javascript:void(0);" dstype="close_album" class="dssc-btn ml5" style="display: none;">
                                                <i class=" iconfont">&#xe67a;</i>
                                                关闭相册
                                            </a>
                                        </div>
                                        <div dstype="album-0"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </dd>
                </dl>
                <div class="bottom">
                    <input type="submit" class="submit" value="@T("提交")">
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function() {
        // 初始化行计数器
        var rowCount = 1;
        // 默认的最小购买数量
        var DEFAULT_MIN_QUANTITY = 1;

        // 页面加载时设置第一行的最小购买数量为默认值、原价为商品价格
        $(document).ready(function() {
            $("input[name='tieredPrice[0].MinQuantity']").val(DEFAULT_MIN_QUANTITY);

            // 如果商品价格已有值，则设置到原价字段
            var goodsPrice = $('input[name="price"]').val();
            if(goodsPrice) {
                $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
            }
        });

        // 商品价格自动同步到阶梯价格的原价字段
        $('input[name="price"]').on('input change', function() {
            var goodsPrice = $(this).val();
            $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
            $('input[name$="tieredPrice[0].Price"]').val(goodsPrice);
        });

        // 重置阶梯价格表格，清空所有行，重新添加首行
        function resetTieredPriceTable() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="price"]').val() || '';

            // 清空表格
            $("#tieredPriceRows").empty();
            rowCount = 0;

            // 添加第一行，最小购买数量默认为2，原价设为商品价格且只读
            var firstRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text w60" value="${DEFAULT_MIN_QUANTITY}" /></td>
                <td><input name="tieredPrice[0].MaxQuantity" type="number" min="2" class="text w60" /></td>
                <td><input name="tieredPrice[0].OriginalPrice" type="number" min="0" step="any" class="text w60" value="${goodsPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[0].Price" type="number" min="0" step="any" class="text w60" required/></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(firstRow);
            rowCount = 1;

            // 启用新行的验证
            setupTieredPriceValidation();
        }

        // 添加重置按钮到阶梯价格区域
        $("#tieredPriceContainer").append('<a href="javascript:void(0);" id="resetTieredPrice" class="dssc-btn" style="margin-left:10px;"><i class="iconfont">&#xe67a;</i>重置阶梯价格</a>');

        // 为重置按钮绑定点击事件
        $("#resetTieredPrice").click(function() {
            // 询问用户是否确定重置
            layer.confirm('确定要重置所有阶梯价格规则吗？', {
                btn: ['确定','取消']
            }, function(){
                resetTieredPriceTable();
                layer.msg('阶梯价格已重置');
            });
        });

        // 添加新行时也要考虑到原价同步
        $("#addTieredPriceRow").click(function() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="price"]').val() || '';

            // 如果表格中没有行，先添加一行
            if ($("#tieredPriceRows tr").length === 0) {
                resetTieredPriceTable();
                return;
            }

            // 查找最后一行的最大数量
            var lastMaxQuantity = 0;
            if ($("#tieredPriceRows tr").length > 0) {
                var lastRow = $("#tieredPriceRows tr:last");
                var lastMaxInput = lastRow.find("input[name$='.MaxQuantity']");
                lastMaxQuantity = parseInt(lastMaxInput.val()) || 0;

                // 如果最后最大数量为0（无限），则提醒用户
                if (lastMaxQuantity === 0) {
                    layer.msg("上一个价格区间的最大数量为不限，不能再添加价格区间");
                    return;
                }
            }

            var newRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[${rowCount}].MinQuantity" type="number" min="1" class="text w60" value="${lastMaxQuantity > 0 ? (lastMaxQuantity + 1) :     DEFAULT_MIN_QUANTITY}" ${lastMaxQuantity > 0 ? 'readonly' : ''} /></td>
                <td><input name="tieredPrice[${rowCount}].MaxQuantity" type="number" min="${lastMaxQuantity+2}" class="text w60" /></td>
                <td><input name="tieredPrice[${rowCount}].OriginalPrice" type="number" class="text w60" step="any" value="${goodsPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[${rowCount}].Price" type="number" step="any" class="text w60" required/></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(newRow);
            rowCount++;

            // 启用新行的验证
            setupTieredPriceValidation();
        });

        // 删除分层价格行
        $(document).on("click", ".remove-row", function() {
            var currentRow = $(this).closest("tr");
            var nextRow = currentRow.next("tr.tiered-price-row");
            var prevRow = currentRow.prev("tr.tiered-price-row");

            // 如果这是最后一行，则不允许删除，而是重置
            if ($("#tieredPriceRows tr").length === 1) {
                var firstRowMinInput = currentRow.find("input[name$='.MinQuantity']");
                firstRowMinInput.val(DEFAULT_MIN_QUANTITY);
                firstRowMinInput.prop('readonly', false);

                // 清空其他输入
                currentRow.find("input[name$='.MaxQuantity']").val("");
                currentRow.find("input[name$='.Price']").val("");

                // 获取当前商品价格
                var goodsPrice = $('input[name="price"]').val() || '';
                currentRow.find("input[name$='.OriginalPrice']").val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');

                layer.msg("至少需要保留一行阶梯价格，已重置为默认值");
                return;
            }

            // 如果有下一行，需要更新其最小数量
            if (nextRow.length > 0) {
                // 如果有上一行，则使用上一行的最大数量+1作为下一行的最小数量
                if (prevRow.length > 0) {
                    var prevMaxQuantity = parseInt(prevRow.find("input[name$='.MaxQuantity']").val()) || 0;
                    if (prevMaxQuantity > 0) {
                        var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                        nextMinInput.val(prevMaxQuantity + 1);
                        nextMinInput.prop('readonly', true);
                    }
                } else {
                    // 如果没有上一行，则下一行的最小数量设为默认值
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(DEFAULT_MIN_QUANTITY);
                    nextMinInput.prop('readonly', false);
                }
            }

            // 删除当前行
            currentRow.remove();

            // 重新计算行索引
            recalculateRowIndices();
        });

        // 分层价格输入的验证设置
        function setupTieredPriceValidation() {
            // 当最大数量输入变化时的处理
            $("#tieredPriceRows").on("change", "input[name$='.MaxQuantity']", function() {
                var currentRow = $(this).closest("tr");
                var currentMinInput = currentRow.find("input[name$='.MinQuantity']");
                var currentMaxInput = $(this);
                var nextRow = currentRow.next("tr.tiered-price-row");

                var minQuantity = parseInt(currentMinInput.val()) || 0;
                var maxQuantity = parseInt(currentMaxInput.val()) || 0;

                // 确保最大值大于最小值
                if (maxQuantity > 0 && maxQuantity <= minQuantity) {
                    layer.msg("最大购买数量必须大于最小购买数量");
                    currentMaxInput.val('');
                    return;
                }

                // 如果有下一行，则更新其最小数量为当前行的最大数量+1
                if (nextRow.length > 0 && maxQuantity > 0) {
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(maxQuantity + 1);
                    nextMinInput.prop('readonly', true);
                }
            });

            // 当价格输入变化时的处理
            $("#tieredPriceRows").on("change", "input[name$='.Price'], input[name$='.OriginalPrice']", function() {
                var value = parseFloat($(this).val());
                if (isNaN(value) || value <= 0) {
                    layer.msg("价格必须是大于0的数字");
                    $(this).val('');
                }
            });
        }

        // 删除行后重新计算行索引
        function recalculateRowIndices() {
            $("#tieredPriceRows tr").each(function(index) {
                $(this).find("input").each(function() {
                    var name = $(this).attr("name");
                    if (name) {
                        name = name.replace(/\[\d+\]/, '[' + index + ']');
                        $(this).attr("name", name);
                    }
                });
            });

            // 更新行计数器
            rowCount = $("#tieredPriceRows tr").length;
        }
    });
</script>
<style>
    td{
        margin: 10px;
    }
</style>
<script src="/static/plugins/jquery.ajaxContent.pack.js" type="text/javascript"></script>
<script src="/static/home/<USER>/sellergoods_add_step3.js"></script>
<script>
    var DEFAULT_GOODS_IMAGE = "/uploads/common/default_goods_image.jpg";

    $(function() {
        /* ajax打开图片空间 */
        $('a[dstype="select-0"]').ajaxContent({
            event: 'click',
            //mouseover
            loaderType: "img",
            loadingMsg: '/static/home/<USER>/loading.gif',
            target: 'div[dstype="album-0"]'
        }).click(function() {
            $(this).hide();
            $(this).next().show();
        });
    });
</script>