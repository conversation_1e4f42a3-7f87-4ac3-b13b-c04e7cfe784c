﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*开通成功*@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/activationSuccessful.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("公司资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <div class="center-card layui-card">
            <div class="successBox">
                <img src="~/public/images/icons/chenggong.png" alt="">
                <p>@T("店铺开通成功！")</p>

                <div class="btnBox">
                    <a href="/">
                        <button id="goHome" class="layui-btn layui-btn-primary layui-border-blue btn">@T("返回首页")</button>
                    </a>
                    <a href="@Url.Action("Index","SellerGoodsAdd",new {area="sellers"})">
                        <button id="putGood" class="layui-btn btn">@T("发布商品")</button>
                    </a>
                </div>  
            </div>

        </div>
    </div>
</div>
<div class="bug"></div>
<script>

</script>