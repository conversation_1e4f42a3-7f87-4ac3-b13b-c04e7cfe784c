﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*开通成功*@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/activationSuccessful.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">首页</a>
        <div>></div>
        <a class="textSelect" href="#">
            供应商合作
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            商家入驻申请
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>签订入驻协议</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>公司资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>财务资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>店铺经营信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>合同签订</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>店铺开通</p>
            </div>
        </div>

        <div class="center-card layui-card">
            <div class="successBox">
                <img src="~/public/images/icons/chenggong.png" alt="">
                <p>店铺开通成功！</p>

                <div class="btnBox">
                    <a href="/">
                        <button id="goHome" class="layui-btn layui-btn-primary layui-border-blue btn">返回首页</button>
                    </a>
                    <a href="#">
                        <button id="putGood" class="layui-btn btn">发布商品</button>
                    </a>
                </div>
            </div>



        </div>
    </div>
</div>
<div class="bug"></div>
<script>

</script>
