﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Common;
using B2B2CShop.Dto;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Org.BouncyCastle.Crypto;
using Pek;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class Cart : DHEntityBase<Cart>
{
    #region 对象操作
    static Cart()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(StoreId));

        // 过滤器 UserModule、TimeModule、IPModule

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Cart[购物车数据表]数据……");

    //    var entity = new Cart();
    //    entity.BuyerId = "abc";
    //    entity.StoreId = 0;
    //    entity.StoreName = "abc";
    //    entity.GoodsId = 0;
    //    entity.MaterialId = 0;
    //    entity.SKUId = 0;
    //    entity.GoodsPrice = 0.0;
    //    entity.GoodsNum = 0;
    //    entity.BlId = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Cart[购物车数据表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>店铺ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Store? Store => Extends.Get(nameof(Store), k => Store.FindById(StoreId));
    /// <summary>商品ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Goods? Goods => Extends.Get(nameof(Goods), k => Goods.FindById(GoodsId));

    /// <summary>商品ID</summary>
    [Map(nameof(GoodsId), typeof(Goods), "Id")]
    public String? GoodsName => Goods?.Name;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="buyerId">买家ID</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<Cart> Search(Int32 buyerId, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (buyerId >= 0) exp &= _.BuyerId == buyerId;
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }
    public static IList<Cart> FindAllByIds(String ids)
    {
        if (ids.IsNullOrEmpty()) return [];
        if (Meta.Session.Count<1000)return  Meta.Cache.FindAll(e => ids.SplitAsInt(",").Contains(e.Id));
        return FindAll(_.Id.In(ids.Trim(',')));
    }

    /// <summary>
    /// 根据SId 商品Id查询购物车
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public static Cart? FindCartBySIdAndSkuId(string SId,long SkuId)
    {
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.BuyerId == SId & e.SKUId == SkuId);
        return Find(_.BuyerId == SId & _.SKUId == SkuId);
    }
    public static List<CartGoodsDto> GetCartInfo(string sId,int lId,decimal exchangeRate)
    {
        if (sId.IsNullOrEmpty()) return [];
        var cartList = FindAll(_.BuyerId == sId).Select(e=>new CartGoodsDto()
        {
            Id = e.Id,
            BuyerId = e.BuyerId ?? "",
            StoreId = e.StoreId,
            StoreName = e.StoreName ?? "",
            GoodsId = e.GoodsId,
            MaterialId = e.MaterialId,
            SKUId = e.SKUId,
            GoodsPrice= (e.GoodsPrice* exchangeRate).ToKeepTwoDecimal(),
            GoodsNum = e.GoodsNum,
            BlId = e.BlId,
            GoodsName = GoodsLan.FindByGIdAndLId(e.GoodsId, lId)?.LanName ?? Goods.FindById(e.GoodsId)?.Name ?? "",
            GoodsImage = GoodsLan.FindByGIdAndLId(e.GoodsId, lId)?.GoodsImage ?? Goods.FindById(e.GoodsId)?.GoodsImage ?? "",
        }).ToList();

        return cartList;
    }
    // Select Count(Id) as Id,Category From DH_Cart Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<Cart> _CategoryCache = new(nameof(Category))
    //{
        //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public ICart ToModel()
    {
        var model = new Cart();
        model.Copy(this);

        return model;
    }
    public static void DelByIds(string ids)
    {
        if (Delete(_.Id.In(ids.Trim(','))) > 0)
            Meta.Cache.Clear("", true);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="storeId">店铺ID</param>
    /// <param name="buyerId">买家ID</param>
    public static void DelByStoreIdAndSID(Int64 storeId,string buyerId)
    {
        if (storeId.IsNull()) return;
        if (buyerId.IsNull()) return;
        if (Delete(_.StoreId == storeId & _.BuyerId == buyerId) > 0)
            Meta.Cache.Clear("", true);
    }
    #endregion
}
