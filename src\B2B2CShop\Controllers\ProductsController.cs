﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using NewLife.Serialization;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.NCube;
using Pek.Seo;
using System.Collections.Generic;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Controllers;

/// <summary>产品列表</summary>
[DHSitemap(IsUse = true)]
public class ProductsController : PekBaseControllerX {


    public IActionResult Index(long cId, bool inventory, bool onSaleint, bool roSh,bool newGoods, string jsonKey,string goodskey, long storeId, int page, int limit = 3)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        var modal = GoodsClass.FindById(cId);
        if (modal == null)
        {
            return Content(GetResource("商品分类不存在"));
        }
        modal.Name = (GoodsClassLan.FindByGIdAndLId(modal.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? modal.Name : GoodsClassLan.FindByGIdAndLId(modal.Id, WorkingLanguage.Id)?.Name;
        viewModel.Entity = modal;
        var userId = ManageProvider.User?.ID ?? 0;
        var list = GoodsSKUDetail.SearchLan(cId, inventory, onSaleint, roSh, newGoods, jsonKey, goodskey, WorkingLanguage.Id, userId, WorkingCurrencies.ExchangeRate, storeId,pages);
        viewModel.ClassAttributesList = ClassAttributes.FindAllByClassIdLan(cId, WorkingLanguage.Id);
        viewModel.GoodsList = list;
        viewModel.Total = pages.TotalCount;
        viewModel.key = goodskey;
        viewModel.page = page;
        viewModel.limit = limit;
        viewModel.inventory = inventory;
        viewModel.roSh = roSh;
        viewModel.newGoods = newGoods;
        viewModel.onSaleint = onSaleint;
        viewModel.cId = cId;
        viewModel.storeId = storeId;
        viewModel.goodsClassArrs = jsonKey.ToJsonEntity<List<CommonDto>>();

        //所有制造商
        viewModel.Storelist = Store.FindAll().ToList().Where(e=> !string.IsNullOrEmpty(e.CompanyName)).ToList();
        return PekView(viewModel,DHSetting.Current.AllowMobileTemp, DHSetting.Current.AllowLanguageTemp);
    }

    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        return [];
    }
}
