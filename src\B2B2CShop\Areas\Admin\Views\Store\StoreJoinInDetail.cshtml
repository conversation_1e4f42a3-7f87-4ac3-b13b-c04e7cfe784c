﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@model StoreJoinIn
@{
}
<div class="page">
 
        <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
            <thead>
                <tr>
                    <th colspan="6">@T("店铺及联系人信息")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>@T("公司名称：")</th>
                    <td>@Model.CompanyName</td>
                    <th>@T("公司所在地：")</th>
                    <td>@(Model.ProvinceName + " " + Model.CityName + " " + Model.CountyName)</td>
                    <th>@T("公司详细地址：")</th>
                    <td>@Model.Address</td>
                </tr>
                <tr>
                    <th>@T("联系人姓名：")</th>
                    <td>@Model.ContactsName</td>
                    <th>@T("联系人电话：")</th>
                    <td>@Model.ContactsPhone</td>
                    <th>@T("电子邮箱：")</th>
                    <td>@Model.ContactsEmail</td>
                </tr>
            </tbody>
        </table>
    @if (Model.Type == 1)
    {
        <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
            <thead>
                <tr>
                    <th colspan="20">@T("证件信息")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th class="w150">@T("证件号码：")</th>
                    <td>@Model.PersonalIdentityNumber</td>
                </tr>
                <!-- 第一处修改：个人证件照片 -->
                <tr>
                    <th>@T("证件照片：")</th>
                    <td colspan="20">
                        <a data-lightbox="lightbox-image" href="@Model.BusinessLicenceNumberElectronic">
                            <img src="@Model.BusinessLicenceNumberElectronic" alt="" style="max-width: 100%; height: auto; width: auto;" />
                        </a>
                    </td>
                </tr>
                
                <!-- 第二处修改：营业执照电子版 -->
                <tr>
                    <th>@T("营业执照<br />电子版：")</th>
                    <td colspan="20">
                        <a data-lightbox="lightbox-image" href="@Model.BusinessLicenceNumberElectronic">
                            <img src="@Model.BusinessLicenceNumberElectronic" alt="" style="max-width: 100%; height: auto; width: auto;" />
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    }
    else
    {
        <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
            <thead>
                <tr>
                    <th colspan="20">@T("营业执照信息（副本）")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th class="w150">@T("营业执照号：")</th>
                    <td>@Model.BusinessLicenceNumber</td>
                </tr>
                <tr>
                    <th>@T("营业执照所在地：")</th>
                    <td>@(Model.BusinessProvinceName + " " + Model.BusinessCityName + " " + Model.BusinessCountyName)</td>
                </tr>
                <tr>
                    <th>@T("注册资金：")</th>
                    <td>@Model.RegisteredCapital</td>
                </tr>
                <tr>
                    <th>@T("营业执照有效期：")</th>
                    <td>@Model.BusinessLicenceStart.ToString("yyyy-MM-dd") - @(Model.BusinessLicenceEnd <= DateTime.MinValue ? @T("长期") : Model.BusinessLicenceEnd.ToString("yyyy-MM-dd"))</td>
                </tr>
                <tr>
                    <th>@T("法定经营范围：")</th>
                    <td colspan="20">@Model.BusinessSphere</td>
                </tr>
                <tr>
                    <th>@T("营业执照<br />电子版：")</th>
                    <td colspan="20">
                        <a data-lightbox="lightbox-image" href="@Model.BusinessLicenceNumberElectronic">
                             <img src="@Model.BusinessLicenceNumberElectronic" alt="" style="max-width: 100%; height: auto; width: auto;" />
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    }
  <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
       <thead>
           <tr>
               <th colspan="20">@T("开户银行信息：")</th>
           </tr>
       </thead>
       <tbody>
           <tr>
               <th class="w150">@T("银行开户名：")</th>
               <td>@Model.BankAccountName</td>
           </tr><tr>
               <th>@T("公司银行账号：")</th>
               <td>@Model.BankAccountNumber</td></tr>
           <tr>
               <th>@T("开户银行支行名称：")</th>
               <td>@Model.BankName</td>
           </tr>
           <tr>
               <th>@T("开户银行所在地：")</th>
               <td colspan="20">@Model.BankAddress</td>
           </tr>
       </tbody>
   </table>
   <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
       <thead>
           <tr>
               <th colspan="20">@T("结算账号信息：")</th>
           </tr>
       </thead>
       <tbody>
           <tr>
               <th class="w150">@T("银行开户名：")</th>
               <td>@Model.SettlementBankAccountName</td>
           </tr>
           <tr>
               <th>@T("公司银行账号：")</th>
               <td>@Model.SettlementBankAccountNumber</td>
           </tr>
           <tr>
               <th>@T("开户银行支行名称：")</th>
               <td>@Model.SettlementBankName</td>
           </tr>
           <tr>
               <th>@T("开户银行所在地：")</th>
               <td>@Model.SettlementBankAddress</td>
           </tr>
       </tbody>
   </table>
    <table border="0" cellpadding="0" cellspacing="0" class="store-joinin ds-default-table">
        <thead>
            <tr>
                <th colspan="20">@T("店铺经营信息")</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <th class="w150">@T("卖家账号：")</th>
                <td>@Model.SellerName</td>
            </tr>
            <tr>
                <th class="w150">@T("店铺名称：")</th>
                <td>@Model.Name</td>
            </tr>
            <tr>
                <th>@T("店铺等级：")</th>
                <td>@Model.StoreGradeName</td>
            </tr>
            <tr>
                <th class="w150">@T("开店时长：")</th>
                <td>@Model.Year @T("年")</td>
            </tr>
            <tr>
                <th>@T("店铺分类：")</th>
                <td>@Model.StoreClassName</td>
            </tr>
            <tr>
                <th>@T("应付总金额：")</th>
                <td>
                    @Model.StoreclassBail @T("元")
                </td>
            </tr>
            <tr>
                <th>@T("经营类目：")</th>
                <td colspan="2">
                    <table border="0" cellpadding="0" cellspacing="0" id="table_category" class="type">
                        <thead>
                            <tr>
                                <th>@T("一级分类")</th>
                                <th>@T("二级分类")</th>
                                <th>@T("比例")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.BusinessClasses)
                            {
                                <tr>
                                    <td>@(GoodsClass.FindById(item.CId1).Name)</td>
                                    <td>@(GoodsClass.FindById(item.CId2).Name)</td>
                                    <td>
                                        @item.CommisRate %
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <th>@T("审核意见：")</th>
                <td colspan="2"><textarea id="joinin_message" name="joinin_message" style="width: 500px;">@Model.Message</textarea></td>
            </tr>
        </tbody>
    </table>
</div>
<link rel="stylesheet" href="/static/plugins/js/jquery.lightbox/css/lightbox.min.css">
<script src="/static/plugins/js/jquery.lightbox/js/lightbox.min.js"></script>
