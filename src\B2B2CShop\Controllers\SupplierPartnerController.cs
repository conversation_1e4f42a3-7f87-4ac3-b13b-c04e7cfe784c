﻿using Aop.Api.Domain;
using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.Seo;
using Pek.Webs;
using System.Dynamic;
using System.Security.Cryptography.X509Certificates;
using XCode.Membership;


namespace B2B2CShop.Controllers
{
    /// <summary>
    /// 供应商合作
    /// </summary>
    [DHSitemap(IsUse = true)]
    public class SupplierPartnerController : PekBaseControllerX
    {
        /// <summary>
        /// 商家入驻首页
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 入驻申请
        /// </summary>
        /// <returns></returns>
        public IActionResult ApplicationEntry()
        {
            return View();
        }

        /// <summary>
        /// 店铺资质
        /// </summary>
        /// <param name="type">1个人/0企业</param>
        /// <returns></returns>
        public IActionResult CompanyQualification(int type)
        {
            ViewBag.Type = type;
            ViewBag.Regions = Regions.FindAllByCIdAndLevel("CN", 0);
            return View();
        }

        /// <summary>
        /// 保存店铺资质内容
        /// </summary>
        /// <param name="Type">个人/公司</param>
        /// <param name="CompanyName">名称</param>
        /// <param name="RegionId">省ID</param>
        /// <param name="CityId">市ID</param>
        /// <param name="AreaId">县/区ID</param>
        /// <param name="Address">详细地址</param>
        /// <param name="Fund">注册资金</param>
        /// <param name="UserName">联系人名称</param>
        /// <param name="Phone">联系电话</param>
        /// <param name="Mail">电子邮件</param>
        /// <param name="BusinessNum">营业执照号</param>
        /// <param name="BusinessRegionId">执照所在省ID</param>
        /// <param name="BusinessCityId">执照所在市ID</param>
        /// <param name="BusinessAreaId">执照所在县/区ID</param>
        /// <param name="BusinessAddress">执照所在地</param>
        /// <param name="dateStart">营业有效期起始时间</param>
        /// <param name="dateEnd">营业有效期结束时间</param>
        /// <param name="Content">法定经营范围</param>
        /// <param name="IdentityNum">证件号</param>
        /// <param name="BusinessFile">执照图片</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavaCompanyQualification(short Type,string CompanyName,int RegionId,int CityId,int AreaId,string Address,decimal Fund, string UserName, string Phone,string Mail,string BusinessNum,int BusinessRegionId,int BusinessCityId, int BusinessAreaId,string BusinessAddress, DateTime dateStart,DateTime dateEnd,string Content, string IdentityNum,IFormFile BusinessFile)
        {
            if (CompanyName.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请输入名称") });
            if (RegionId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择省") });
            if (CityId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择市") });
            if (AreaId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择县/区") });
            if (Address.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请输入详细地址") });
            if (UserName.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请填写联系人名称") });
            if (Phone.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请填写联系人电话") });
            if (Mail.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请填写联系人邮箱") });
            var user = ManageProvider.User;
            if (user==null) return Json(new DResult() { success = false, msg = GetResource("获取不到用户信息") });
            var bytes = BusinessFile.OpenReadStream().ReadBytes(BusinessFile.Length);
            if (!bytes.IsImageFile())
            {
                return Json(new DResult() { success = false, msg = GetResource("非法操作，请上传图片！") });
            }
            // 百度云图片内容审核
            var censorRes = ContentReviewHelp.imageCensor.UserDefined(bytes);
            if (censorRes != null && censorRes["conclusionType"]?.ToInt() == 2)
            {
                //return Prompt(new PromptModel { Message = GetResource("图片内容不合规"), IsOk = false });
                return Json(new { file_id = 0, msg = GetResource("图片内容不合规") });
            }
            if (Type == 0)
            {
                if (Fund <= 0) return Json(new DResult() { success = false, msg = GetResource("请填写注册资金") });
                if (BusinessNum.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("营业执照号") });
                if (BusinessRegionId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择执照所在省ID") });
                if (BusinessCityId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择执照所在市ID") });
                if (BusinessAreaId <= 0) return Json(new DResult() { success = false, msg = GetResource("请选择执照所在县/区ID") });
                if (dateStart.IsNull() || dateStart <= DateTime.MinValue) return Json(new DResult() { success = false, msg = GetResource("请选择有效期") });
                if (Content.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请填写法定经营范围") });
            }
            else
            {
                if (IdentityNum.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = GetResource("请填写证件号码") });
            }
            var storeJoin = new StoreJoinIn();
            storeJoin.Type = Type;
            storeJoin.CompanyName = CompanyName;
            storeJoin.ProvinceId = RegionId;
            storeJoin.ProvinceCode = Regions.FindById(RegionId)?.AreaCode;
            storeJoin.CityId = CityId;
            storeJoin.CountyId = AreaId;
            storeJoin.Address = Address;
            storeJoin.ContactsName = UserName;
            storeJoin.ContactsPhone = Phone;
            storeJoin.ContactsEmail = Mail;
            storeJoin.UId = user.ID;
            storeJoin.UName = user.Name;
            storeJoin.SellerName = user.Code;
            #region 上传图片
            var fileModel = new UploadInfo
            {
                FileSize = BusinessFile.Length,
                FileType = 2,
                //ItemId = Id,
                IsImg = true,
                OriginFileName = BusinessFile.FileName
            };

            var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(BusinessFile.FileName)}";
            var filepath = $"BusinessLicenceInfo/{filename}";
            var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

            filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

            saveFileName.EnsureDirectory();
            BusinessFile.SaveAs(saveFileName);

            fileModel.FileName = filename;
            fileModel.FileUrl = filepath;
            fileModel.Insert();
            #endregion
            storeJoin.BusinessLicenceNumberElectronic = filepath;//证件照片

            if (Type==0)
            {
                storeJoin.RegisteredCapital = Fund;
                storeJoin.BusinessLicenceNumber = BusinessNum;
                storeJoin.BusinessLicenceProvinceId = BusinessRegionId;
                storeJoin.BusinessLicenceCityId = BusinessCityId;
                storeJoin.BusinessLicenceCountyId = BusinessAreaId;
                storeJoin.BusinessLicenceAddress = BusinessAddress;
                storeJoin.BusinessLicenceStart = dateStart;
                storeJoin.BusinessLicenceEnd = dateEnd;
                storeJoin.BusinessSphere = Content;
            }
            else
            {
                storeJoin.PersonalIdentityNumber = IdentityNum;
            }
            storeJoin.Insert();

            return Json(new DResult() { success = true,msg = GetResource("提交成功"), data = storeJoin.Id.SafeString()});//返回入驻ID
        }

        /// <summary>
        /// 财务资质
        /// </summary>
        /// <returns></returns>
        public IActionResult FinanceQualification(long id)
        {
            //if (id <= 0) return Content(GetResource("请填写商家入驻信息"));
            var storeJoinIn = StoreJoinIn.FindById(id);
            if (storeJoinIn == null) storeJoinIn = new StoreJoinIn();
            //if (storeJoinIn == null) return Content(GetResource("请填写商家入驻信息"));
            //if (storeJoinIn.State!=0) return Content(GetResource("商家入驻已申请过"));
            return View(storeJoinIn);
        }

        /// <summary>
        /// 保存财务资质
        /// </summary>
        /// <param name="id">商家入驻ID</param>
        /// <param name="accountName">开户行</param>
        /// <param name="bankAccount">银行账户</param>
        /// <param name="bankAddress">开户行地址</param>
        /// <param name="bankName">开户行名称</param>
        /// <param name="accountName2"></param>
        /// <param name="bankAccount2"></param>
        /// <param name="bankAddress2"></param>
        /// <param name="bankName2"></param>
        /// <param name="agree">是否为结算账号</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SaveFinanceQualification(long id,string accountName,string bankAccount,string bankAddress,string bankName,string accountName2,string bankAccount2,string bankAddress2,string bankName2,bool agree)
        {
            if (id <= 0) return Json(new DResult() { success = false, msg = "请完善公司资质信息" });
            var storeJoinIn = StoreJoinIn.FindById(id);
            if (storeJoinIn==null) return Json(new DResult() { success = false, msg = "请完善公司资质信息" });
            if (accountName.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行" });
            if (bankAccount.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写银行账户" });
            if (bankAddress.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行地址" });
            if (bankName.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行名称" });
            if (!agree)
            {
                if (accountName2.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行" });
                if (bankAccount2.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写银行账户" });
                if (bankAddress2.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行地址" });
                if (bankName2.IsNullOrEmpty()) return Json(new DResult() { success = false, msg = "请填写开户行名称" });
            }
            storeJoinIn.BankAccountName = accountName;
            storeJoinIn.BankAccountNumber = bankAccount;
            storeJoinIn.BankAddress = bankAddress;
            storeJoinIn.BankName = bankName;
            storeJoinIn.IsSettlementAccount = agree ? (short)1 : (short)2;
            if (agree)
            {
                storeJoinIn.SettlementBankAccountName = accountName;
                storeJoinIn.SettlementBankAccountNumber = bankAccount;
                storeJoinIn.SettlementBankAddress = bankAddress;
                storeJoinIn.SettlementBankName = bankName;
            }
            else
            {
                storeJoinIn.SettlementBankAccountName = accountName2;
                storeJoinIn.SettlementBankAccountNumber = bankAccount2;
                storeJoinIn.SettlementBankAddress = bankAddress2;
                storeJoinIn.SettlementBankName = bankName2;
            }
            storeJoinIn.Update();
            return Json(new DResult() { success = true, msg = GetResource("提交成功") });
        }

        /// <summary>
        /// 经营信息
        /// </summary>
        /// <returns></returns>
        public IActionResult BusinessInfo(long id)
        {
            //if (id <= 0) return Content(GetResource("请填写商家入驻信息"));
            var storeJoinIn = StoreJoinIn.FindById(id);
            if (storeJoinIn == null) storeJoinIn = new StoreJoinIn();
            //if (storeJoinIn == null) return Content(GetResource("请填写商家入驻信息"));
            //if (storeJoinIn.State != 0) return Content(GetResource("商家入驻已申请过"));
            //if (storeJoinIn.BankAccountName.IsNullOrEmpty()) return Content(GetResource("请完善财务资质信息"));
            var goodsClass = GoodsClass.FindAllByLevel(0);
            ViewBag.GoodsClass1 = goodsClass;
            return View(storeJoinIn);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">入驻ID</param>
        /// <param name="storeName">店铺名称</param>
        /// <param name="level">店铺等级</param>
        /// <param name="time">开店时长</param>
        /// <param name="storeclass">店铺分类</param>
        /// <param name="cid1s">经营类目</param>
        /// <param name="cid2s"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult SavaBusinessInfo(long id,string storeName,int level,int time,int storeclass,string cid1s, string cid2s)
        {
            return null;
        }
        /// <summary>
        /// 经营类目
        /// </summary>
        /// <returns></returns>
        public IActionResult BusinessCategory()
        {
            return View();
        }

        /// <summary>
        /// 提交成功
        /// </summary>
        /// <returns></returns>
        public IActionResult SubmitSuccessful()
        {
            return View();
        }

        /// <summary>
        /// 合同签订
        /// </summary>
        /// <returns></returns>
        public IActionResult ContractSigning()
        {
            return View();
        }

        /// <summary>
        /// 开通成功
        /// </summary>
        /// <returns></returns>
        public IActionResult ActivationSuccessful()
        {
            return View();
        }

        /// <summary>
        /// 根据地区ID查下级列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public IActionResult GetAddressIdList(int Id)
        {
            var AreaCode = Regions.FindById(Id)?.AreaCode ?? "";
            var list = Regions.FindAllByParentCode(AreaCode);
            return Json(new { data = list });
        }

        /// <summary>
        /// 上传图片
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        public IActionResult UploadImage(IFormFile formFile)
        {
            return null;
        }
    }
}
