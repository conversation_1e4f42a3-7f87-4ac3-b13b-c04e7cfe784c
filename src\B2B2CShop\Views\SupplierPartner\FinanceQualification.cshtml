@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@* 财务资质信息 *@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/finance.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">首页</a>
        <div>></div>
        <a class="textSelect" href="#">
            供应商合作
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            商家入驻申请
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>签订入驻协议</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>公司资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>财务资质信息</p>
            </div>
            <div class="step-item">
                <div class="step-circle">4</div>
                <p>店铺经营信息</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>合同签订</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>店铺开通</p>
            </div>
        </div>

        <!-- 协议内容卡片 -->
        <div class="center-card layui-card">

            <div class="layui-card tip">
                <div class="layui-card-header">注意事项:</div>
                <div class="layui-card-body">
                    以下所需要上传的电子版资质文件仅支持JPG\GIF\PNG格式图片，大小请控制在1M之内。
                </div>
            </div>

            <form class="layui-form">
                <h3 class="big-title">开户银行信息</h3>
                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">银行开户名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="accountName" placeholder="请输入银行开户名" autocomplete="off"
                                class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">公司银行账户</label>
                            <div class="layui-input-inline">
                                <input type="number" name="bankAccount" autocomplete="off" placeholder="请输入公司银行账户"
                                    class="layui-input" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开户银行支行名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="address" placeholder="请输入开户银行支行名称" autocomplete="off"
                                    class="layui-input" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("开户银行所在地")</label>
                            <div class="layui-input-inline">
                                <select name="quiz1" lay-search lay-verify="required" lay-filter="province">
                                    <option value="">请选择省</option>
                                    <option value="浙江">浙江省</option>
                                    <option value="江西">江西省</option>
                                    <option value="福建">福建省</option>
                                    <option value="天津">天津市</option>
                                    <option value="河北">河北省</option>
                                    <option value="山西">山西省</option>

                                </select>
                            </div>
                            <label class="layui-form-label hide">@T("开户银行所在地")</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" id="agreeCheck" name="agree" lay-skin="primary" title="此账号为结算账号"
                                    lay-filter="agreeFilter">
                            </div>

                        </div>
                    </div>
                </div>
                <h3 class="big-title">结算账号信息</h3>
                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">银行开户名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="accountName2" placeholder="请输入银行开户名" autocomplete="off"
                                class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">公司银行账户</label>
                            <div class="layui-input-inline">
                                <input type="number" name="bankAccount2" autocomplete="off" placeholder="请输入公司银行账户"
                                    class="layui-input" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">开户银行支行名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="address2" placeholder="请输入开户银行支行名称" autocomplete="off"
                                    class="layui-input" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("开户银行所在地")</label>
                        <div class="layui-input-inline">
                            <select name="quiz2" lay-search lay-verify="required">
                                <option value="">请选择省</option>
                                <option value="浙江">浙江省</option>
                                <option value="江西">江西省</option>
                                <option value="福建">福建省</option>
                                <option value="天津">天津市</option>
                                <option value="河北">河北省</option>
                                <option value="山西">山西省</option>
                            </select>
                        </div>
                    </div>
                </div>


                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <button id="submitBtn" class="layui-btn btn layui-btn-disabled" layui-btn-disabled lay-submit
                            lay-filter="demo1">下一步</button>
                    </div>
                </div>
            </form>
        </div>



    </div>
</div>
<div class="bug"></div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        layui.use(['form', 'laydate', 'util'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 渲染表单
            form.render();


            // 提交事件
            form.on('submit(demo1)', function (data) {
                var field = data.field; // 获取表单字段值
                console.log("field", field);


                return false; // 阻止默认 form 跳转
            });


            form.on('checkbox(agreeFilter)', function (data) {
                if (data.elem.checked) {
                    // 校验开户银行信息四项是否有值
                    var accountName = $("input[name='accountName']").val();
                    var bankAccount = $("input[name='bankAccount']").val();
                    var address = $("input[name='address']").val();
                    var quiz1 = $("select[name='quiz1']").val();

                    if (!accountName || !bankAccount || !address || !quiz1) {
                        layer.msg('请先完整填写开户银行信息的所有必填项');
                        // 取消勾选
                        $(data.elem).prop('checked', false);
                        form.render('checkbox');
                        return false;
                    }

                    // 同步开户银行信息到结算账号信息
                    $("input[name='accountName2']").val(accountName).prop('disabled', true);
                    $("input[name='bankAccount2']").val(bankAccount).prop('disabled', true);
                    $("input[name='address2']").val(address).prop('disabled', true);
                    $("select[name='quiz2']").val(quiz1).prop('disabled', true);
                } else {
                    // 清空结算账号信息并启用编辑
                    $("input[name='accountName2']").val('').prop('disabled', false);
                    $("input[name='bankAccount2']").val('').prop('disabled', false);
                    $("input[name='address2']").val('').prop('disabled', false);
                    $("select[name='quiz2']").val('').prop('disabled', false);
                }
                form.render();
                checkAllRequiredFields();
                console.log(333);
            });

            // 监听所有必填项有值
            function checkAllRequiredFields() {
                var allFilled = true;
                var requiredFields = [
                    'input[name="accountName"][lay-verify*="required"]',
                    'input[name="bankAccount"][lay-verify*="required"]',
                    'input[name="address"][lay-verify*="required"]',
                    'select[name="quiz1"][lay-verify*="required"]',
                    'input[name="accountName2"][lay-verify*="required"]',
                    'input[name="bankAccount2"][lay-verify*="required"]',
                    'input[name="address2"][lay-verify*="required"]',
                    'select[name="quiz2"][lay-verify*="required"]'
                ];

                requiredFields.forEach(function (selector) {
                    var $field = $(selector);
                    if ($field.length > 0) {
                        var value = $field.val();
                        if (!value || value.trim() === '') {
                            allFilled = false;
                        }
                    }
                });

                var $submitBtn = $('#submitBtn');
                console.log("allFilled", allFilled);
                if (allFilled) {
                    $submitBtn.removeClass('layui-btn-disabled');
                } else {
                    $submitBtn.addClass('layui-btn-disabled');
                }
                
            }

            // 监听必填项的变化
            $(document).on('input change', 'input[lay-verify*="required"], select[lay-verify*="required"]', function () {
                checkAllRequiredFields();

            });

            // 监听 select 下拉框选择事件
            form.on('select', function (data) {
                checkAllRequiredFields();
            });




        });



    });


</script>