﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@* 财务资质信息 *@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/finance.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a class="textSelect" href="@Url.Action("Index")")>
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("公司资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">4</div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <!-- 协议内容卡片 -->
        <div class="center-card layui-card">

            <div class="layui-card tip">
                <div class="layui-card-header">@T("注意事项")</div>
                <div class="layui-card-body">
                    @T("以下所需要上传的电子版资质文件仅支持JPG\\GIF\\PNG格式图片，大小请控制在1M之内。")
                </div>
            </div>

            <form class="layui-form">
                <input type="hidden" name="id" value="@Model.Id" />
                <h3 class="big-title">@T("开户银行信息")</h3>
                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("银行开户名")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="accountName" placeholder="@T("请输入银行开户名")" autocomplete="off"
                                class="layui-input" value="" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("公司银行账户")</label>
                            <div class="layui-input-inline">
                                <input type="number" name="bankAccount" autocomplete="off" placeholder="@T("请输入公司银行账户")"
                                    class="layui-input" lay-verify="required" value="">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("开户银行支行名称")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="bankName" placeholder="@T("请输入开户银行支行名称")" autocomplete="off"
                                    class="layui-input" value="" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("开户银行所在地")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="bankAddress" placeholder="@T("请输入开户银行支行名称")" autocomplete="off"
                                       class="layui-input" value="" lay-verify="required">
                            </div>
                            <label class="layui-form-label hide">@T("开户银行所在地")</label>
                            <div class="layui-input-inline">
                                <input type="checkbox" id="agreeCheck" name="agree" lay-skin="primary" title="@T("此账号为结算账号")"
                                    lay-filter="agree">
                            </div>

                        </div>
                    </div>
                </div>
                <h3 class="big-title">@T("结算账号信息")</h3>
                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("银行开户名")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="accountName2" placeholder="@T("请输入银行开户名")" autocomplete="off"
                                class="layui-input" value="" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("公司银行账户")</label>
                            <div class="layui-input-inline">
                                <input type="number" name="bankAccount2" autocomplete="off" placeholder="@T("请输入公司银行账户")"
                                    class="layui-input" lay-verify="required" value="">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("开户银行支行名称")</label>
                            <div class="layui-input-inline">
                                <input type="text" name="bankName2" placeholder="@T("请输入开户银行支行名称")" autocomplete="off"
                                    class="layui-input" value="" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6 layui-col-xs6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("开户银行所在地")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="bankAddress2" placeholder="@T("请输入开户银行支行名称")" autocomplete="off"
                                   class="layui-input" value="" lay-verify="required">
                        </div>
                    </div>
                </div>


                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit
                            lay-filter="demo1">@T("下一步")</button>
                    </div>
                </div>
            </form>
        </div>



    </div>
</div>
<div class="bug"></div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        layui.use(['form', 'laydate', 'util'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 渲染表单
            form.render();


            // 提交事件
            form.on('submit(demo1)', function (data) {
                var field = data.field; // 获取表单字段值
                field.agree = $('#agreeCheck').prop('checked');
                
                // 调用后端接口
                $.ajax({
                    url: '@Url.Action("SaveFinanceQualification", "SupplierPartner")',
                    type: 'POST',
                    data: {
                        id: field.id,
                        accountName: field.accountName,
                        bankAccount: field.bankAccount,
                        bankAddress: field.bankAddress,
                        bankName: field.bankName,
                        accountName2: field.accountName2,
                        bankAccount2: field.bankAccount2,
                        bankAddress2: field.bankAddress2,
                        bankName2: field.bankName2,
                        agree: field.agree
                    },
                    success: function(response) {
                        if(response.success) {
                            layer.msg('提交成功',{icon: 1});
                            window.location.href = '@Url.Action("BusinessInfo", "SupplierPartner",new { id = Model.Id })';
                        } else {
                            layer.msg(response.msg || '提交失败',{icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请稍后重试',{icon: 2});
                    }
                });
                
                return false; // 阻止默认 form 跳转
            });


            // 同步开户银行信息到结算账号信息的函数
            function syncBankInfoToSettle() {
                if ($('#agreeCheck').prop('checked')) {
                    $("input[name='accountName2']").val($("input[name='accountName']").val());
                    $("input[name='bankAccount2']").val($("input[name='bankAccount']").val());
                    $("input[name='bankName2']").val($("input[name='bankName']").val());
                    $("input[name='bankAddress2']").val($("input[name='bankAddress']").val());
                    // form.render('select');
                }
            }

            // 监听开户银行信息变化，实时同步到结算账号信息
            $("input[name='accountName'], input[name='bankAccount'], input[name='address']").on('input', function () {
                syncBankInfoToSettle();
            });

            // 监听开户银行所在地下拉框变化
            // form.on('select()', function (data) {
            //     if (data.elem.name === 'bankAddress') {
            //         syncBankInfoToSettle();
            //     }
            // });

            form.on('checkbox(agree)', function (data) {
                if (data.elem.checked) {
                    // 先缓存结算账号信息
                    settleAccountCache = {
                        accountName2: $("input[name='accountName2']").val(),
                        bankAccount2: $("input[name='bankAccount2']").val(),
                        bankName2: $("input[name='bankName2']").val(),
                        bankAddress2: $("input[name='bankAddress2']").val()
                    };
                    // 复制开户银行信息到结算账号信息并禁用编辑
                    syncBankInfoToSettle();
                    $("input[name='accountName2'], input[name='bankAccount2'], input[name='bankName2']").prop('disabled', true);
                    $("input[name='bankAddress2']").prop('disabled', true);
                    // form.render('select');
                    // 重新检查必填项
                    checkAllRequired();
                } else {
                    // 还原结算账号信息并启用编辑
                    $("input[name='accountName2']").val(settleAccountCache.accountName2).prop('disabled', false);
                    $("input[name='bankAccount2']").val(settleAccountCache.bankAccount2).prop('disabled', false);
                    $("input[name='bankName2']").val(settleAccountCache.bankName2).prop('disabled', false);
                    $("input[name='bankAddress2']").val(settleAccountCache.bankAddress2).prop('disabled', false);
                    // form.render('select');
                    // 重新检查必填项
                    checkAllRequired();
                }
                form.render();
                checkAllRequiredFields();
                console.log(333);
            });

            // 监听所有必填项有值
            function checkAllRequiredFields() {
                var allFilled = true;
                var requiredFields = [
                    'input[name="accountName"][lay-verify*="required"]',
                    'input[name="bankAccount"][lay-verify*="required"]',
                    'input[name="bankName"][lay-verify*="required"]',
                    'input[name="bankAddress"][lay-verify*="required"]',
                    'input[name="accountName2"][lay-verify*="required"]',
                    'input[name="bankAccount2"][lay-verify*="required"]',
                    'input[name="bankName2"][lay-verify*="required"]',
                    'input[name="bankAddress2"][lay-verify*="required"]'
                ];

                requiredFields.forEach(function (selector) {
                    var $field = $(selector);
                    if ($field.length > 0) {
                        var value = $field.val();
                        if (!value || value.trim() === '') {
                            allFilled = false;
                        }
                    }
                });

                var $submitBtn = $('#submitBtn');
                console.log("allFilled", allFilled);
                if (allFilled) {
                    $submitBtn.removeClass('layui-btn-disabled');
                } else {
                    $submitBtn.addClass('layui-btn-disabled');
                }
                
            }

            // 监听所有必填字段的变化
            $(document).on('input change', 'input[lay-verify*="required"], input[lay-verify*="required"]', function () {
                checkAllRequired();
            });

            // // 监听下拉框选择事件
            // form.on('select()', function (data) {
            //     checkAllRequired();
            // });




        });



    });


</script>