.main {
  background-color: var(--white2);
  padding: 0 10vw;
  box-sizing: border-box;
  width: 100%;
  margin-left: 0;

}

.header2,
.headerItem {
  display: none;

}

.header {
  padding: 1vw 7vw;
}

.breadBox {
  border-bottom: none;
}

/* 步骤条美化 */
.steps-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
  gap: 0;
  border-radius: 8px;
  padding: 24px 0 16px 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1 1 0;
  min-width: 120px;
  position: relative;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--text-color4);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 6px;
  z-index: 2;
  border: 4px solid var(--white2);
}

.step-item.active .step-circle {
  background: var(--blue-deep);
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -50%;
  top: 16px;
  width: 100%;
  height: 4px;
  background: var(--text-color4);
  z-index: 0;
}

.step-item.active:nth-child(1):after,
.step-item.active:nth-child(2):after,
.step-item.active:nth-child(3):after,
.step-item.active:nth-child(4):after{
  background-color: var(--blue-deep);
}


.step-item.active p {
  color: var(--blue-deep);
}

.step-item p {
  margin: 0;
  font-size: 15px;
  color: var(--text-color2);
}

.step-item:last-child::after {
  display: none;
}

/* 卡片美化 */
.center-card {
  margin: 0 auto;
  background: var(--white);
  border-radius: 10px;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
  padding: 20px 40px 40px 40px;
}

.successBox{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--text-color);
  margin: 20px 0 40px;
  gap: 20px;
}


.layui-card-body {
  color: var(--text-color3);
}

.layui-container {
  width: auto !important;
}

.bug {
  background-color: var(--white2);
}

.layui-icon-ok {
  font-size: 24px;
  color: var(--white);
}


.textSelect {
  color: var(--blue-deep) !important;
}

.layui-btn:hover {
  opacity: 1;

}


.layui-btn-container .layui-btn {
  width: 100px;
  border-radius: 5px;
}


.btnBox {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.btn {
  width: 176px;
  border-radius: 50px;
  margin: 20px 0 20px;
}



.layui-form {
  color: var(--text-color3);
  margin-right: 110px;
  gap: 40px;
}


.add-btn {
  width: 200px;
  background-color: #E4E4E4;
  color: #666666;
  border-radius: 5px;
  border: 1px solid var(--text-color4);
}


.category-table {
  width: 100%;
  border: 1px solid #BABABA;
  margin-top: 30px;
}
.category-table td {
  border: 1px solid #BABABA;
}

.category-table th {
  padding: 15px;
  min-width: 80px;
  color: var(--text-color);
  background-color: #E4E4E4;
  text-align: left;
}

.category-table td {
  padding: 15px;
}

.category-table td:nth-child(odd) {
  width: 84px;
  text-align: right;
}
.category-table td:nth-child(even) {
  text-align: left;

}

.category-table .inside {
  text-align: center;
  border: 1px solid #BABABA;
}

.category-table .inside th {
  text-align: center;
}
.category-table .inside td {
  border: none;
  text-align: center;
}


.btn-smBox .layui-btn-sm {
  width: 76px;
}

.btn-smBox {
  display: flex;
  align-items: center;
  height: 38px;
}
.mb{
  margin-bottom: 4px;
}

.main {
  margin-left: initial !important;
  width: 100% !important;
  font-size: initial !important;
}


.pic{
  width: 80px;
  height: 80px;
  object-fit: contain;
}

/* 添加图片容器样式 */
.material-image-container {
    display: flex;
    flex-wrap: wrap; /* 图片过多时换行 */
    gap: 10px; /* 图片之间的间距 */
}