﻿<script asp-location="Head">
</script>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3 class="ny-panel-title">@("店铺管理")</h3>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("StoreApply")"><span>@T("开店申请")</span></a></li>
                <li><a href="javascript:void(0)" onclick="openAddStoreModal()"><span>@T("新增外驻店铺")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="get" name="formSearch" id="formSearch">
        <div class="ds-search-form">
            <dl>
                <dt>@T("所属等级")：</dt>
                <dd>
                    <select name="belongingLevel">
                        <option value="-1">@T("请选择")</option>
                        @foreach(var item in ViewBag.StoreGradeList)
                        {
                            <!option value="@item.Value" @(Model.belongingLevel == item.Value ? "selected" : "")>@item.Text</!option>
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("店主")</dt>
                <dd>
                    <input type="text" value="@Model.shopkeeper" id="shopkeeper" name="shopkeeper">
                </dd>
            </dl>
            <dl>
                <dt>@T("店铺名称")</dt>
                <dd>
                    <input type="text" value="@Model.shopNames" id="shopNames" name="shopNames">
                </dd>
            </dl>
            <div class="btn_group">
                <input type="submit" class="btn" value="@T("搜索")">
            </div>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr style="text-align:center;">
                <th class="w24"></th>
                <th>@T("排序")</th>
                <th>@T("店铺名称")</th>
                <th>@T("店铺ID")</th>
                <th>@T("店主账号")</th>
                <th>@T("卖家账号")</th>
                <th>@T("店铺资金")</th>
                <th>@T("店铺保证金")</th>
                <th>@T("所属等级")</th>
                <th>@T("有效期至")</th>
                <th>@T("状态")</th>
                <th style="text-align:center;">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (Store item in Model.list)
            {
                <tr id="<EMAIL>">

                    <td><input type="checkbox" class="checkitem" value="@item.Id" /></td>
                    <td class="w48 sort"><span title="@T("可编辑")" ajax_branch="goods_class_sort" datatype="number" fieldid="@item.Id" fieldname="gc_sort" ds_type="inline_edit" class="editable">@item.Sort</span></td>
                    <td>@item.Name</td>
                    <td>@item.Id</td>
                    <td>@item.UName</td>
                    <td>@item.SellerName</td>
                    <td>
                        <div>@T("可用")：<span style="color: red;">@item.AvaliableMoney</span>@T("元")</div>
                        <div>@T("冻结")：<span style="color: red;">@item.FreezeMoney</span>@T("元")</div>
                    </td>
                    <td><span style="color: red;">@item.AvaliableDeposit</span>@T("元")</td>
                    <td>@StoreGrade.FindById(item.GradeId)?.Name</td>
                    <td>@item.EndTime</td>
                    <td>
                        @switch (item.State)
                        {
                            case (short)0:
                                @T("关闭")
                                break;
                            case (short)1:
                                @T("开启")
                                break;
                            case (short)2:
                                @T("审核中")
                                break;
                            default:
                                @T("未知状态")
                                break;
                        }
                    </td>
                    <td>
                        <a class="dsui-btn-edit" href="@Url.Action("EditStore",new { Id=item.Id})"><i class="iconfont"></i>@T("编辑")</a>
                    </td>
                </tr>
            }
        </tbody>
        <tfoot>
            @* <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small" onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr> *@
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script type="text/javascript" src="~/static/admin/js/jquery.edit.js" charset="utf-8"></script>

<script asp-location="Footer">
    function submit_delete(ids_str) {
        _uri = "@Url.Action("Delete")?Ids=" + ids_str;
        dsLayerConfirm(_uri, '您确定要删除吗?');
    }

    function openAddStoreModal() {
        layui.use('layer', function(){
            var layer = layui.layer;
            layer.open({
                type: 2,
                title: '新增外驻店铺',
                shadeClose: true,
                shade: 0.8,
                area: ['900px', '500px'],
                fixed: false, //不固定
                maxmin: true, // 启用最大化最小化按钮
                content: '@Url.Action("AddStore")' // iframe的url
            });
        });
    }
</script>