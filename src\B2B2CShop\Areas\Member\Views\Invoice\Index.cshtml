@using B2B2CShop.Dto
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/invoice.css");

    var site = SiteInfo.GetDefaultSeo();
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }
</style>
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Order")">@T("发票")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content">
            <form method="get">
                <div class="filterBox">
                    <div style="width: 40%;margin: 0;">
                        <div class="layui-form"><input class="layui-input" placeholder="@T("请输入订单号/BOM标识进行查询")" value="@Model.key" name="key"></div>
                    </div>
                    <button class="button button_blue">@T("查询")</button>
                </div>
            </form>

            <!--  -->
            <div class="tablesBox">
                <table class="layui-table" style="background-color: white;">
                    <colgroup>
                        <col width="55%">
                        <col width="15%">
                        <col width="15%">
                    </colgroup>
                    @foreach(OrderDto order in Model.list)
                    {
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th>
                                    <span class="layui-form" style="margin-right: .5vw;">
                                        @* <input type="checkbox" name="BBB" checked> *@
                                    </span>
                                    <span style="margin-right: 5%">
                                        @(order.AddTime?.ToString("yyyy-MM-dd HH:mm:ss"))
                                    </span>
                                    @T("订单编号") :
                                    <span id="orderId"> @order.OrderSn</span>
                                    <span class="textSelect pointer" id="copy" onclick="copy('#orderId')">
                                        @T("复制")
                                    </span>
                                </th>
                                <th class="tableIcon" style="margin-right: auto;">
                                    @* <i class="iconfont  icon-zaixiankefu1" onclick="toRouter(this)" data-link="../userInfo/message.html"></i> *@
                                </th>
                                <th></th>
                                <th class="tableIcon" style="text-align: right;">
                                   @*  <i class="iconfont  icon-icdelete" style="font-size: 1.2vw;"></i> *@
                                </th>
                            </tr>
                            <!--   -->
                        </thead>

                        <tbody>
                            @foreach (OrderGoodsDto goods in order.OrderGoods??[])
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo">
                                            <div class="layui-form" style="margin-right: .5vw;">
                                                @* <input type="checkbox" name="BBB" checked> *@
                                            </div>
                                            <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                <img src="@goods.GoodsImage" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                            </div>
                                            <div class="goodsInformation"
                                            style="width: 60%;margin-left: 5%;margin-right: auto;">
                                                <div class="tdTitle"> <span class="textOver2">@goods.GoodsName</span> </div>
                                                @* <div>原厂编号<span class="name textOver">ACDC电源模块</span></div> *@
                                                <div>@T("制造商"): <span class="name textOver">@goods.CompanyName</span> </div>
                                                @*                                                 <div class="textOver">
                                                    制造商编号: <span class="name textOver">727-S40FC008C3B1V000 </span>
                                                </div> *@
                                                <div class="textOver">
                                                    @T("型号"):<span class="name textOver">@goods.SpecValue</span>
                                                </div>
                                                @*                                              <div class="textOver">
                                                    客户编号:<span class="name">220V转5V3.3V9V12V15V24V</span>
                                                </div> *@
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>@T("商品数量"): *@goods.GoodsNum </div>
                                        <div>@T("商品单价")：@goods.SymbolLeft @goods.GoodsPayPrice.ToString("F2") </div>
                                        
                                    </td>
                                    @if (goods == order.OrderGoods?[0])
                                    {
                                        <td rowspan="2" colspan="1" class="fnTd">
                                            <div class="hover">@T("交易成功")</div>
                                            <div class="hover">@T("订单详情")</div>
                                            @* <div class="redmi" onclick="toRouter(this)" data-link="../orders/express.html">@T("查看物流")</div> *@
                                        </td>
                                        <td class="tableBtnBox" rowspan="2" colspan="1">
                                            <div>@T("运费"): @order.SymbolLeft @order.ShippingFee?.ToString("F2")</div>
                                            <div>@T("合计")：<span class="money">@order.SymbolLeft @order.OrderAmount?.ToString("F2") </span> </div>
                                            <div>
                                                @* <button class="button textSelect" onclick="download('url','@T("发票")')">@T("下载发票")</button> *@
                                                <button class="button textSelect textOver" onclick="generateInvoice('@order.Id')">@T("下载发票")</button>
                                            </div>
                                        </td>
                                    }
                                </tr>
                            }
                        </tbody>
                        }
                    </table>
                    <div id="pagingBox" style="text-align: right;"></div>
                    <script>
                        layui.use(function () {
                            var laypage = layui.laypage;
                            laypage.render({
                            elem: 'pagingBox',
                            count: @Model.total, // 数据总数
                            limit: @(Model.limit > 0 ? Model.limit : 10), // 每页显示条数
                            limits: [5,10, 20, 50, 100], // 每页条数的选择项
                            curr: @(Model.page > 0 ? Model.page : 1), // 当前页码
                            groups: 5, // 连续显示页码个数
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                            theme: '#2C79E8', // 自定义主题色
                            prev: '@T("上一页")',
                            next: '@T("下一页")',
                            first: '@T("首页")',
                            last: '@T("尾页")',
                            countText: ['@T("共") ',' @T("条")'],
                            skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                            limitTemplet: function(item) {
                              return item + ' @T("条/页")';
                            },
                            jump: function(obj, first) {
                                // 首次不执行（首次加载时不跳转）
                                if (!first) {
                                    // 创建一个隐藏的表单来提交所有参数
                                    var $form = $('<form></form>');
                                    $form.attr('action', '@Url.Action("Index")');
                                    $form.attr('method', 'get');
                                    $form.css('display', 'none');

                                    // 添加页码参数
                                    $form.append('<input type="hidden" name="page" value="' + obj.curr + '" />');

                                    // 添加每页条数参数
                                    $form.append('<input type="hidden" name="limit" value="' + obj.limit + '" />');

                                    // 获取当前表单中的所有参数
                                    $('form:first input, form:first select').each(function() {
                                        var name = $(this).attr('name');
                                        var value = $(this).val();

                                        // 如果存在名称和值，并且不是页码相关参数，则添加到隐藏表单中
                                        if (name && value && name !== 'page' && name !== 'limit') {
                                            $form.append('<input type="hidden" name="' + name + '" value="' + value + '" />');
                                        }
                                    });

                                    // 将表单添加到文档中并提交
                                    $('body').append($form);
                                    $form.submit();
                                }
                            }
                        });
                        });
                    </script>

                </div>

            </div>
    </div>

</div>
<script>
    function generateInvoice(orderId){
        $.post('@Url.Action("GenerateInvoice")',{orderId},function(res){
            if(res.success){
                const link = document.createElement('a');
                link.href = res.msg; 
                link.download = '@T("发票")';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        })
    }
</script>