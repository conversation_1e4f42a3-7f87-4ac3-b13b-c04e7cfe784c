﻿@model Currencies
<style asp-location="true">
    .red {
        color: red
    }
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("物料入库")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")"><span>@T("管理")</span></a></li>
                <li><a href="@Url.Action("Add")" class="current"><span>@T("入库")</span></a></li>
            </ul>
        </div>
    </div>
    <form method="post" action="@Url.Action("Add")" id="doc_form">
        <div class="ds-default-form">
            <br />
            <dl>
                <dd>
                    @T("仓库：")
                    <select name="WareHouseId" class="txt">
                        <option value=0>@T("请选择仓库")</option>
                        @foreach (var item in (IEnumerable<WareHouse>)ViewBag.WareHouseList)
                        {
                            <option value=@item.Id>@T(item.Name)</option>
                        }
                    </select>
                </dd>
            </dl>
            <br />
            <dl>
                <dd>
                    @T("物料：")
                    <select name="MerchantMaterialId" id="MerchantMaterialId" class="select txt">
                        <option value=0>@T("请选择物料")</option>
                        @foreach (var item in (IEnumerable<MerchantMaterial>)ViewBag.MaterialList)
                        {
                            <option value=@item.Id>@T(item.Name)</option>
                        }
                    </select>
                </dd>
            </dl>
            <br />
            <dl>
                <dd>@T("数量：")<input type="text" name="Quantity" class="txt"></dd>
            </dl>
            <br />
            <div class="btn_group">
                <button type="submit" class="btn">@T("保存")</button>
            </div>
        </div>
    </form>
</div>
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>
<script>
    $(document).ready(function () {
        $('#MerchantMaterialId').select2({
            placeholder: "@T("请选择物料")",
            allowClear: true
        });

        // 新的防抖实现方式
        let isSubmitting = false;
        
        $('#doc_form').on('submit', function(e) {
            if (isSubmitting) {
                e.preventDefault();
                return false;
            }
            
            // 禁用按钮并显示加载状态
            $('button[type="submit"]')
                .prop('disabled', true)
                .text('@T("提交中...")');
            
            isSubmitting = true;
            
            // 允许表单正常提交
            return true;
        });
        
        // 表单提交完成后重置状态（适用于ajax提交）
        $(document).ajaxComplete(function() {
            isSubmitting = false;
            $('button[type="submit"]')
                .prop('disabled', false)
                .text('@T("保存")');
        });
    });
</script>
