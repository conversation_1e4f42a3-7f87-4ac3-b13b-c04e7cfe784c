<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <AssemblyTitle>外贸平台</AssemblyTitle>
    <Description>外贸电商平台</Description>
    <Company>深圳市海凌科电子有限公司</Company>
    <Copyright>©2020-2025 深圳市海凌科电子有限公司</Copyright>
    <VersionPrefix>1.0</VersionPrefix>
    <VersionSuffix>$([System.DateTime]::Now.ToString(`yyyy.MMdd`))</VersionSuffix>
    <Version>$(VersionPrefix).$(VersionSuffix)</Version>
    <FileVersion>$(Version)</FileVersion>
    <AssemblyVersion>$(VersionPrefix).*</AssemblyVersion>
    <Deterministic>false</Deterministic>
    <OutputPath>..\..\Bin\B2B2CShop</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
    <NoWarn>1701;1702;NU5104;NETSDK1138;CS7035</NoWarn>

    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AlipaySDKNet.Standard" Version="4.9.627" />
    <PackageReference Include="Baidu.AI" Version="4.15.16" />
    <PackageReference Include="DH.Magicodes.IE.Core" Version="4.0.2025.72200084" />
    <PackageReference Include="DH.Magicodes.IE.Excel" Version="4.0.2025.72200084" />
    <PackageReference Include="DH.MiniExcel" Version="4.1.2025.318-beta0914" />
    <PackageReference Include="DH.NRedis.Extensions" Version="4.13.2025.713-beta1514" />
    <PackageReference Include="DH.NStardust.Extensions" Version="4.13.2025.717-beta0836" />
    <PackageReference Include="DH.SLazyCaptcha" Version="4.0.2025.709-beta0819" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="PayPalServerSDK" Version="0.6.1" />
    <PackageReference Include="Pek.AspNetCore" Version="4.12.2025.724-beta0708" />
    <PackageReference Include="Pek.Common" Version="4.12.2025.724-beta0707" />
    <PackageReference Include="Pek.ExChangeRate" Version="4.11.2025.306-beta1124" />
    <PackageReference Include="Pek.Mail.Extensions" Version="4.0.2025.324-beta0154" />
    <PackageReference Include="Pek.NCubeUI" Version="4.13.2025.724-beta1202" />
    <PackageReference Include="Pek.NCubeUI.DsMallUI" Version="4.11.2025.323-beta1201" />
    <PackageReference Include="Pek.Sms.FengHuo" Version="4.0.2025.508-beta1133" />
    <PackageReference Include="Pek.WAF" Version="4.12.2025.713-beta0226" />
    <PackageReference Include="SKIT.FlurlHttpClient.Wechat.TenpayV3" Version="3.13.0" />
  </ItemGroup>
  <ItemGroup>
    <Using Include="NewLife" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)' == 'Debug'">
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Content Update="Themes\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Content Update="Data\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
