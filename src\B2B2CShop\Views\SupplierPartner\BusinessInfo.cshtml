@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*经营信息*@
@model StoreJoinIn
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/businessInfo.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">@T("首页")</a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("供应商合作")
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            @T("商家入驻申请")
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("签订入驻协议")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("公司资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("财务资质信息")</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>@T("店铺经营信息")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>@T("合同签订")</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>@T("店铺开通")</p>
            </div>
        </div>

        <!-- 协议内容卡片 -->
        <div class="center-card layui-card">

            <div class="layui-card tip">
                <div class="layui-card-header">@T("注意事项")</div>
                <div class="layui-card-body">
                    @T("以下所需要上传的电子版资质文件仅支持JPG\\GIF\\PNG格式图片，大小请控制在1M之内。")
                </div>
            </div>

            <form class="layui-form">
                <h3 class="big-title">@T("店铺经营信息")</h3>
                <input type="hidden" name="id" value="@Model.Id"/> 
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item mb">
                            <label class="layui-form-label">@T("店铺名称")</label>
                            <div class="layui-input-inline first-input">
                                <input type="text" name="storeName" placeholder="@T("请输入店铺名称")" autocomplete="off"
                                    class="layui-input" value="" lay-verify="required">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label hide">@T("店铺名称")</label>
                            <div class="layui-input-inline first-input red-text">
                                @T("店铺名称注册后不可修改，请认真填写")
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("店铺等级")</label>
                            <div class="layui-input-inline">
                                <select name="grade" lay-search lay-verify="required" lay-filter="grade">
                                    <option value="">@T("请选择")</option>
                                    @foreach (StoreGrade item in ViewBag.StoreGradelList)
                                    {
                                        <option value="@item.Id" data-goodsnum="@item.GoodsLimit" data-price="@item.Price">@item.Name</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row layui-hide" id="gradeInfo">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label hide">@T("店铺等级")</label>
                            <div class="layui-input-inline first-input blue-text">
                                <span>@T("商品发布数")：<strong id="goodsnum">0</strong> @T("收费标准")：<strong id="charge">0</strong>@T("元/年")</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("店铺时长")</label>
                            <div class="layui-input-inline">
                                <select name="year" lay-search lay-verify="required" lay-filter="year">
                                    <option value="1" selected>1 @T("年")</option>
                                    <option value="2">2 @T("年")</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item mb">
                            <label class="layui-form-label">@T("店铺分类")</label>
                            <div class="layui-input-inline">
                                <select name="storeclass" lay-search lay-verify="required" lay-filter="storeclass">
                                    <option value="">@T("请选择")</option>
                                    @foreach (StoreClass item in ViewBag.StoreClassList)    
                                    {
                                        <option value="@item.Id">@item.Name</option>
                                    }
                                </select>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">@T("店铺分类")</label>
                        <div class="layui-input-block red-text">
                            @T("请根据您所经营的内容认真选择店铺分类，注册后商家不可自行修改。")
                        </div>
                    </div>
                </div>
                <div class="layui-form-item mb">
                    <label class="layui-form-label">@T("经营类目")</label>
                    <div class="oneness layui-hide">
                        <div class="layui-input-inline">
                            <select name="cId1" lay-filter="cId1">
                                <option value="">@T("请选择")</option>
                                @foreach (GoodsClass item in ViewBag.GoodsClassList1)
                                {
                                    <option value="@item.Id" >@item.Name</option>
                                }
                            </select>
                        </div>
                        <div class="layui-input-inline layui-hide">
                            <select name="cId2" lay-filter="cId2">
                                <option value="">@T("请选择")</option>
                            </select>
                        </div>
                        <div class="layui-input-inline btn-smBox">
                            <button type="button" class="yes layui-btn layui-btn-primary layui-btn-sm">@T("确认")</button>
                            <button type="button" class="no layui-btn layui-btn-primary layui-btn-sm">@T("取消")</button>
                        </div>
                    </div>
                    @* layui-show *@
                    <div class="layui-input-inline addBox">
                        <button type="button" class="layui-btn add-btn">
                            <i class="layui-icon layui-icon-addition"></i> @T("选择添加类目")
                        </button>
                    </div>

                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">@T("经营类目")</label>
                        <div class="layui-input-inline red-text">
                            @T("请选择最后一级类目")
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">表格</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table">
                                <tr>
                                    <th>@T("一级类目")</th>
                                    <th>@T("二级类目")</th>
                                    <th>@T("操作")</th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>


                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit
                            lay-filter="demo1">@T("提交申请")</button>
                    </div>
                </div>
            </form>
        </div>



    </div>
</div>
<div class="bug"></div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        layui.use(['form', 'laydate', 'util'], function () {
            var form = layui.form;
            var layer = layui.layer;

            var tableData = []; // 用于存储表格数据

            // 渲染表单
            form.render();

            // 监听店铺等级选择显示收费标准
            form.on('select(grade)', function(data){
                var $selected = $(data.elem).find("option:selected");
                var goodsNum = $selected.data('goodsnum');
                var price = $selected.data('price');
                
                if(data.value) {
                    $('#goodsnum').text(goodsNum || 0);
                    $('#charge').text(price || 0);
                    $('#gradeInfo').removeClass('layui-hide');
                } else {
                    $('#gradeInfo').addClass('layui-hide');
                }
            });

            // 提交事件
            form.on('submit(demo1)', function (data) {
                var field = data.field; // 获取表单字段值
                
                var tableData = getTableData();
                
                if (tableData.length === 0) {
                    layer.msg('@T("请添加经营类目")');
                    return false;
                }
                
                // 准备提交数据
                var postData = {
                    id: field.id || 0, // 根据实际情况获取ID
                    storeName: field.storeName,
                    grade: parseInt(field.grade),
                    year: parseInt(field.year),
                    storeclass: parseInt(field.storeclass),
                    clds: tableData.map(id => id.toString()) // 将long数组转为string数组
                };
                
                // 调用接口
                $.ajax({
                    url: '@Url.Action("SavaBusinessInfo")', // 根据实际路由调整
                    type: 'POST',
                    data: postData,
                    success: function (res) {
                        if (res.success) {
                            layer.msg(res.msg ||'@T("提交成功")');
                            // 跳转到成功页面或其他操作
                            window.location.href = '@Url.Action("SubmitSuccessful", new { id = Model.Id})'
                        } else {
                            layer.msg(res.msg || '@T("提交失败")');
                        }
                    },
                    error: function () {
                        layer.msg('@T("网络错误，请重试")');
                    }
                });
                
                return false; // 阻止默认 form 跳转
            });

            // 检查所有必填项和表格数据
            function checkFormCompletion() {
                var allFieldsValid = true;
                $('.layui-form [lay-verify="required"]').each(function () {
                    if (!$(this).val()) {
                        allFieldsValid = false;
                        return false; // 退出循环
                    }
                });

                var tableDataNotEmpty = $('.category-table tr:gt(0)').length > 0;

                if (allFieldsValid && tableDataNotEmpty) {
                    $('#submitBtn').removeClass('layui-btn-disabled');
                } else {
                    $('#submitBtn').addClass('layui-btn-disabled');
                }
            }

            // 监听表单输入变化
            $('.layui-form [lay-verify="required"]').on('input change', function () {
                checkFormCompletion();
            });

            // 使用 MutationObserver 监听表格数据变化
            const tableObserver = new MutationObserver(function () {
                checkFormCompletion();
            });

            tableObserver.observe(document.querySelector('.category-table'), {
                childList: true, // 监听子节点的变化
                subtree: false // 不监听后代节点
            });


            // 点击 "添加" 按钮
            $('.addBox').on('click', function () {
                $('.oneness').removeClass('layui-hide').addClass('layui-show');
                $(this).addClass('layui-hide');
            });

            // 点击 "取消" 按钮
            $('.btn-smBox .no').on('click', function () {
                // 隐藏oneness，显示addBox
                $('.oneness').removeClass('layui-show').addClass('layui-hide');
                $('.addBox').removeClass('layui-hide');
                // 重置级联选择
                $('select[name="cId1"]').val('');
                $('select[name="cId2"]').val('');
                // 隐藏cId2的容器
                $('select[name="cId2"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                // 重新渲染表单
                form.render('select');
            });

            // 监听cId1的选择
            form.on('select(cId1)', function (data) {
                var quiz2Container = $('select[name="cId2"]').closest('.layui-input-inline');
                if (data.value) {
                    quiz2Container.removeClass('layui-hide').addClass('layui-show');
                    //调用接口获取二级分类
                    $.ajax({
                        url: '/SupplierPartner/SearchGoodsClass',
                        type: 'GET',
                        data: { pId: data.value },
                        success: function(res) {
                            if(res.code === 0 && res.data.length > 0) {
                                var $select = $('select[name="cId2"]');
                                $select.empty().append('<option value="">@T("请选择")</option>');
                                
                                $.each(res.data, function(i, item) {
                                    $select.append('<option value="' + item.Id + '" ' +
                                        'data-id="' + item.Id + '" ' +
                                        'data-commission-rate="' + item.CommisRate + '">'+ item.Name +'</option>');
                                });
                                form.render('select');
                            } else {
                                layer.msg('暂无二级分类数据');
                            }
                        },
                        error: function() {
                            layer.msg('获取分类数据失败');
                        }
                    });
                } else {
                    quiz2Container.removeClass('layui-show').addClass('layui-hide');
                    $('select[name="cId2"]').val('');
                    form.render('select');
                }
            });

            // 监听 select 下拉框选择事件
            form.on('select', function (data) {
                checkFormCompletion();
            });



            // 删除行
            $('.category-table').on('click', '.del', function () {
                $(this).closest('tr').remove();
                checkFormCompletion(); // 检查表单完成状态
            });


            // 点击 "确认" 按钮
            $('.btn-smBox .yes').on('click', function () {
                var cId1 = $('select[name="cId1"]').val();
                var cId2 = $('select[name="cId2"]').val();
                var quiz2Id = $('select[name="cId2"] option:selected').data('id'); // 获取 data-id

                var tdVal1 = $('select[name="cId1"] option:selected').text();
                var tdVal2 = $('select[name="cId2"] option:selected').text();
                var commissionRate = $('select[name="cId2"] option:selected').data('commission-rate');

                var existingCIds = getTableData();
                // 检查是否重复
                if (existingCIds.includes(cId2)) {
                    layer.msg('该类目已存在，请选择其他类目');
                    return;
                }

                if (cId1 && cId2 && quiz2Id) {
                    var newRow = `
                        <tr data-id="${quiz2Id}">
                            <td data-id="${cId1}">${tdVal1}</td>
                            <td data-id="${cId2}" data-commission-rate="${commissionRate}">${tdVal2} 分佣比例${commissionRate}%</td>
                            <td class="del">删除</td>
                        </tr>
                    `;
                    $('.category-table').append(newRow);

                    // 重置级联选择
                    $('select[name="cId1"]').val('');
                    $('select[name="cId2"]').val('');
                    $('select[name="cId2"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                    form.render('select');

                    // 隐藏oneness，显示addBox
                    $('.oneness').removeClass('layui-show').addClass('layui-hide');
                    $('.addBox').removeClass('layui-hide');

                    // 检查表单完成状态
                    checkFormCompletion();
                } else {
                    layer.msg('请完整选择类目');
                }
            });


            //获取表格内容
            function getTableData() {
                var cIds = [];
                $('.category-table tr:gt(0)').each(function () {
                    var row = $(this);
                    var cId = row.find('td:eq(1)').data('id');
                    if (cId) cIds.push(cId);
                });
                return cIds;
            }
        });
    });
</script>