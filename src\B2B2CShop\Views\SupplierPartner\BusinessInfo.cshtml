﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@*经营信息*@
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/businessInfo.css");
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        <a href="/">首页</a>
        <div>></div>
        <a class="textSelect" href="#">
            供应商合作
        </a>
        <div>></div>
        <a class="textSelect" href="#">
            商家入驻申请
        </a>
    </div>
    <div class="layui-container">
        <!-- 步骤条 -->
        <div class="steps-bar">
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>签订入驻协议</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>公司资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>财务资质信息</p>
            </div>
            <div class="step-item active">
                <div class="step-circle">
                    <i class="layui-icon layui-icon-ok"></i>
                </div>
                <p>店铺经营信息</p>
            </div>
            <div class="step-item">
                <div class="step-circle">5</div>
                <p>合同签订</p>
            </div>
            <div class="step-item">
                <div class="step-circle">6</div>
                <p>店铺开通</p>
            </div>
        </div>

        <!-- 协议内容卡片 -->
        <div class="center-card layui-card">

            <div class="layui-card tip">
                <div class="layui-card-header">注意事项:</div>
                <div class="layui-card-body">
                    以下所需要上传的电子版资质文件仅支持JPG\GIF\PNG格式图片，大小请控制在1M之内。
                </div>
            </div>

            <form class="layui-form">
                <h3 class="big-title">店铺经营信息</h3>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item mb">
                            <label class="layui-form-label">店铺名称</label>
                            <div class="layui-input-inline first-input">
                                <input type="text" name="accountName" placeholder="请输入店铺名称" autocomplete="off"
                                    class="layui-input" value="店铺名称" lay-verify="required">
                            </div>

                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label hide">店铺名称</label>
                            <div class="layui-input-inline first-input red-text">
                                店铺名称注册后不可修改，请认真填写
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("店铺等级")</label>
                            <div class="layui-input-inline">
                                <select name="grade" lay-search lay-verify="required" lay-filter="grade">
                                    <option value="">请选择</option>
                                    <option value="0">0</option>
                                    <option value="系统默认">系统默认</option>
                                    <option value="白金店铺">白金店铺</option>
                                    <option value="钻石店铺">钻石店铺</option>
                                    <option value="黑钻店铺">黑钻店铺</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("店铺时长")</label>
                            <div class="layui-input-inline">
                                <select name="duration" lay-search lay-verify="required" lay-filter="duration">
                                    <option value="">请选择</option>
                                    <option value="浙江" selected>1年</option>
                                    <option value="江西">2年</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md6 layui-col-xs6">
                        <div class="layui-form-item mb">
                            <label class="layui-form-label">@T("店铺分类")</label>
                            <div class="layui-input-inline">
                                <select name="classify" lay-search lay-verify="required" lay-filter="classify">
                                    <option value="">请选择省</option>
                                    <option value="浙江">浙江省</option>
                                    <option value="江西">江西省</option>
                                    <option value="福建">福建省</option>
                                    <option value="天津">天津市</option>
                                    <option value="河北">河北省</option>
                                    <option value="山西">山西省</option>
                                </select>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">@T("店铺分类")</label>
                        <div class="layui-input-block red-text">
                            请根据您所经营的内容认真选择店铺分类，注册后商家不可自行修改。
                        </div>
                    </div>
                </div>
                <div class="layui-form-item mb">
                    <label class="layui-form-label">经营类目</label>
                    <div class="oneness layui-hide">
                        <div class="layui-input-inline">
                            @* selected *@
                            <select name="quiz1" lay-filter="quiz1">
                                <option value="">请选择省</option>
                                <option value="智能数码">智能数码</option>
                                <option value="家电">家电</option>
                                <option value="食品">食品</option>
                            </select>
                        </div>
                        <div class="layui-input-inline layui-hide">
                            <select name="quiz2" lay-filter="quiz2">
                                <option value="">请选择市</option>
                                <option value="电子教育">电子教育</option>
                                <option value="空调">空调</option>
                                <option value="生鲜食品">生鲜食品</option>
                            </select>
                        </div>
                        <div class="layui-input-inline layui-hide">
                            <select name="quiz3" lay-filter="quiz13">
                                <option value="">请选择县/区</option>
                                <option data-id="1" value="学生电脑  (分佣比例: 3%)">学生电脑</option>
                                <option data-id="2" value="挂式空调  (分佣比例: 5%)">挂式空调</option>
                                <option data-id="3" value="鱼类 (分佣比例: 1%)">鱼类</option>
                            </select>
                        </div>
                        <div class="layui-input-inline btn-smBox">
                            <button type="button" class="yes layui-btn layui-btn-primary layui-btn-sm">确认</button>
                            <button type="button" class="no layui-btn layui-btn-primary layui-btn-sm">取消</button>
                        </div>
                    </div>
                    @* layui-show *@
                    <div class="layui-input-inline addBox">
                        <button type="button" class="layui-btn add-btn">
                            <i class="layui-icon layui-icon-addition"></i> 选择添加类目
                        </button>
                    </div>

                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">经营类目</label>
                        <div class="layui-input-inline red-text">
                            请选择最后一级类目
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-form-item">
                        <label class="layui-form-label hide">表格</label>
                        <div class="layui-input-inline tableBox">
                            <table class="category-table">
                                <tr>
                                    <th>一级类目</th>
                                    <th>二级类目</th>
                                    <th>三级类目</th>
                                    <th>操作</th>
                                </tr>
                                <!--<tr>
                                    <td>男装/女装/内衣</td>
                                    <td>袜子配饰</td>
                                    <td>女袜(分佣比例: 1%)</td>
                                    <td class="del">删除</td>
                                </tr>
                                <tr>
                                    <td>厨卫电器/生活家电/厨具</td>
                                    <td>生活家电</td>
                                    <td>吸尘器 (分佣比例: 5%)</td>
                                    <td class="del">删除</td>
                                </tr>-->
                            </table>
                        </div>
                    </div>
                </div>


                <div class="layui-row">
                    <div class="layui-form-item btnBox">
                        <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit
                            lay-filter="demo1">提交申请</button>
                    </div>
                </div>
            </form>
        </div>



    </div>
</div>
<div class="bug"></div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        layui.use(['form', 'laydate', 'util'], function () {
            var form = layui.form;
            var layer = layui.layer;

            var tableData = []; // 用于存储表格数据

            // 渲染表单
            form.render();
            // 提交事件
            form.on('submit(demo1)', function (data) {
                var field = data.field; // 获取表单字段值
                console.log("field", field);
                tableData = getTableData();
                console.log("表格数据:", tableData);
                if (tableData.length === 0) {
                    layer.msg('请添加经营类目');
                    return false;
                }
                field.tableData = tableData;

                layer.msg('提交成功');
                return false; // 阻止默认 form 跳转
            });

            // 检查所有必填项和表格数据
            function checkFormCompletion() {
                var allFieldsValid = true;
                $('.layui-form [lay-verify="required"]').each(function () {
                    if (!$(this).val()) {
                        allFieldsValid = false;
                        return false; // 退出循环
                    }
                });

                var tableDataNotEmpty = $('.category-table tr:gt(0)').length > 0;

                console.log("allFieldsValid:", allFieldsValid);
                console.log("tableDataNotEmpty:", tableDataNotEmpty);
                if (allFieldsValid && tableDataNotEmpty) {
                    $('#submitBtn').removeClass('layui-btn-disabled');
                } else {
                    $('#submitBtn').addClass('layui-btn-disabled');
                }
            }

            // 监听表单输入变化
            $('.layui-form [lay-verify="required"]').on('input change', function () {
                checkFormCompletion();
            });

            // 使用 MutationObserver 监听表格数据变化
            const tableObserver = new MutationObserver(function () {
                checkFormCompletion();
            });

            tableObserver.observe(document.querySelector('.category-table'), {
                childList: true, // 监听子节点的变化
                subtree: false // 不监听后代节点
            });


            // 点击 "添加" 按钮
            $('.addBox').on('click', function () {
                $('.oneness').removeClass('layui-hide').addClass('layui-show');
                $(this).addClass('layui-hide');
            });

            // 点击 "取消" 按钮
            $('.btn-smBox .no').on('click', function () {
                // 隐藏oneness，显示addBox
                $('.oneness').removeClass('layui-show').addClass('layui-hide');
                $('.addBox').removeClass('layui-hide');
                // 重置级联选择
                $('select[name="quiz1"]').val('');
                $('select[name="quiz2"]').val('');
                $('select[name="quiz3"]').val('');
                // 隐藏quiz2和quiz3的容器
                $('select[name="quiz2"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                $('select[name="quiz3"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                // 重新渲染表单
                form.render('select');
            });

            // 监听quiz1的选择
            form.on('select(quiz1)', function (data) {
                var quiz2Container = $('select[name="quiz2"]').closest('.layui-input-inline');
                if (data.value) {
                    quiz2Container.removeClass('layui-hide').addClass('layui-show');
                } else {
                    quiz2Container.removeClass('layui-show').addClass('layui-hide');
                    // 同时隐藏quiz3并重置值
                    $('select[name="quiz3"]').closest('.layui-input-inline').addClass('layui-hide');
                    $('select[name="quiz2"]').val('');
                    $('select[name="quiz3"]').val('');
                    form.render('select');
                }
            });

            // 监听quiz2的选择
            form.on('select(quiz2)', function (data) {
                var quiz3Container = $('select[name="quiz3"]').closest('.layui-input-inline');
                if (data.value) {
                    quiz3Container.removeClass('layui-hide').addClass('layui-show');
                } else {
                    quiz3Container.removeClass('layui-show').addClass('layui-hide');
                    $('select[name="quiz3"]').val('');
                    form.render('select');
                }
            });

            // 删除行
            $('.category-table').on('click', '.del', function () {
                $(this).closest('tr').remove();
                checkFormCompletion(); // 检查表单完成状态
            });


            // 点击 "确认" 按钮
            $('.btn-smBox .yes').on('click', function () {
                var quiz1 = $('select[name="quiz1"]').val();
                var quiz2 = $('select[name="quiz2"]').val();
                var quiz3 = $('select[name="quiz3"]').val();
                var quiz3Id = $('select[name="quiz3"] option:selected').data('id'); // 获取 data-id

                if (quiz1 && quiz2 && quiz3 && quiz3Id) {
                    var newRow = `
                        <tr data-id="${quiz3Id}">
                            <td>${quiz1}</td>
                            <td>${quiz2}</td>
                            <td>${quiz3}</td>
                            <td class="del">删除</td>
                        </tr>
                    `;
                    $('.category-table').append(newRow);

                    // 重置级联选择
                    $('select[name="quiz1"]').val('');
                    $('select[name="quiz2"]').val('');
                    $('select[name="quiz3"]').val('');
                    $('select[name="quiz2"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                    $('select[name="quiz3"]').closest('.layui-input-inline').removeClass('layui-show').addClass('layui-hide');
                    form.render('select');

                    // 隐藏oneness，显示addBox
                    $('.oneness').removeClass('layui-show').addClass('layui-hide');
                    $('.addBox').removeClass('layui-hide');

                    // 检查表单完成状态
                    checkFormCompletion();
                } else {
                    layer.msg('请完整选择类目');
                }
            });


            //获取表格内容
            function getTableData() {
                var tableData = [];
                $('.category-table tr:gt(0)').each(function () {
                    var row = $(this);
                    var rowData = {
                        tId: row.data('id'), // 获取 data-id 属性
                        firstCategory: row.find('td:eq(0)').text(),
                        secondCategory: row.find('td:eq(1)').text(),
                        thirdCategory: row.find('td:eq(2)').text()
                    };
                    tableData.push(rowData);
                });
                return tableData;
            }



        });



    });


</script>