@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@inject IManageProvider _provider
@inject IWebHelper _webHelper
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/orders.css");

    var site = SiteInfo.GetDefaultSeo();
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
    int orderState = ViewBag.OrderState ?? -1;
    DateTime dtStart = Model.buyStartDate;
    DateTime dtEnd = Model.buyEndDate;
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }
    .main {
    margin-left: 15%;
    width: 70%;
    }
    .row-display
    {
    display:none;
    }
</style>
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Order")">@T("订单")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content" data-show="true" style="display: block;">
            <!-- 过滤功能 -->
            <form method="get">
                <div class="filterBox">
                    <div class="flex">
                        <div class="label">@T("关键词"):</div>
                        <div class="layui-form"><input class="layui-input" name="orderkey" value="@Model.key" placeholder="@T("订单号")/BOM"></div>
                    </div>
                    <div class="flex" style="margin-right: 0.2vw;">
                        <div class="label">@T("订单状态"): </div>
                        <div class="layui-form">
                            <select class="select" name="orderState" lay-filter="orderState" id="orderState" title="@T("订单状态")">
                                <option value="-1" selected="@(orderState == -1)">@T("全部类别")</option>
                                <option value="10" selected="@(orderState == 10)">@T("待支付")</option>
                                <option value="20" selected="@(orderState == 20)">@T("待发货")</option>
                                <option value="30" selected="@(orderState == 30)">@T("待收货")</option>
                                <option value="40" selected="@(orderState == 40)">@T("已完成")</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form flex layuiDateBox" style="width: 30%;max-width: 50%;min-width: 450px;">
                        <div class="layui-inline" id="ID-laydate-rangeLinked">
                            <div class="layui-form-mid" style="white-space: nowrap;">@T("购买时间"):</div>
                            <div class="layui-input-inline">
                                <input id="ID-laydate-start-date-1" class="layui-input" placeholder="@T("开始日期")" lay-filter="buyStartDate" name="buyStartDate" value="@(dtStart==DateTime.MinValue?"": dtStart.ToString("yyyy-MM-dd"))">
                            </div>
                            -
                            <div class="layui-input-inline">
                                <input id="ID-laydate-end-date-1" class="layui-input" placeholder="@T("结束日期")" lay-filter="buyEndDate" name="buyEndDate" value="@(dtEnd==DateTime.MinValue?"":dtEnd.ToString("yyyy-MM-dd"))">
                            </div>
                        </div>
                        <script>
                            var laydate = layui.laydate;
                            // 日期范围 - 左右面板联动选择模式
                            laydate.render({
                                elem: '#ID-laydate-rangeLinked',
                                range: ['#ID-laydate-start-date-1', '#ID-laydate-end-date-1'],
                                lang: '@Language?.UniqueSeoCode',
                                rangeLinked: true, // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
                                done: function(value, date, endDate){
                                    // 选择日期后自动提交表单
                                    $('#ID-laydate-rangeLinked').closest('form').submit();
                                }
                            });
                        </script>
                    </div>
                    <div style="width: 80px;margin: auto;">
                        <button class="button button_blue" type="submit">@T("查询")</button>
                    </div>
                </div>
            </form>
            <!-- 订单状态 -->
            <div class="optionsBox2">
                <div class="titleBox1 flex">
                    <div data-select="false" id="allOrder" onclick="titleClick(this,-1)" class="@(orderState == -1 ? "titleSelect":"")">@T("全部订单")
                        <span class="textSelect">
                            (@Model.orderStateNum.All)
                        </span>
                    </div>
                   
                    <div data-select="false" onclick="titleClick(this,10)" class="@(orderState == 10 ? "titleSelect":"")">@T("待支付")
                        <span class="textSelect">
                            (@Model.orderStateNum.State1)
                        </span>
                    </div>
                   
                    <div data-select="false" onclick="titleClick(this,30)" class="@(orderState == 30 ? "titleSelect":"")">@T("配送中")
                        <span class="textSelect">
                            (@Model.orderStateNum.State3)
                        </span>
                    </div>
                   
                    <div data-select="false" onclick="titleClick(this,35)" class="@(orderState == 35 ? "titleSelect":"")">@T("待签收")
                        <span class="textSelect">
                            (@Model.orderStateNum.State4)
                        </span>
                    </div>
                  
                    <div data-select="false" onclick="titleClick(this,40)" class="@(orderState == 40 ? "titleSelect":"")">@T("已完成")
                        <span class="textSelect">
                            (@Model.orderStateNum.State5)
                        </span>
                    </div>
                    
                </div>
            </div>

            <!-- 全选 -->
            <div class="optionsBox" style="z-index: 1 !important;">
                <span class="layui-form">
                    <label for="selectAll">
                        @T("全选")
                    </label>
                    <input type="checkbox" name="selectAll" class="layui-checkbox" id="selectAll" lay-filter="selectAll">
                </span>
                <div> <button class="button hoverBlue" onclick="BatchConfirmReceipt()">@T("批量确认收货")</button> </div>
                <div> <button class="button hoverRed" onclick="BatchDeletion()">@T("批量删除订单")</button> </div>
            </div>
            <!-- 所有订单-表格 -->

            <div class="tablesBox">
                @foreach (OrderDto item in Model.list)
                {
                    <table class="layui-table" style="background-color: white;">
                        <colgroup>
                            <col width="50%">
                            <col width="15%">
                            <col width="15%">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th colspan="2">
                                    <span class="layui-form" style="margin-right: .5vw;">
                                        <input type="checkbox" name="order" value="@item.Id">
                                    </span>
                                    <span style="margin-right: 5%">
                                        @(item.AddTime?.ToString("yyyy-MM-dd HH:mm:ss"))
                                    </span>
                                    <span id="orderId">@T("订单编号"): @item.OrderSn</span>
                                    <span class="textSelect pointer" id="copy" onclick="copy('#orderId')">
                                        @T("复制")
                                    </span>
                                </th>
                                <th class="tableIcon" style="margin-right: auto;">
                                    @* <i class="iconfont  icon-zaixiankefu1" onclick="toRouter(this)" data-link=""></i> *@
                                </th>
                                <th class="tableIcon" style="text-align: center;">
                                    <span style="text-align: right;" class="textSelect pointer"onclick="addBom()">@T("添加BOM标识") </span>
                                    <i class="iconfont  icon-icdelete" style="font-size: 1.2vw;" onclick="openDialog3('@T("警告")','@T("您确定要删除该订单？")',@item.Id)"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="@item.Id">
                            @{
                                var i = 0;
                                foreach (OrderGoodsDto goods in item.OrderGoods??new List<OrderGoodsDto>())
                                {
                                    <tr class="@(i > 1 ? "row-display" : "")">
                                        <td>
                                            <div class="goodsInfo" style="place-items: center;">
                                                <div class="layui-form" style="margin-right: .5vw;">
                                                    @*<input type="checkbox" name="BBB" checked>*@
                                                </div>
                                                <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = goods.SkuId })">
                                                    <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                        <img src="@goods.GoodsImage" alt="商品图片"
                                                         class="goodsImg" >
                                                    </div>
                                                </a>
                                                <div class="goodsInformation"
                                                style="width: 60%;margin-left: 5%;margin-right: auto;">
                                                    <div>
                                                        <span class="tdTitle textOver2">@goods.GoodsName</span>
                                                    </div>
                                                    <div>
                                                        <p>@goods.SpecValue</p>
                                                    </div>
                                                    @*<div>@T("原厂编号")<span class="name textOver">ACDC电源模块</span></div>*@
                                                    <div>@T("制造商"): <span class="name textOver">@goods.CompanyName</span> </div>
                                                    @*<div class="textOver">
                                                        @T("制造商编号"): <span class="name textOver">
                                                            727-S40FC008C3B1V000
                                                        </span>
                                                    </div>*@
                                                    <div class="textOver">
                                                        @T("型号"):<span class="name textOver"></span>
                                                    </div>
                                                    @*<div class="textOver">
                                                        @T("客户编号"):<span class="name">220V转5V3.3V9V12V15V24V</span>
                                                    </div>*@
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>@T("商品数量")： *@goods.GoodsNum </div>
                                            <div>@T("商品单价")：@<EMAIL>("F2") </div>

                                        </td>
                                        @if(i == 0)
                                        {
                                            <td rowspan="@item.OrderGoods?.Count" colspan="1" class="fnTd">
                                                @if (item.RefundState==0)
                                                {
                                                    @if (item.OrderState == 10)
                                                    {
                                                        <div class="redmi" data-step="2">@T("待支付")</div>
                                                    }
                                                    else if (item.OrderState == 30)
                                                    {
                                                        <div class="hover" data-step="1,3,4">@T("卖家已发货")</div>
                                                        // <div class="redmi" data-step="1,3,4" onclick="toRouter(this)" data-link="../orders/express.html">@T("查看物流")</div>
                                                    }
                                                    else if (item.OrderState == 20)
                                                    {
                                                        <div class="hover" data-step="1,3,4">@T("等待卖家发货")</div>
                                                    }
                                                    else if (item.OrderState == 0)
                                                    {
                                                        <div class="hover red" data-step="1,3,4">@T("交易关闭")</div>
                                                    }
                                                    else if (item.OrderState == 35)
                                                    {
                                                        <div class="hover" data-step="1,3,4">@T("待签收")</div>
                                                    }
                                                    else if (item.OrderState == 40)
                                                    {
                                                        <div class="hover blue" data-step="1,3,4">@T("交易完成")</div>
                                                    }
                                                    if(item.OrderState != 0)
                                                    {
                                                        <a class="hover" data-step="1,2,3,4" href="@Url.Action("OrderDetails")?Id=@item.Id">
                                                            @T("订单详情")
                                                        </a>
                                                    }
                                                }
                                                else
                                                {
                                                    var refund = RefundReturn.FindByOrderId(item.Id);
                                                    if (refund?.RefundreturnSellerState==4)
                                                    {
                                                        <div class="hover red" data-step="1,3,4">@T("已退款")</div>
                                                    }
                                                    else
                                                    {
                                                        <div class="hover red" data-step="1,3,4">@T("申请退款中")</div>
                                                    }

                                                    <a class="hover" data-step="1,2,3,4" href="@Url.Action("Detail","Refund")?Id=@refund?.Id">@T("退款详情")</a>

                                                }
                                            </td>
                                            <td class="tableBtnBox" rowspan="@item.OrderGoods?.Count" colspan="1">

                                                <div data-step="2" style="margin-top: 10px;color: black;font-size: 15px;cursor: pointer;">@T("修改订单")</div>
                                                <div>@T("运费"): @<EMAIL>?.ToString("F2")</div>
                                                <div data-step="1，2，3，4">@T("合计")：<span class="money">@<EMAIL>?.ToString("F2") </span> </div>
                                                <div class="flex">
                                                    @*<button class="button" data-step="1,3,4">@T("加入购物车")</button>*@
                                                </div>
                                                <div class="flex">
                                                    @*<button class="button bgSelect" data-step="1,3,4">@T("再次购买")</button>
                                                    <button class="button bgSelect" data-step="2">@T("立即支付")</button>*@
                                                </div>
                                                @if (item.OrderState == 10)
                                                {
                                                    <div class="flex">
                                                        <button class="button button_red" data-step="2" onclick="openDialog2('@T("警告")','@T("您确定要取消该订单？")','@item.Id')">@T("取消订单")</button>
                                                    </div>
                                                    <div class="flex">
                                                        <button class="button bgSelect" data-step="2" onclick="toRouter(this)" data-link="@Url.Action("Payment")?Id=@item.Id">@T("立即支付")</button>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="flex">
                                                        <button class="button bgSelect" data-step="1,3,4" onclick="goodsDetailView('@goods.SkuId')">@T("再次购买")</button>
                                                    </div>
                                                    if(item.OrderState >= 15)
                                                    {
                                                        if (item.RefundState!=2)
                                                        {
                                                            <div class="flex">
                                                                <button class="button bgSelect" data-step="1,3,4" onclick="ApplyRefund('@item.Id')">@T("申请退款")</button>
                                                            </div>
                                                        }
                                                        if (item.OrderState >= 30 && item.OrderState != 40 && item.RefundState==0)
                                                        {
                                                            <div class="flex">
                                                                <button class="button bgSelect" onclick="ConfirmReceipt('@item.Id')">@T("确认收货")</button>
                                                            </div>
                                                        }
                                                    }
                                                }
                                            </td>
                                        }
                                    </tr>
                                    i++;
                                }
                                if(item.OrderGoods?.Count > 2)
                                {
                                    <tr id="open">
                                        <td class="hover" style="text-align: center;" colspan="4" onclick="rowShow('@item.Id',true)">
                                            --- @T("展开所有商品") (@item.OrderGoods.Count) ---
                                        </td>
                                    </tr>
                                    <tr class="row-display" id="close">
                                        <td class="hover" style="text-align: center;" colspan="4" onclick="rowShow('@item.Id',false)">
                                            --- @T("收起") ---
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                }
                <div id="pagingBox" style="text-align: right;"></div>
                <script>
                    function select(dom) {
                        const type = dom.getAttribute("data-type");
                        const parentDom = dom.parentNode;
                        $(parentDom).children().attr('class', '')
                        // console.log(parentDom,$(parentDom));
                        if (type == 0) {
                            dom.className = "bgSelect";
                        } else if (type == 1) {
                            dom.className = "bgSelect";
                        } else if (type == 2) {
                            dom.className = "bgSelect";
                        } else if (type == 3) {
                            dom.className = "bgSelect";
                        }
                    }
                    layui.use(function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'pagingBox',
                            count: @Model.total, // 数据总数
                            limit: @(Model.limit > 0 ? Model.limit : 10), // 每页显示条数
                            limits: [5,10, 20, 50, 100], // 每页条数的选择项
                            curr: @(Model.page > 0 ? Model.page : 1), // 当前页码
                            groups: 5, // 连续显示页码个数
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                            theme: '#2C79E8', // 自定义主题色
                            prev: '@T("上一页")',
                            next: '@T("下一页")',
                            first: '@T("首页")',
                            last: '@T("尾页")',
                            countText: ['@T("共") ',' @T("条")'],
                            skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                            limitTemplet: function(item) {
                              return item + ' @T("条/页")';
                            },
                            jump: function(obj, first) {
                                // 首次不执行（首次加载时不跳转）
                                if (!first) {
                                    // 创建一个隐藏的表单来提交所有参数
                                    var $form = $('<form></form>');
                                    $form.attr('action', '@Url.Action("Index")');
                                    $form.attr('method', 'get');
                                    $form.css('display', 'none');

                                    // 添加页码参数
                                    $form.append('<input type="hidden" name="page" value="' + obj.curr + '" />');

                                    // 添加每页条数参数
                                    $form.append('<input type="hidden" name="limit" value="' + obj.limit + '" />');

                                    // 获取当前表单中的所有参数
                                    $('form:first input, form:first select').each(function() {
                                        var name = $(this).attr('name');
                                        var value = $(this).val();

                                        // 如果存在名称和值，并且不是页码相关参数，则添加到隐藏表单中
                                        if (name && value && name !== 'page' && name !== 'limit') {
                                            $form.append('<input type="hidden" name="' + name + '" value="' + value + '" />');
                                        }
                                    });

                                    // 将表单添加到文档中并提交
                                    $('body').append($form);
                                    $form.submit();
                                }
                            }
                        });
                    });
                </script>

            </div>
        </div>
        <!-- /* 无数据 */ -->
        <div class="noData" data-show="false" style="display: none;">
            <img src="~/public/images/pics/konggouwuche.png" alt="" style="width: 30%;margin-top: 5%;">
            <div class="noDataText">@T("您的购物车为空，去首页逛逛吧")！</div>
            <button class="button button_blue noDataBtn"><i class="iconfont icon-home" style="font-size: 1.2vw;"></i>
                @T("去首页")
            </button>
        </div>
    </div>
    <!-- 产品推荐 -国内才有 外国注重隐私 -->
    <div class="productComment">
        <div class="title">
            @T("相似推荐")
        </div>
        <div class="mainBox2_container">
            @foreach (GoodsDto item in ViewBag.Randomlist)
            {
                <div class="mainBox2_content">
                    <div>
                        <a href="@Url.Action("Index", "Goods", new { Area = "", skuId = item.SkuId })">
                            <img src="@item.GoodsImage" alt="@T("商品图片")">
                        </a>
                    </div>
                    <div class="mainBox2_content_desc">@item.Name</div>
                    <div class="gray">
                        @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                    </div>
                    <div class="mainBox2_content_price">@<EMAIL></div>
                    <!-- 加入购物车 -->
                    @if (item.GoodsStorage <= 0)
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','@T("库存不足")',false)"></div>
                    }
                    else
                    {
                        <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.SkuId','','',true)"></div>
                    }
                </div>
            }
        </div>
    </div>
</div>
<script src="/public/script/lazyload.js"></script>
<script>
    window.addEventListener('load', function() {
    window.lazyLoadImages();
});
        function addBom(orderId = "S02412069039") {
      var title = orderId + ' --BOM标识'
      layui.layer.open({
        type: 1,
        // area: size,
        skin: 'dialog_addBom',
        resize:true,
        shadeClose: true,
        title,
        btn: ['@T("保存")'],
        btn1: function (index, layero) {
            layer.close(index)
            console.log(index,layero);
            return false;
        },
        content: `
        <style>
        .bomBox{
          min-width: 400px;
          padding: 1vw 1vw .5vw 1vw;
      }
      .bomBox>div{
          margin-bottom: .5vw;
          font-size: .75vw;
          letter-spacing: 1px;
      }
      /* bomBox 自定义按钮 */
      .dialog_addBom>.layui-layer-btn{
          width: 100%;
          padding: 0px;
          margin: 0px;
          box-sizing: border-box;
          /* border: 2px solid ; */
          margin-bottom: 1vw;
      }
      .dialog_addBom>.layui-layer-btn>a{
          width: 60%;
          margin-right: 20%;
          min-height: 32px;
          text-align: center;
          background-color: var(--blue-deep);
      }
      </style>
        <div class="bomBox">
            <div class="bomTip">@T("您对此订单添加备注，是方便您自己辨识和日后查找")</div>
            <div class="bomTip" style="color:crimson;">@T("注意"):@T("此备注不是给我商城工作人员的备注")，@T("千万别误解").</div>
            <div class="textarea" data-currentCount="0" data-maxLength="/20"
                style="position: relative;">
                <textarea name="" maxlength="20" rows="2" oninput="textareaOninput(this)"
                    placeholder="@T("请输入BOM标识备注")：@T("20个字符以内")" class="layui-textarea"
                    style="padding: .3vw .3vw 1.5vw .5vw;resize: none;font-size: .8vw;letter-spacing: 1px;"></textarea>
            </div>

        </div>
        `
      })
      //  <button class="button button_blue" style="width:60%;margin-left:20%;margin-top:.5vw;" onclick="bomConfirm(${1})">@T("保存")</button>
    }

    function BatchConfirmReceipt()
    {
         var selectedValues = [];
            $('input[name="order"]:checked').each(function () {
                selectedValues.push($(this).val());
            });
            var ids = selectedValues.join(",");
                $.post('@Url.Action("BatchConfirmReceipt")',{Ids:ids},function(res)
                {
                    layui.layer.msg(res.msg);
                    if(res.success)
                    {
                        location.reload();
                     }
                 })
    }

    function BatchDeletion()
    {
         var selectedValues = [];
            $('input[name="order"]:checked').each(function () {
                selectedValues.push($(this).val());
            });
            var ids = selectedValues.join(",");
                $.post('@Url.Action("Delete")',{Ids:ids},function(res)
                {
                    layui.layer.msg(res.msg);
                    if(res.success)
                    {
                        location.reload();
                     }
                 })
    }

    function rowShow(id,isshow)
    {
        if(isshow)
        {
            var rows = document.getElementById(id).querySelectorAll("tr");
                rows.forEach(function(row) {
                   row.classList.remove("row-display");
                });

            rows[rows.length-2].classList.add("row-display");
            rows[rows.length-1].classList.remove("row-display");
        }
        else
        {
            var rows = document.getElementById(id).querySelectorAll("tr");
            var i = 0;
                 rows.forEach(function(row) {
                     if(i > 1)
                     {
                         row.classList.add("row-display");
                     }
                   i++;
                });
            rows[rows.length-1].classList.add("row-display");
            rows[rows.length-2].classList.remove("row-display");
        }
    }

    function titleClick(dom, index) {
        // $(dom).siblings().removeClass('titleSelect');
        // $(dom).addClass('titleSelect');
        window.location.href = '@Url.Action("Index")?orderState=' + index;
         // 03 当前步骤对应的内容
        // const stepList = $('[data-step]');
        // stepList.each(function (_index, item) {
        //     if (item.getAttribute('data-step').includes(index)) {
        //         console.log(item,index);
        //         item.style.display = 'block';
        //     }else{
        //         item.style.display = 'none';
        //     }
        // })
    }
        function openDialog2(title = '@T("温馨提示")',content = '@T("您确定要执行此操作吗")?',id) {
        layui.layer.open({
            title: title,
            content: content,
            area: '20vw',
            btn: ['@T("确认")', '@T("取消")'],
            yes: function(index, layero)
            {
                layer.close(index);
                $.post('@Url.Action("CancelOrder")',{Id:id},function(res)
                {
                    layui.layer.msg(res.msg);
                    if(res.success)
                    {
                        location.reload();
                     }
                 })
            },
            btn2: function(index, layero){
                layer.close(index);
            },
        })
    }
        function openDialog3(title = '@T("温馨提示")',content = '@T("您确定要执行此操作吗")?',id) {
        layui.layer.open({
            title: title,
            content: content,
            area: '20vw',
            btn: ['@T("确认")', '@T("取消")'],
            yes: function(index, layero)
            {
                layer.close(index);
                $.post('@Url.Action("Delete")',{Ids:id},function(res)
                {
                    layui.layer.msg(res.msg);
                    if(res.success)
                    {
                        location.reload();
                     }
                 })
            },
            btn2: function(index, layero){
                layer.close(index);
            },
        })
    }
    //titleClick($('#allOrder')[0],-1)
</script>
<script asp-location="Footer">

    function goodsDetailView(skuId){
        window.location.href = '/Goods/'+skuId+'.html';
    }

    function ApplyRefund(OrderId)
    {
        layui.layer.open(
        {
            type: 1,
            title: '@T("申请退款")',
            area: ['500px', 'auto'],
            btn: ['@T("确定")'],
            content: `
            <style>
                .refund-form {
                    padding: 20px;
                }
                .refund-form .form-item {
                    margin-bottom: 20px;
                }
                .refund-form .form-label {
                    display: block;
                    margin-bottom: 8px;
                    color: #333;
                    font-size: 14px;
                }
                .refund-form select {
                    width: 100%;
                    height: 38px;
                    line-height: 38px;
                    padding: 0 10px;
                    border: 1px solid #e6e6e6;
                    border-radius: 2px;
                    color: #666;
                }
                .refund-form .layui-textarea {
                    min-height: 100px;
                    resize: none;
                }
                .refund-form .layui-upload-list {
                    margin: 10px 0;
                }
                .refund-form #uploadText {
                    color: red;
                    font-size: 12px;
                }
            </style>
            <div class="refund-form">
                <div class="form-item">
                    <label class="form-label">@T("退款方式")：</label>
                    <select id="Way" class="layui-select">
                        <option value="1">@T("退货退款")</option>
                        <option value="2">@T("退款(无需退货)")</option>
                        @* <option value="3">@T("退货")</option> *@
                    </select>
                </div>
                <div class="form-item">
                    <label class="form-label">@T("收货状态")：</label>
                    <select id="State" class="layui-select">
                        <option value="1">@T("已收到货")</option>
                        <option value="2">@T("未收到货")</option>
                    </select>
                </div>
                <div class="form-item">
                    <label class="form-label">@T("原因")：</label>
                    <textarea name="desc" placeholder="@T("请输入退款原因")" class="layui-textarea" id="Reason"></textarea>
                </div>
                <div class="form-item">
                    <label class="form-label">@T("上传图片")：</label>
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-primary" id="uploadBtn">
                            <i class="layui-icon">&#xe67c;</i>@T("选择图片")
                        </button>
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="uploadPreview" style="max-width: 200px; max-height: 200px;">
                            <p id="uploadText"></p>
                        </div>
                        <input type="hidden" id="ImageUrl" name="ImageUrl">
                    </div>
                </div>
            </div>`,
            success: function(layero, index) {
                // 初始化上传组件
                var upload = layui.upload;

                // 执行实例
                var uploadInst = upload.render({
                    elem: '#uploadBtn', // 绑定元素
                    url: '@Url.Action("UploadImg", "Refund")', // 上传接口
                    accept: 'images', // 只允许上传图片
                    acceptMime: 'image/*', // 只筛选图片文件
                    size: 2048, // 限制文件大小，单位 KB
                    before: function(obj) {
                        // 预读本地文件示例，不支持ie8
                        obj.preview(function(index, file, result) {
                            $('#uploadPreview').attr('src', result); // 图片链接（base64）
                        });

                        $('#uploadText').html('@T("上传中...")');
                    },
                    done: function(res) {
                        // 上传完毕回调
                        if(res.file_id > 0) { // 上传成功
                            $('#uploadText').html('@T("上传成功")');
                            $('#ImageUrl').val(res.file_path); // 保存图片URL到隐藏字段
                        } else {
                            $('#uploadText').html('@T("上传失败"): ' + res.msg);
                            $('#uploadPreview').attr('src', '');      // 清除图片预览
                            $('#ImageUrl').val('');
                            return layui.layer.msg('@T("上传失败")');
                        }
                    },
                    error: function() {
                        // 演示失败状态，并实现重传
                        var uploadText = $('#uploadText');
                        uploadText.html('<span style="color: #FF5722;">@T("上传失败")</span> <a class="layui-btn layui-btn-xs reload-btn">@T("重试")</a>');
                        uploadText.find('.reload-btn').on('click', function() {
                            uploadInst.upload();
                        });
                    }
                });
            },
            yes:function(index,layero)
            {
                var Way = $("#Way").val();
                var ImageUrl = $("#ImageUrl").val();
                var State = $("#State").val();
                var Reason = $("#Reason").val();

                if(!Reason)
                {
                    return layui.layer.msg('@T("请输入退款原因")');
                }

                $.post("@Url.Action("Add", "Refund")",{
                    OrderId: OrderId,
                    Way: Way,
                    State: State,
                    Reason: Reason,
                    ImageUrl: ImageUrl
                }, function(res) {
                    if(res.success) {
                        layui.layer.msg('@T("申请成功")');
                        layui.layer.close(index);
                        setTimeout(function() {
                            location.reload();
                        }, 500);
                    } else {
                        layui.layer.msg(res.msg);
                    }
                });
            }
        })
    }

    layui.use(['form','upload'],function()
    {
        var form = layui.form;
        form.on('select(orderState)', function(data){
            $(data.elem).closest('form').submit();
        });
        layui.form.on("checkbox(selectAll)",function(data)
        {
            var isChecked = this.checked;
            $('input[name="order"]').prop('checked', isChecked);
            layui.form.render('checkbox');
        });
    });
    function ConfirmReceipt(id) {
        layer.confirm('@T("确认要收货吗？")', {
            btn: ['@T("确定")', '@T("取消")'],
            title: '@T("确认提示")'
        }, function() {
            $.ajax({
                url: '/Member/Orders/ConfirmReceipt',
                type: 'POST',
                data: { orderId: id },
                success: function(result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1}, function() {
                            // 刷新页面显示最新状态
                            location.reload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('@T("网络错误，请稍后重试")', {icon: 2});
                }
            });
        });
    }
</script>