﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Dto;
using B2B2CShop.Events.EventModel;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Pek;
using Pek.Events;
using Pek.Helpers;
using Pek.Infrastructure;

using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class GoodsClass : DHEntityBase<GoodsClass>
{
    #region 对象操作
    static GoodsClass()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(GoodTypeId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 保留2位小数
        //CommisRate = Math.Round(CommisRate, 2);

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    /// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    protected override void InitData()
    {
        // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
        if (Meta.Session.Count > 0) return;

        if (XTrace.Debug) XTrace.WriteLine("开始初始化GoodsClass[商品分类表]数据……");

        var _eventPublisher = ObjectContainer.Provider.GetPekService<IEventPublisher>();
        _eventPublisher?.Publish(new InitGoodsClassEvent());

        if (XTrace.Debug) XTrace.WriteLine("完成初始化GoodsClass[商品分类表]数据！");
    }

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>所属类型编号</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public GoodType? GoodType => Extends.Get(nameof(GoodType), k => GoodType.FindById(GoodTypeId));

    /// <summary>所属类型编号</summary>
    [Map(nameof(GoodTypeId), typeof(GoodType), "Id")]
    public String? GoodTypeName => GoodType?.Name;

    /// <summary>所属类型层级</summary>
    public Int32 Level => ParentId == 0 ? 0 : FindById(ParentId)?.Level + 1 ?? 0;

    /// <summary>从顶级到当前分类的所有ID</summary>
    public String ParentIdList => ParentId == 0 ? Id.SafeString() : $"{FindById((Int64)ParentId)?.ParentIdList},{Id}";


    public String ParentNames
    {
        get
        {
            var classids = ParentIdList.Split(",").ToList();
            string classStr = "";
            foreach (var item in classids)
            {
                classStr += GoodsClass.FindById(Int64.Parse(item))?.Name + ">";
            }
            classStr = classStr.Remove(classStr.Length - 1);
            return classStr;
        }
    }

    public String? plist
    {
        get
        {
            if (ParentId == 0) return Id.SafeString();

            var parent = FindById((Int64)ParentId);
            if (parent == null) return Id.SafeString();

            var grandParent = parent.ParentId > 0 ? FindById((Int64)parent.ParentId) : null;

            if (grandParent != null)
                return $"{grandParent.Id},{parent.Id},{Id}";
            else
                return $"{parent.Id},{Id}";
        }
    }
    /// <summary>
    ///是否存在子集
    /// </summary>
    [XmlIgnore, ScriptIgnore, IgnoreDataMember]
    public bool subset { get; set; } = false;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="parentId">商品分类上级ID</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsClass> Search(Int64 parentId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (parentId >= 0) exp &= _.ParentId == parentId;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据列表Ids获取列表
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public static IList<GoodsClass> FindByIds(String ids)
    {
        if (ids.IsNullOrWhiteSpace()) return [];

        ids = ids.Trim(',');

        if (Meta.Session.Count < 1000)
        {
            return Meta.Cache.FindAll(x => ids.Split(",").Contains(x.Id.ToString()));
        }

        return FindAll(_.Id.In(ids.Split(',')));
    }

    /// <summary>根据当前层级查找</summary>
    /// <param name="level">当前层级</param>
    /// <returns>实体列表</returns>
    public static IEnumerable<GoodsClass> FindAllByLevel(Int32 level)
    {
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.Level == level).OrderBy(x => x.Sort);

        return FindAll().Where(e => e.Level == level).OrderBy(x => x.Sort);
    }

    /// <summary>
    /// 查询商品分类树以及商品
    /// </summary>
    /// <param name="LId"></param>
    public static List<GoodsClassDto1> FindAllByTreeLan(Int32 LId)
    {
        var list = FindAllByLevel(0).Where(e => e.Enable == true).Select(x =>
        {
            var classChild = FindAllByParentId(x.Id).Where(e => e.Enable == true).OrderBy(e => e.Sort).Select(e => new GoodsClassDto1
            {
                Id = e.Id.SafeString(),
                Name = (GoodsClassLan.FindByGIdAndLId(e.Id, LId)?.Name).IsNullOrWhiteSpace() ? e.Name : GoodsClassLan.FindByGIdAndLId(e.Id, LId)?.Name,
                Sum = MerchantMaterial.GetQuantityByClassId(0, e.Id),
                Goods = Goods.FindAllByCid2(e.Id).Where(e => e.GoodsState == 1).Select(e => new GoodsDto
                {
                    Id = e.Id.SafeString(),
                    SkuId = (GoodsSKUDetail.FindByGoodsIdAndMaterialId(e.Id,e.MerchantMaterial)?.Id??0).SafeString(),
                    Name = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanName ?? e.Name,
                    GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover,
                    GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialIds),
                }).ToList(),
            }).ToList();

            var classChildIds = classChild.Select(e => e.Id).ToList();

            return new GoodsClassDto1
            {
                Id = x.Id.ToString(),
                Name = (GoodsClassLan.FindByGIdAndLId(x.Id, LId)?.Name).IsNullOrWhiteSpace() ? x.Name : GoodsClassLan.FindByGIdAndLId(x.Id, LId)?.Name,
                Child = classChild,
                Goods = Goods.FindAllByCid1(x.Id).Where(e => classChildIds.Contains(e.Cid2.SafeString()) && e.GoodsState == 1).Select(e => new GoodsDto
                {
                    Id = e.Id.SafeString(),
                    Name = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanName ?? e.Name,
                    GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover,
                    GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialIds),
                }).ToList(),
            };
        }).ToList();

        return list;
    }

    // Select Count(Id) as Id,Category From DH_GoodsClass Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<GoodsClass> _CategoryCache = new FieldCache<GoodsClass>(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IGoodsClass ToModel()
    {
        var model = new GoodsClass();
        model.Copy(this);

        return model;
    }

    #endregion
}
