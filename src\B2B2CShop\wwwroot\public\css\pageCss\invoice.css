
.breadBox {
    border-bottom: none;
}

.main {
    margin-left: 15%;
    width: 70%;
}

.userInfoBox {
    margin-bottom: 1vw;
    min-height: 70vh;
    display: flex;
    /* border: 1px solid ; */
}

aside {
    padding: 10px 0px;
    width: 15%;
    min-width: 200px;
    height: fit-content;
    border: 1px solid #E2E3E9;
    /* background-color: red; */
}

aside>div:nth-child(1) {
    font-size: 20px;
    padding-top: 10px;
    text-indent: 15%;
    font-weight: 550;
}

aside>a:not(:first-child) {
    margin-bottom: .5vw;
    padding: 7px 0vw;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
    width: 100%;
    cursor: pointer;
}
aside>a>div:nth-child(1) {
    margin-right: .5vw;
    margin-left: 15%;
    font-size: 20px;
}
._line{
    width: 80% !important;
    height: 0px !important;
    border: none;
    border-bottom: 1px solid var(--line);
    margin: 0px 0px .7vw 10% !important;
}
aside>a:not(.bgSelect):hover{
    color:  var(--blue-deep);
}

.content {
    width: 80%;
    display: block;
    padding: 0vw 1vw 1vw 1vw;
}


.filterBox {
    position: sticky;
    top: 0;
    background: white !important;
    display: flex;
    place-items: center;
    /* border: 1px solid ;  */
    z-index: 2;
}
/* .button{
    height: 2.1vw;
} */
.optionsBox {
    margin-top: 1vw;
    width: 100%;
    padding-top: .5vw;
    position: sticky;
    top: 4vh;
    background-color: white;
    /* border: 2px solid ; */
    z-index: 2;
    display: flex;
    place-items: center;
}
.titleBox1{
    flex: 1;
    display: flex;
    padding-bottom: .5vw;
    position: relative;
}
.titleBox1>div {
    margin-left: 4%;
    cursor: pointer;
    padding-bottom: .2vw;

}
.titleBox2 {
    position: absolute;
    right: 0%;
    background-color: white;
    display: flex;
    place-items: center;
    z-index: 2;
}

.titleBox2>div {
    margin-left: 1vw;
}

.titleBox2>div>.button {
    background-color: white;
}


.tablesBox {
    /* padding: 1vw 0px; */
    width: 100%;
    /* height: 100vh; */
    /* border: 1px solid ; */
    overflow: auto;
    box-sizing: border-box;
}

.layui-table:not(:first-child) {
    margin-top: 2vw;
}

.tableIcon {
    font-size: 2vw;
}

th {
    border: none !important;
    font-weight: 500 !important;
    font-size: .8vw !important;
}

.goodsInfo {
    padding-top: 0;
    min-width: 15vw;
    display: flex;
    /* border: 1px solid red; */
}

.goodsInfo>div:nth-child(2) {
    width: 100%;
    object-fit: fill;
    text-align: center;
}

.goodsInfo>div:nth-child(3)>div {
    color: var(--text-color2);
}

.goodsInformation>div {
    margin-top: .2vw;
    display: flex;
}

.name {
    margin-left: auto;
    width: 70%;
    display: block;
    color: var(--text-color);
    /* border: 1px solid red; */
}

.tdTitle {
    color: var(--text-color) !important;
    font-size: 0.9vw;
    margin-bottom: 0.4vw;
}

td {
    vertical-align: top;
}

td>div:last-child {
    padding-bottom: .5vw;
}

.tableBtnBox {
    text-align: center;
}

.tableBtnBox>div:not(:first-child) {
    margin-top: .5vw;
}

.tableBtnBox>div>button {
    width: 80%;
    border-radius: 45px;
}

.fnTd>div:not(:first-child) {
    margin-top: .5vw;
}
/* td>div>.button{
    height: 1.5vw;
    line-height: .8vw;
} */

