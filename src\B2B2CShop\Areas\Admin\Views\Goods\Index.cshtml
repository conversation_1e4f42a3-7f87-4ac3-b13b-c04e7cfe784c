﻿@using B2B2CShop.Dto
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    var goodsClassList1 = GoodsClass.FindAllByParentId(0);
    var goodsClassList2 = GoodsClass.FindAllByParentId(Model.cId1 <= 0 ? -1 : Model.cId1);

}
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("商品管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="@Url.Action("Index")" class="current"><span>@T("管理")</span></a></li>
            </ul>
        </div>
    </div>
    <div class="fixed-empty"></div>

    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom">
            <h4 title="@T("提示相关设置操作时应注意的要点")">@T("操作提示")</h4>
            <span id="explanationZoom" title="收起提示" class="arrow"></span>
        </div>
        <ul>
            <li>@T("当商品处于非上架状态时，前台将不能浏览该商品，店主可控制商品上架状态")</li>
            <li>@T("当商品处于违规下架状态时，前台将不能购买该商品，只有管理员可以控制商品是否违规下架，并且商品只有重新编辑后才能上架")</li>
        </ul>
    </div>
    <form method="get" name="formSearch" id="formSearch" >
        <div class="ds-search-form">
            <dl>
                <dt>@T("商品名称")</dt>
                <dd><input type="text" value="@Model.goodsName" name="goodsName" id="goodsName" class="txt" ></dd>
            </dl>
            <dl>
                <dt>@T("平台货号")</dt>
                <dd><input type="text" value="@Model.goodsId" name="goodsId" id="goodsId" class="txt" /></dd>
            </dl>
            <dl>
                <dt>@T("分类")</dt>
                <dd id="searchgc_td">
                    <select name="cId1" id="cId1" class="w200">
                        <option value="">@T("请选择")</option>
                        @foreach (var item in goodsClassList1)
                        {
                            if (item.Id == Model.cId1)
                            {
                                <option value="@item.Id" selected>@item.Name</option>
                            }
                            else
                            {
                                <option value="@item.Id">@item.Name</option>
                            }
                        }
                    </select>
                    <select name="cId2" id="cId2" class="w200">
                        <option value="">@T("请选择")</option>
                        @foreach (var item in goodsClassList2)
                        {
                            if (item.Id == Model.cId2)
                            {
                                <option value="@item.Id" selected>@item.Name</option>
                            }
                            else
                            {
                                <option value="@item.Id">@item.Name</option>
                            }
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("所属店铺")</dt>
                <dd>
                    <select name="storeId" class="w200">
                        <option value="">@T("请选择")</option>
                        @foreach (Store item in Model.storeList)
                        {
                            if (Model.storeId == item.Id)
                            {
                                <option value="@item.Id" selected>@item.Name</option>
                            }
                            else
                            {
                                <option value="@item.Id">@item.Name</option>
                            }
                        }
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("上架状态")</dt>
                <dd>
                    <select name="goodsstate" class="w200">
                        <!option value="-1" @(Model.goodsstate == -1?"selected":"")>@T("请选择...")</!option>
                        <!option value="1" @(Model.goodsstate == 1 ? "selected" : "")>@T("出售中")</!option>
                        <!option value="0" @(Model.goodsstate == 0 ? "selected" : "")>@T("仓库中")</!option>
                        <!option value="10" @(Model.goodsstate == 10 ? "selected" : "")>@T("违规下架")</!option>
                    </select>
                </dd>
            </dl>
            <dl>
                <dt>@T("审核状态")</dt>
                <dd>
                    <select name="goodsverify" class="w200">
                        <!option value="-1" @(Model.goodsverify == -1 ? "selected" : "")>@T("请选择...")</!option>
                        <!option value="1" @(Model.goodsverify == 1 ? "selected" : "")>@T("通过")</!option>
                        <!option value="0" @(Model.goodsverify == 0 ? "selected" : "")>@T("未通过")</!option>
                        <!option value="10" @(Model.goodsverify == 10 ? "selected" : "")>@T("等待审核")</!option>
                    </select>
                </dd>
            </dl>

            <div class="btn_group">
                <a href="javascript:document.formSearch.submit();" id="dssubmit" class="btn " title="@T("查询")">@T("查询")</a>
            </div>
        </div>
    </form>


    <table class="ds-default-table">
        <thead>
            <tr class="thead">
                <th class="w24"></th>
                <th class="w60 align-center">@T("平台货号")</th>
                <th colspan="2">@T("商品名称")</th>
                <th>@T("品牌&分类")</th>
                <th class="w72 align-center">@T("价格")</th>
                <th class="w72 align-center">@T("库存")</th>
                <th class="w150 align-center">@T("商品状态")</th>
                <th class="w150 align-center">@T("审核状态")</th>
                <th class="w72 align-center">@T("平台推荐")</th>
                <th class="w200 align-center">@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (ManageGoodsDto item in Model.list)
            {
                <tr class="hover edit" id="<EMAIL>">
                    <td><input type="checkbox" value="@item.GoodsId" class="checkitem"></td>
                    <td class="align-center">@item.GoodsId</td>
                    <td class="w60 picture"><div class="size-56x56"><span class="thumb size-56x56"><i></i><img src="@item.GoodsImage" width="56" height="56" /></span></div></td>
                    <td>
                        <dl class="goods-info">
                            <dt class="goods-name">@item.GoodsName</dt>
                            <dd class="goods-type">
                                <i class="iconfont ">&#xe72b;</i>
                            </dd>
                            <dd class="goods-store">@item.StoreName</dd>
                        </dl>
                    </td>
                    <td>
                        <p>@item.GoodsClass</p>
                    </td>
                    <td class="align-center">@item.GoodsPrice</td>
                    <td class="align-center">@item.Inventory</td>
                    <td class="align-center">
                        @item.GoodsStateName
                        @if (item.GoodsState == 10)
                        {
                            <p>@( "原因:" + item.GoodsStateRemark)</p>
                        }
                    </td>
                    <td class="align-center">
                        @item.GoodsVerifyName
                        @if (item.GoodsVerify == 0)
                        {
                            <p>@( "原因:" + item.GoodsVerifyRemark)</p>
                        }
                    </td>
                    <td class="align-center yes-onoff">
                        <a href="JavaScript:void(0);" class="enabled" ajax_branch='mall_goods_commend' ds_type="inline_edit" fieldname="mall_goods_commend" fieldid="@item.GoodsId" fieldvalue="1" title="可编辑"><img src="/static/admin/images/treetable/transparent.gif"></a>
                    </td>
                    <td class="align-center">
                        @if (item.GoodsVerify == 10)
                        {
                            <a href="@Url.Action("GoodsDetail" , new {gId = item.GoodsId})" class="dsui-btn-edit"><i class="iconfont"></i>@T("审核")</a>
                        }
                        else
                        {
                            <a href="@Url.Action("GoodsDetail", new { gId = item.GoodsId, isView = true })" class="dsui-btn-view"><i class="iconfont"></i>@T("查看")</a>
                        }
                        <a href="javascript:void(0);"  class="dsui-btn-del" onclick="removeGoods('@item.GoodsId')"><i class="iconfont"></i>@T("违规下架")</a>
                    </td>
                </tr>
                <tr style="display:none;">
                    <td colspan="20"><div class="dssc-goods-sku ps-container"></div></td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr class="tfoot">
                &nbsp;&nbsp;
                <td>
                    <input type="checkbox" class="checkall" id="checkallBottom" title="@T("全选")">
                </td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    <a href="JavaScript:void(0);" class="btn btn-small" onclick="batchRemoveGoods()"><span>@T("违规下架")</span></a>
                </td>
            </tr>
        </tfoot>
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>
<script type="text/javascript" src="/static/admin/js/jquery.edit.js" charset="utf-8"></script>
<script type="text/javascript" src="/static/plugins/jquery.mousewheel.js"></script>
<script type="text/javascript" src="/static/plugins/mlselection.js" charset="utf-8"></script>
<script>
$(function() {
    // 监听一级分类变化
    $('#cId1').change(function() {
        var parentId = $(this).val();
        if (!parentId) {
            $('#cId2').html('<option value="">@T("请选择")</option>');
            return;
        }
        
        // 显示加载中状态
        $('#cId2').html('<option value="">@T("加载中...")</option>');
        
        // 调用API获取子分类
                $.get('@Url.Action("GetGoodsClass")', { pId: parentId }, function(data) {
            var options = '<option value="">@T("请选择")</option>';
            $.each(data, function(index, item) {
                options += '<option value="' + item.Id + '">' + item.Name + '</option>';
            });
            $('#cId2').html(options);
        });
    });
});

function removeGoods(gIds) {
    layer.prompt({
        title: '@T("请输入违规下架原因")',
        formType: 2,
        btn: ['@T("确定")','@T("取消")']
    }, function(reason, index){
        layer.close(index);
        const loading = layer.msg('@T("处理中...")', {icon: 16, shade: 0.3, time: 0});
        
        $.post('@Url.Action("RemoveGoods", "Goods")', { 
            gIds: gIds,
            reason: reason
        })
        .done(function(res) {
            layer.close(loading);
            if(res.success) {
                layer.msg('@T("下架成功")', {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg || '@T("操作失败")', {icon: 2});
            }
        })
        .fail(function() {
            layer.close(loading);
            layer.msg('@T("请求失败，请重试")', {icon: 2});
        });
    });
}


function batchRemoveGoods() {
    // 获取选中的商品ID
    var selectedIds = [];
    $('.checkitem:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if(selectedIds.length === 0) {
        layer.msg('@T("请至少选择一件商品")', {icon: 2});
        return;
    }
    
    layer.prompt({
        title: '@T("请输入违规下架原因")',
        formType: 2,
        btn: ['@T("确定")','@T("取消")']
    }, function(reason, index){
        layer.close(index);
        const loading = layer.msg('@T("处理中...")', {icon: 16, shade: 0.3, time: 0});
        
        $.post('@Url.Action("RemoveGoods", "Goods")', { 
            gIds: selectedIds.join(','),
            reason: reason
        })
        .done(function(res) {
            layer.close(loading);
            if(res.success) {
                layer.msg('@T("批量下架成功")', {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg || '@T("操作失败")', {icon: 2});
            }
        })
        .fail(function() {
            layer.close(loading);
            layer.msg('@T("请求失败，请重试")', {icon: 2});
        });
    });
}
</script>