﻿using B2B2CShop.Areas.Admin.Controllers;
using B2B2CShop.Common.Routing;
using B2B2CShop.Dto;
using B2B2CShop.Entity;

using DH.Core.Domain.Localization;
using DH.Entity;

using Microsoft.AspNetCore.ResponseCompression;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using Pek;
using Pek.ExChangeRate;
using Pek.Helpers;
using Pek.Infrastructure;
using Pek.NCubeUI;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.MVC.Routing;
using Pek.Timing;
using Pek.VirtualFileSystem;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Common;

public partial class DHStartup : IPekStartup {
    /// <summary>
    /// 配置添加的中间件的使用
    /// </summary>
    /// <param name="application">用于配置应用程序的请求管道的生成器</param>
    public void Configure(IApplicationBuilder application)
    {
        //XTrace.WriteLine($"Configure进来了");

        var site = SiteInfo.FindDefault();
        if (site == null)
        {
            site = new SiteInfo
            {
                SiteName = "海凌科外贸平台",
                SeoTitle = "海凌科外贸平台",
                SeoKey = "海凌科外贸平台",
                SeoDescribe = "海凌科外贸平台"
            };
            site.Insert();
        }

        if (UserE.Meta.Count == 1) // 只有一个管理员用户时
        {
            using (var tran1 = UserE.Meta.CreateTrans())
            {
                var model = new UserE();
                var modelDetail = new UserDetail();

                model.Password = ManageProvider.Provider?.PasswordProvider.Hash("123456".MD5());
                model.RegisterTime = DateTime.Now;
                model.Enable = true;
                model.Logins = 1;
                model.LastLogin = DateTime.Now;
                model.LastLoginIP = Pek.Helpers.DHWeb.IP;
                model.RegisterIP = Pek.Helpers.DHWeb.IP;

                model.RoleID = Role.GetOrAdd(DHSetting.Current.DefaultRole).ID;

                model.Name = "buyer";
                model.DisplayName = "海凌科_123456";
                model.Insert();

                modelDetail.Id = model.ID;
                modelDetail.Insert();

                var modelUserEx = new UserEx
                {
                    Id = model.ID,
                };
                modelUserEx.Insert();

                tran1.Commit();
            }
            UserE.Meta.Cache.Clear("", true);
            UserEx.Meta.Cache.Clear("");
            UserDetail.Meta.Cache.Clear("");

            var modelStore = new Store
            {
                Name = "官方自营店铺",
                GradeId = 1,
                UId = 2,
                UName = "buyer",
                SellerName = "buyer",
                StoreClassId = 0,
                CountryId = Country.FindByName("中国")?.Id ?? 0,
                CountryCode = Country.FindByName("中国")?.TwoLetterIsoCode ?? "",
                RegionId = Regions.FindByName("深圳市")?.Id ?? 0,
                RegionCode = Regions.FindByName("深圳市")?.AreaCode ?? "",
                State = 1,
                Sort = 255,
                AddTime = UnixTime.ToTimestamp(DateTime.Now),
                Recommend = false,
                Theme = "default",
                Credit = 100,
                DescCredit = 5,
                ServiceCredit = 5,
                DeliveryCredit = 5,
                Collect = 0,
                Sales = 0,
                FreePrice = 0.00M,
                DecorationSwitch = false,
                DecorationOnly = false,
                DecorationImageCount = 0,
                PlatformStore = true,
                BindAllGc = true,
                BaoZh = false,
                QTian = false,
                ZhPing = false,
                ErXiaoShi = false,
                TuiHuo = false,
                ShiYong = false,
                ShiTi = false,
                XiaoXie = false,
                HuoDaoFK = false,
                Longitude = String.Empty,
                Latitude = String.Empty,
                MgDiscountState = false,
                AvaliableDeposit = 0.00M,
                FreezeDeposit = 0.00M,
                PayableDeposit = 0.00M,
                AvaliableMoney = 0.00M,
                FreezeMoney = 0.00M,
                KdnIfOpen = false,
                TrackIfOpen = false,
            };
            modelStore.Insert();

            var modelStoreJoinIn = new StoreJoinIn
            {
                Id = modelStore.Id,
                UId = 2,
                UName = "buyer",
                Type = 0,
                CountryId = Country.FindByName("中国")?.Id ?? 0,
                CountryCode = Country.FindByName("中国")?.TwoLetterIsoCode ?? "",
                ProvinceId = Regions.FindByName("广东")?.Id ?? 0,
                ProvinceCode = Regions.FindByName("广东")?.AreaCode ?? "",
                SellerName = "buyer",
                Name = "官方自营店铺",
                Longitude = String.Empty,
                Latitude = String.Empty,
                State = 40,
                Year = 1,
                StoreclassBail = 0,
                PayingAmount = 0.00M,
                PaySn = String.Empty,
                PaymentCode = String.Empty,
                TradeSn = String.Empty,
                RcbAmount = 0.00M,
                PDAmount = 0.00M,
            };
            modelStoreJoinIn.Insert();

            var modelSeller = new Seller
            {
                Name = "buyer",
                UId = 2,
                SellerGroupId = 0,
                StoreId = modelStore.Id,
                IsAdmin = true,
            };
            modelSeller.Insert();

            var modelAlbumCategory = new AlbumCategory
            {
                Name = "默认相册",
                StoreId = modelStore.Id,
                Sort = 255,
                IsDefault = true
            };
            modelAlbumCategory.Insert();

            var modelSnsAlbumClass = new SnsAlbumClass
            {
                UId = 2,
                Name = "buyer 买家秀",
                Des = "买家秀默认相册",
                Sort = 255,
                IsDefault = true,
            };
            modelSnsAlbumClass.Insert();
        }

        // 时区与上一条记录的时区不一致时，新增当前最新时区
        ZoneTime.UpdateCurrent();

        // 初始化本地化资源
        DHHelper.InitLocaleStringResource();

        // 初始化消息模板
        DHHelper.InitMsgTpl();
    }

    /// <summary>
    /// 添加并配置任何中间件
    /// </summary>
    /// <param name="services">服务描述符集合</param>
    /// <param name="configuration">应用程序的配置</param>
    /// <param name="webHostEnvironment"></param>
    public void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        services.AddScoped<SlugRouteTransformer>();  // slug路由转换
        services.AddScoped<IPekUrlHelper, PekUrlHelper>();

        //XTrace.WriteLine($"ConfigureServices进来了");
        DHSetting.Current.IsAllowUrlSuffix = true;
        DHSetting.Current.IsInstalled = true;

        DHSetting.Current.IsAlertOrCheckCode = 2;
        DHSetting.Current.AdminArea = "Biz";
        DHSetting.Current.CaptChaUrl = "/CaptCha/GetCheckCode";
        DHSetting.Current.SitemapXmlEnabled = true;
        DHSetting.Current.StartTime = DateTime.Now;
        DHSetting.Current.PaswordStrength = "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,32}$";
        DHSetting.Current.UserCenterUrl = "~/Member";

        DHSetting.Current.Save();

        ExChangeRateSettings.Current.APIKey = "************************";
        ExChangeRateSettings.Current.Save();

        LocalizationSettings.Current.AutomaticallyDetectLanguage = true;
        LocalizationSettings.Current.Save();

        // 验证码
        // 内存缓存
        services.AddCaptcha(configuration);

        //var content = "Data".AsDirectory();
        //// 下载区域数据
        //var RegionPath = "Data/Regions.db".GetFullPath();
        //if (!RegionPath.AsFile().Exists)
        //{
        //    ThreadPool.QueueUserWorkItem(async s =>
        //    {
        //        await DHWeb.DownloadLinkAndExtract("http://x.deng-hao.com/", "3-Regions-20221005.zip", content.FullName, true);
        //    });
        //}

    }

    /// <summary>
    /// 配置虚拟文件系统
    /// </summary>
    /// <param name="options">虚拟文件配置</param>
    public void ConfigureVirtualFileSystem(DHVirtualFileSystemOptions options)
    {
    }

    /// <summary>
    /// 注册路由
    /// </summary>
    /// <param name="endpoints">路由生成器</param>
    public void UseDHEndpoints(IEndpointRouteBuilder endpoints)
    {
    }

    /// <summary>
    /// 将区域路由写入数据库
    /// </summary>
    public void ConfigureArea()
    {
        AreaBase.SetRoute<HomeController>(AdminArea.AreaName);
    }

    /// <summary>
    /// 调整菜单
    /// </summary>
    public void ChangeMenu()
    {

    }

    /// <summary>
    /// 升级处理逻辑
    /// </summary>
    public void Update()
    {
        Task.Run(() =>
        {

            if (DHSetting.Current.UpdateInfo.IsNullOrWhiteSpace())
            {
                DHSetting.Current.UpdateInfo = "1.0.0_0";
                DHSetting.Current.Save();
            }
            var updateinfo = DHSetting.Current.UpdateInfo.Split("_");

            if (new Version(DHSetting.Current.CurrentVersion) == new Version("1.0.0"))
            {
                if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "0"))
                {
                    //物料入库
                    var materials = MerchantMaterial.FindAll();
                    foreach (var material in materials)
                    {

                        var entity = new MaterialIncoming()
                        {
                            WareHouseId = 1,
                            MerchantMaterialId = material.Id,
                            StoreId = material.StoreId,
                            Quantity = 10000
                        };
                        entity.Insert();
                        var model = WareHouseMaterial.Find(WareHouseMaterial._.WareHouseId == 1 & WareHouseMaterial._.MerchantMaterialId == material.Id);
                        if (model == null)
                        {
                            model = new WareHouseMaterial()
                            {
                                WareHouseId = 1,
                                MerchantMaterialId = material.Id,
                                StoreId = material.StoreId,
                                Quantity = 10000
                            };
                            model.Insert();
                        }
                        else
                        {
                            model.Quantity = 10000;
                            model.Update();
                        }
                        material.Quantity = 10000;
                        material.Update();
                    }
                    DHSetting.Current.UpdateInfo = "1.0.0_1";
                    DHSetting.Current.Save();
                }
                else if(updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "1"))
                {
                    var goodsList = Goods.FindAll();
                    foreach (var item in goodsList)
                    {
                        var skuList = GoodsSKUDetail.FindAllHaveSpecByGoodsId(item.Id);
                        string materialIds = "";
                        foreach (var sku in skuList)
                        {
                            materialIds += sku.MaterialId + ",";
                        }
                        materialIds = materialIds.SubString(0, materialIds.Length - 1);
                        item.MaterialIds = materialIds.IsNullOrEmpty() ? item.MerchantMaterial.SafeString() : materialIds;
                        item.Update();
                    }
                    DHSetting.Current.UpdateInfo = "1.0.0_2";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "2"))
                {

                    var path = Path.Combine(DHSetting.Current.WebRootPath, "public", "city.json");
                    string json = File.ReadAllText(path);
                    var list = Newtonsoft.Json.JsonConvert.DeserializeObject<List<CitiyDto>>(json);
                    if (list == null) return;
                    var listRegions = Regions.FindAll();
                    List<Regions> adds = new();

                    foreach (var item in list)
                    {
                        if(item.name.Length >= 50)
                        {
                            if (item.name == "Valle Huejúcar (Fraccionamiento Popular) [Fraccionamiento]") item.name = "Valle Huejúcar";
                            else if (item.name == "Mismaloya (Fraccionamiento Pedregal de Santa Martha)") item.name = "Mismaloya";
                            else continue;
                        }
                        var modelRegions1 = listRegions.FirstOrDefault(e => e.CId == item.country_code & e.Name == item.state_name & e.Level == 0);
                        if (modelRegions1 == null) continue;
                        var modelRegions2 = listRegions.FirstOrDefault(e => e.ParentCode == modelRegions1.AreaCode & e.Name == item.name);
                        if(modelRegions2 == null)
                        {
                            modelRegions2 = new Regions()
                            {
                                CId = modelRegions1.CId,
                                Level = 1,
                                ParentCode = modelRegions1.AreaCode,
                                AreaCode = Guid.NewGuid().ToString().MD5(),
                                Name = item.name,
                                ShortName = item.name,
                                //MergerName = modelRegions1.Name + "," + item.name,
                                PinYin = item.name,
                                Lng = item.longitude.ToDecimal(),
                                Lat = item.latitude.ToDecimal(),
                            };
                            adds.Add(modelRegions2);
                        }
                    }
                    adds.Insert();
                    DHSetting.Current.UpdateInfo = "1.0.0_3";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "3"))
                {
                    var path = Path.Combine(DHSetting.Current.WebRootPath, "public", "TelAreaCode.json");
                    string json = File.ReadAllText(path);
                    var list = Newtonsoft.Json.JsonConvert.DeserializeObject<List<TelAreaCode>>(json);
                    if (list == null) return;
                    var listAreaCode = TelAreaCode.FindAll();
                    List<TelAreaCode> telareacodes = new();
                    foreach (var item in list)
                    {
                        var modelAreaCode = listAreaCode.FirstOrDefault(e => e.CountryId == item.CountryId & e.AreaCode == item.AreaCode);
                        if (modelAreaCode != null) continue;


                        modelAreaCode = new TelAreaCode()
                        {
                            AreaCode = item.AreaCode,
                            CountryId = item.CountryId,
                            Description = item.Description
                        };
                        telareacodes.Add(modelAreaCode);
                    }
                    telareacodes.Insert();
                    DHSetting.Current.UpdateInfo = "1.0.0_4";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "4"))
                {
                    foreach (var item in Goods.FindAll())
                    {
                        item.GoodsWeight = (Randoms.RndNum(2).ToDecimal())/100; //随机重量
                        item.Update();
                    }
                    DHSetting.Current.UpdateInfo = "1.0.0_6";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "5"))
                {
                    foreach (var item in Order.FindAll())
                    {
                        if (item.RefundState==2)
                        {
                            item.OrderState = 0;
                        }
                        item.Update();
                    }
                    DHSetting.Current.UpdateInfo = "1.0.0_6";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "6"))
                {
                    #region 转移商品价格到物料价格表___弃用
                    //var goodstieredPriceList = GoodsTieredPrice.FindAll();
                    //foreach (var item in goodstieredPriceList)//转移数据到新表
                    //{
                    //    var goods = Goods.FindById(item.GoodsId);
                    //    if (!goods.IsNull())
                    //    {
                    //        var tp = MaterialTieredPrice.FindByMaterialIdAndMinQuantity(goods.MerchantMaterial, item.MinQuantity);
                    //        if (!tp.IsNull())
                    //        {
                    //            continue;
                    //        }
                    //        var materialtPrice = new MaterialTieredPrice();
                    //        materialtPrice.MaterialId = goods.MerchantMaterial;
                    //        materialtPrice.StoreId = goods.StoreId;
                    //        materialtPrice.MinQuantity = item.MinQuantity;
                    //        materialtPrice.MaxQuantity = item.MaxQuantity;
                    //        materialtPrice.OriginalPrice = item.OriginalPrice;
                    //        materialtPrice.Price = item.Price;
                    //        materialtPrice.Enabled = item.Enabled;
                    //        materialtPrice.Insert();
                    //    }
                    //}
                    //var goodsList = Goods.FindAll();
                    //foreach (var item in goodsList)//商品表中的价格
                    //{
                    //    var material = MerchantMaterial.FindById(item.MerchantMaterial);
                    //    if (material!=null)
                    //    {
                    //        material.Price = item.GoodsPrice;
                    //        material.MarketPrice = item.GoodsMarketPrice;
                    //        material.Weight = item.GoodsWeight;
                    //        material.Update();
                    //    }
                    //}
                    //var skulist = GoodsSKUDetail.FindAll();
                    //foreach (var item in skulist)//sku中的商品价格
                    //{
                    //    var material = MerchantMaterial.FindById(item.MaterialId);
                    //    var goods = Goods.FindById(item.GoodsId);
                    //    if (material != null && goods != null)
                    //    {
                    //        material.Price = item.GoodsPrice;
                    //        material.MarketPrice = item.GoodsMarketPrice;
                    //        material.Weight = goods.GoodsWeight;
                    //        material.Update();
                    //    }
                    //    foreach (var specItem in item.goodsSpecDto)
                    //    {
                    //        var spec = GoodsSpecValue.FindById(specItem.VId);
                    //        if (spec!=null)
                    //        {
                    //            spec.GoodsId = goods.Id;
                    //            spec.GoodsName = goods.Name;
                    //            spec.Update();
                    //        }
                    //    }
                        
                    //}
                    #endregion
                    
                    DHSetting.Current.UpdateInfo = "1.0.0_7";
                    DHSetting.Current.Save();
                }
                else if (updateinfo[0] != "1.0.0" || (updateinfo[0] == "1.0.0" && updateinfo[1] == "7"))
                {
                    #region 添加默认商品SKU
                    var goodsList = Goods.FindAll();
                    foreach (var item in goodsList)
                    {
                        var skuCount = GoodsSKUDetail.FindCount(GoodsSKUDetail._.GoodsId==item.Id);
                        if (skuCount == 0) //如果有SKU则不添加默认SKU
                        {
                            #region 添加默认商品SKU
                            var sku = new GoodsSKUDetail();
                            sku.SpecValue = string.Empty;
                            sku.GoodsId = item.Id;
                            sku.MaterialId = item.MerchantMaterial;
                            sku.GoodsMarketPrice = item.GoodsMarketPrice;
                            sku.GoodsPrice = item.GoodsPrice;
                            sku.Insert();
                            #endregion
                        }
                        GoodsSKUDetail.Meta.Cache.Clear("",true);
                        #region 更改商品图片添加SKUID
                        var goodsImageList = GoodsImages.FindAllByGoodsCommonId(item.Id);
                        foreach (var goodsImage in goodsImageList)
                        {
                            var sku = GoodsSKUDetail.FindByGoodsIdAndMaterialId(item.Id,item.MerchantMaterial);
                            if (goodsImage.SkuId.IsNull() || goodsImage.SkuId <= 0)
                            {
                                goodsImage.SkuId = sku?.Id ?? 0;
                                goodsImage.Update();
                            }
                        }
                        var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                        foreach (var lan in LanguageList)
                        {
                            var goodsImagelanList = GoodsImagesLan.FindAllByGoodsCommonIdAndLId(item.Id, lan.Id);
                            foreach (var goodsImage in goodsImagelanList)
                            {
                                var sku = GoodsSKUDetail.FindByGoodsIdAndMaterialId(item.Id, item.MerchantMaterial);
                                if (goodsImage.SkuId.IsNull() || goodsImage.SkuId <= 0)
                                {
                                    goodsImage.SkuId = sku?.Id ?? 0;
                                    goodsImage.Update();
                                }
                            }
                        }

                        #endregion

                        item.GoodsVerify = 1;
                        var goodsCommon = GoodsCommon.FindById(item.Id);
                        if (goodsCommon!=null)
                        {
                            goodsCommon.GoodsVerify = 1;
                            goodsCommon.Update();
                        }
                        item.Update();
                    }
                    #endregion

                    #region 批量修改物料状态
                    var materials = MerchantMaterial.FindAll();
                    foreach (var item in materials)
                    {
                        item.Status = 1;
                        item.Update();
                    }
                    #endregion

               

                    DHSetting.Current.UpdateInfo = "1.0.0_8";
                    DHSetting.Current.Save();
                }
            }
        });

     }

    /// <summary>
    /// 配置使用添加的中间件
    /// </summary>
    /// <param name="application">用于配置应用程序的请求管道的生成器</param>
    public void ConfigureMiddleware(IApplicationBuilder application)
    {
    }

    /// <summary>
    /// UseRouting前执行的数据
    /// </summary>
    /// <param name="application"></param>
    public void BeforeRouting(IApplicationBuilder application)
    {
    }

    /// <summary>
    /// UseAuthentication或者UseAuthorization后面 Endpoints前执行的数据
    /// </summary>
    /// <param name="application"></param>
    public void AfterAuth(IApplicationBuilder application)
    {
    }

    /// <summary>
    /// 处理数据
    /// </summary>
    public void ProcessData()
    {
        
    }

    /// <summary>
    /// 获取此启动配置实现的顺序
    /// </summary>
    public int StartupOrder => 999; //常见服务应在错误处理程序之后加载

    /// <summary>
    /// 获取此启动配置实现的顺序。主要针对ConfigureMiddleware、UseRouting前执行的数据、UseAuthentication或者UseAuthorization后面 Endpoints前执行的数据
    /// </summary>
    public int ConfigureOrder => 200;
}