﻿using B2B2CShop.Entity;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>商品管理</summary>
[DisplayName("商品管理")]
[Description("用于商品的管理")]
[AdminArea]
[DHMenu(80, ParentMenuName = "Products", CurrentMenuUrl = "~/{area}/Goods", CurrentMenuName = "GoodsList", CurrentIcon = "&#xe732;", LastUpdate = "20241218")]
public class GoodsController : PekCubeAdminControllerX {

    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("商品列表")]
    public IActionResult Index(string goodsName,string goodsId,long storeId, long cId1,long cId2, int goodsstate=-1,int goodsverify = -1,int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "CreateTime",
            Desc = true
        };
        viewModel.storeList = Store.FindAll();
        viewModel.goodsName = goodsName;
        viewModel.goodsId = goodsId;
        viewModel.cId1 = cId1;
        viewModel.cId2 = cId2;
        viewModel.storeId = storeId;
        viewModel.goodsstate = goodsstate;
        viewModel.goodsverify = goodsverify;
        viewModel.list = Goods.SearchManage(goodsName, goodsId, cId1,cId2, storeId, goodsstate, goodsverify, pages);
        viewModel.page = page;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {
            { "goodsName", goodsName },
            { "goodsId", goodsId },
            { "cId1", cId1.SafeString() },
            { "cId2", cId2.SafeString() },
            { "storeId", storeId.SafeString() },
            { "goodsstate", goodsstate.SafeString() },
            { "goodsverify", goodsverify.SafeString() }
        });
        return View(viewModel);
    }

    /// <summary>
    /// 商品信息页面
    /// </summary>
    /// <param name="gId">商品ID</param>
    /// <param name="isView">是否仅查看</param>
    /// <returns></returns>
    public IActionResult GoodsDetail(long gId,bool isView = false)
    {
        if (gId <= 0)
        {
            return Prompt(new PromptModel { Message = GetResource("商品ID不存在") });
        }
        var goods = Goods.FindById(gId);
        if (goods == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品记录不存在") });
        }
        ViewBag.Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
        ViewBag.IsView = isView;
        return View(goods);
    }

    public IActionResult GetGoodsClass(long pId)
    {
        var list = GoodsClass.FindAllByParentId(pId);
        return Json(list);
    }

    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("商品审核")]
    public IActionResult AuditGoods(long gId,bool ispass,string reason)
    {
        if (gId <= 0)
        {
            return Json(new DResult { success = false, msg = GetResource("商品ID为空") });
        }
        var goods = Goods.FindById(gId);
        if (goods == null)
        {
            return Json(new DResult { success = false, msg = GetResource("商品记录为空") });
        }
        var goodsCommon = GoodsCommon.FindById(gId);
        if (goodsCommon == null)
        {
            return Json(new DResult { success = false, msg = GetResource("商品记录为空") });
        }
        if (!ispass && reason.IsNullOrEmpty())
        {
            return Json(new DResult { success = false, msg = GetResource("请输入商品审核不通过的原因") });
        }
        goods.GoodsVerify = ispass ? 1 : 0;
        goodsCommon.GoodsVerify = ispass ? 1 : 0;
        goodsCommon.GoodsVerifyRemark = ispass ? "" : reason;
        goods.Update();
        goodsCommon.Update();
        return Json(new DResult { success = true, msg = GetResource("操作成功") });

    }

    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("商品违规下架")]
    public IActionResult RemoveGoods(string gIds,string reason)
    {
        if (gIds.IsNullOrEmpty())
        {
            return Json(new DResult { success = false, msg = GetResource("商品ID为空") });
        }
        var goodslist = Goods.FindAllByIds(gIds);
        if (goodslist.Count<=0)
        {
            return Json(new DResult { success = false, msg = GetResource("商品记录为空") });
        }
        if (reason.IsNullOrEmpty())
        {
            return Json(new DResult { success = false, msg = GetResource("请输入商品下架的原因") });
        }
        foreach (var goods in goodslist)
        {
            if (goods.GoodsState == 1)
            {
                goods.GoodsState = 10;
                goods.Update();
                var goodsCommon = GoodsCommon.FindById(goods.Id);
                if (goodsCommon!=null)
                {
                    goodsCommon.GoodsState = 10;
                    goodsCommon.GoodsSteteRemark = reason;
                    goodsCommon.Update();
                }
            }

        }
        return Json(new DResult { success = true, msg = GetResource("操作成功") });
    }
}
