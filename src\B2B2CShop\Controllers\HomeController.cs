﻿using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Cube.ViewModels;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.Helpers;
using Pek.Mail.Core;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.MVC.Routing;
using Pek.Seo;
using Pek.Timing;
using System.Collections.Generic;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Controllers;

/// <summary>主页面</summary>
[DHSitemap(IsUse = true)]
public class CubeHomeController : PekBaseControllerX {
    /// <summary>主页面</summary>
    /// <returns></returns>
    public IActionResult Index()
    {

        ViewBag.Message = "主页面";

        //DHSetting.Current.AllowDynamicRedirection = true;
        //DHSetting.Current.Save();

        dynamic viewModel = new ExpandoObject();

        //var _pek = Pek.Webs.HttpContext.Current.RequestServices.GetService<IPekUrlHelper>();
        //XTrace.WriteLine($"获取数据：{_pek?.PekRouteUrl(Url, "MuliChangeLanguage", WorkingLanguage.UniqueSeoCode, new { langid = 1 })}");

        int lid = WorkingLanguage.Id;

        var goodsList = GoodsSKUDetail.FindAll().Where(e=>e.Goods.GoodsState==1).ToList();
        List<GoodsSKUDetail> likeList = new();
        if(goodsList.Count > 0)
        {
            for (int i = 0; i < 12; i++)
            {
                Random random = new Random();
                int randomNumber = random.Next(0, goodsList.Count);
                var modal = goodsList[randomNumber];
                likeList.Add(modal);
            }
        }

        viewModel.LikeList = likeList.Select(e =>
        {
            var goodsImage = GoodsImages.FindDefaultBySkuId(e.Id, lid);
            if (goodsImage.IsNullOrEmpty())
            {
                goodsImage = GoodsLan.FindByGIdAndLId(e.Id, lid)?.LanGoodsImage ?? e.Goods.GoodsImage ?? "";
            }
            return new GoodsDto
            {
                Id = e.GoodsId.SafeString(),
                SkuId = e.Id.SafeString(),
                Name = (GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanName ?? e.Goods.Name) + " " + e.SpecValueDetail(lid),
                GoodsPrice = (e.GoodsPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal(),
                GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialId.SafeString()),
                GoodsImage = AlbumPic.FindByNameAndSId(goodsImage, e.Goods.StoreId)?.Cover ?? "",
                GoodsSalenum = e.GoodsSalenum,
                GoodsBuynum = e.GoodsBuynum,
                EvaluationGoodStar = e.EvaluationGoodStar,
            };
        });

        //供应商品牌
        viewModel.StoreList = Store.FindAll(Store._.State == 1, new PageParameter { PageIndex = 1, PageSize = 20 });

        //文章公告
        viewModel.Articlelist = Article.FindAll(null, new PageParameter { PageIndex = 1, PageSize = 4, Sort = "Id", Desc = true }).Select(e => new Article
        { 
            Id = e.Id,
            Name = (ArticleLan.FindByAIdAndLId(e.Id,WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : ArticleLan.FindByAIdAndLId(e.Id, WorkingLanguage.Id)?.Name
        });

        //商品分类
        viewModel.GoodsClassList = GoodsClass.FindAllByTreeLan(WorkingLanguage.Id);

        //销量排行榜
        var goods1 = GoodsSKUDetail.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 12, Sort = GoodsSKUDetail._.GoodsSalenum, Desc = true }, 1);
        viewModel.GoodsRankList1 = goods1;

        //热销排行榜
        var goods2 = GoodsSKUDetail.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 12, Sort = GoodsSKUDetail._.GoodsBuynum, Desc = true }, 1);
        viewModel.GoodsRankList2 = goods2;

        //新品推荐
        viewModel.NewGoodsList1 = GoodsSKUDetail.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 4, Sort = "Id", Desc = true }, 1);

        viewModel.NewGoodsList2 = GoodsSKUDetail.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 2, PageSize = 4, Sort = "Id", Desc = true }, 1);

        //首页轮播图
        viewModel.Advposition = AdvPosition.FindById(1);//首页轮播图位置ID
        viewModel.Slideshow = Advertising.FindAllEnabledByAPIdLan(1, WorkingLanguage.Id);//首页轮播图


        return PekView(viewModel, DHSetting.Current.AllowMobileTemp, DHSetting.Current.AllowLanguageTemp);
    }

    /// <summary>错误</summary>
    /// <returns></returns>
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        // 正式环境中，错误页避免返回500错误码
        HttpContext.Response.StatusCode = 200;

        var model = HttpContext.Items["Exception"] as ErrorModel;
        if (IsJsonRequest)
        {
            if (model?.Exception != null) return Json(500, null, model.Exception);
        }

        return View("Error", model);
    }

    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        var list = new List<DHSitemap>
        {
            new() {
                SType = SiteMap.首页,
                ActionName = "Index",
                ControllerName = "CubeHome"
            }
        };

        return list;
    }

    /// <summary>
    /// 根据父级ID查询商品分类
    /// </summary>
    /// <param name="ParentId"></param>
    /// <returns></returns>
    public IActionResult GetGoodsClassByParentId(long ParentId)
    {
        var list = GoodsClass.FindAllByParentId(ParentId);
        return Json(new { data = list });
    }

    /// <summary>
    /// 查询最新的商品
    /// </summary>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    public IActionResult GetGoodsTop(int page,int limit,string sort = "Id")
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            Sort = sort,
            Desc = true
        };
        int lid = WorkingLanguage.Id;
        var goods = GoodsSKUDetail.FindAll(GoodsSKUDetail._.GoodsState == 1, pages).Select(e =>
        {
            var goodsImage = GoodsImages.FindDefaultBySkuId(e.Id, lid);
            if (goodsImage.IsNullOrEmpty())
            {
                goodsImage = GoodsLan.FindByGIdAndLId(e.Id, lid)?.LanGoodsImage ?? e.Goods.GoodsImage ?? "";
            }
            return new
            {
                Id = e.Id.SafeString(),
                Name = (GoodsLan.FindByGIdAndLId(e.GoodsId, lid)?.LanName ?? e.GoodsName) + " " + e.SpecValueDetail(lid),
                GoodsPrice = e.GoodsPrice * WorkingCurrencies.ExchangeRate,
                GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialId.SafeString()),
                GoodsImage = AlbumPic.FindByNameAndSId(goodsImage, e.Goods.StoreId)?.Cover,
                e.GoodsSalenum,
                e.GoodsBuynum,
                e.EvaluationGoodStar,
            };
        });
        return Json(new { data = goods });
    }

    /// <summary>
    /// 根据一级商品分类ID查询二级商品分类
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    public IActionResult GetGoodsClassById(long classId)
    {
        var list = GoodsClass.FindAllByParentId(classId).OrderBy(e => e.Sort).Select(e => new
        {
            Id = e.Id.SafeString(),
            Name = (GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name,
            Sum = MerchantMaterial.GetQuantityByClassId(0, e.Id)
        });
        return Json(new { data = list });
    }

    /// <summary>
    /// 加入心愿清单
    /// </summary>
    /// <param name="goodsId"></param>
    /// <param name="sId"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddWishlist(long skuId)
    {
        var res = new DResult();
        var goodSku = GoodsSKUDetail.FindById(skuId);
        if(goodSku == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var userId = ManageProvider.User?.ID ?? 0;
        if(userId <= 0)
        {
            res.msg = GetResource("请先登录");
            return Json(res);
        }

        var modelWishlist = Wishlist.FindBySkuIdAndBuyerId(goodSku.Id,userId);

        if(modelWishlist == null)
        {
            goodSku.GoodsCollect+=1;
            goodSku.Update();

            Wishlist wish = new()
            {
                BuyerId = userId,
                StoreId = goodSku.Goods.StoreId,
                StoreName = goodSku.Goods.StoreName,
                GoodsId = goodSku.GoodsId,
                SkuId = goodSku.Id,
                GoodsPrice = goodSku.GoodsPrice,
                WishlistAddTime = UnixTime.ToTimestamp()
            };
            wish.Insert();
        }

        res.success = true;
        res.msg = GetResource("操作成功");
        return Json(res);
    }

    /// <summary>
    /// 取消心愿清单
    /// </summary>
    /// <param name="goodsId"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult DelWishlist(long skuId)
    {
        var res = new DResult();
        var userId = ManageProvider.User?.ID ?? 0;
        var goodssku = GoodsSKUDetail.FindById(skuId);
        if (goodssku == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var wish = Wishlist.FindBySkuIdAndBuyerId(goodssku.Id,userId);
        if(wish == null)
        {
            res.msg = GetResource("心愿清单不存在");
            return Json(res);
        }
        goodssku.GoodsCollect -= 1;
        goodssku.Update();
        wish.Delete();
        res.success = true;
        res.msg = GetResource("操作成功");
        return Json(res);
    }

    /// <summary>
    /// 加入购物车
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddCart(long SkuId, int GoodsNum)
    {
        var res = new DResult();  
        if (SkuId <= 0)
        {
            res.msg = GetResource("商品不能为空");
            return Json(res);
        }
        if (GoodsNum <= 0)
        {
            res.msg = GetResource("商品数量必须大于0");
            return Json(res);
        }
        var skudetail = GoodsSKUDetail.FindById(SkuId);
        if (skudetail == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var goods = skudetail.Goods;
       
        var cart = Cart.FindCartBySIdAndSkuId(SId, skudetail.Id);

        var tieredPrice = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(goods.Id, SkuId, GoodsNum);
        var werehouseMaterial = WareHouseMaterial.FindAllByMaterialIdLan(skudetail.MaterialId, WorkingLanguage.Id).OrderByDescending(o => o.Quantity).FirstOrDefault();//获取数量最多的仓库
        if (werehouseMaterial == null || werehouseMaterial.Quantity <= 0)
        {
            res.msg = GetResource("库存不足");
            return Json(res);
        }
        if (werehouseMaterial?.Quantity < (cart?.GoodsNum??0) + GoodsNum)
        {
            res.msg = GetResource("商品加购数量大于库存数量");
            return Json(res);
        }

        if (cart == null)
        {
            cart = new Cart()
            {
                BuyerId = SId,
                StoreId = goods.StoreId,
                StoreName = goods.StoreName,
                GoodsId = goods.Id,
                MaterialId = skudetail.MaterialId,
                SKUId = SkuId,
                GoodsPrice = tieredPrice?.Price?? skudetail.GoodsPrice,
                GoodsNum = GoodsNum,
            };
            cart.Insert();
        }
        else
        {
           
            cart.GoodsNum +=  GoodsNum;
            cart.Update();
        }
        res.success = true;
        res.msg = GetResource("操作成功");
        res.data = Cart.FindAllByBuyerId(SId).Count;//获取购物车数量
        return Json(res);
    }
    public IActionResult SkuSelector(long goodsId,int num)
    {
        var res = new DResult();
        if (goodsId.IsNull())
        {
            res.msg = GetResource("商品不能为空");
            return Json(res);
        }
        if (num <= 0)
        {
            res.msg = GetResource("商品数量必须大于0");
            return Json(res);
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        dynamic viewModel = new ExpandoObject();
        //需要显示的规格/值
        var skulist = GoodsSKUDetail.FindAllHaveSpecByGoodsId(goodsId);
        IDictionary<int, string> dictSpec = new Dictionary<int, string>();
        IDictionary<int, string> dictSpecValue = new Dictionary<int, string>();

        List<GoodsSpecDto> specvalues = new();
        foreach (GoodsSKUDetail item in skulist)
        {
            foreach (var specitem in item.goodsSpecDto)
            {
                if (!dictSpec.ContainsKey(specitem.Id))
                {
                    dictSpec.Add(specitem.Id,GoodsSpecValueLan.FindBySIdAndLId(specitem.Id,WorkingLanguage.Id)?.LanName??specitem.Name);
                }
                if (!dictSpecValue.ContainsKey(specitem.VId))
                {
                    dictSpecValue.Add(specitem.VId,GoodsSpecValueLan.FindBySIdAndLId(specitem.VId, WorkingLanguage.Id)?.LanName ?? specitem.Value);
                    specvalues.Add(specitem);
                }
            }
        }

        viewModel.GoodsId = goods.Id;
        viewModel.Num = num;
        viewModel.Goods = goods;
        viewModel.skuList = skulist;
        viewModel.dictSpec = dictSpec;
        viewModel.specvalues = specvalues;
        return View(viewModel);
    }


    public IActionResult ClickNumber(int Id) 
    {
        if (Id.IsNull()) return Json(new DResult() { success = false, msg = GetResource("广告记录不存在") });
        var adv = Advertising.FindById(Id);
        if (adv.IsNull()) return Json(new DResult() { success = false, msg = GetResource("广告记录不存在") });
        adv.ClickNumber += 1;
        adv.Update();
        return Json(new DResult() { success = true, msg = GetResource("操作成功") });
    }

    /// <summary>
    /// 服务和投诉
    /// </summary>
    /// <returns></returns>
    public IActionResult ServicesAndComplaints()
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.Country = Country.FindAll().Select(e => new Country
        {
            Name = CountryLan.FindByCIdAndLId(e.Id, WorkingLanguage.Id)?.Name ?? e.Name,
            TwoLetterIsoCode = e.TwoLetterIsoCode,
        });
        return View(viewModel);
    }

    /// <summary>
    /// 添加服务和投诉
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> AddFeedback(String Name,String Phone,String Mail,String CompanyName,String Theme,String Country,String Content)
    {
        DResult res = new();

        if (Name.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("姓名不能为空");
            return Json(res);
        }
        if (Phone.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("电话不能为空");
            return Json(res);
        }
        if (Mail.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("邮箱不能为空");
            return Json(res);
        }
        if (CompanyName.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("公司名称不能为空");
            return Json(res);
        }
        if (Theme.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("主题不能为空");
            return Json(res);
        }
        if (Country.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("国家不能为空");
            return Json(res);
        }
        if (Content.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("内容不能为空");
            return Json(res);
        }
        if (!ValidateHelper.IsEmail(Mail))
        {
            res.msg = GetResource("邮箱格式不正确");
            return Json(res);
        }

        FeedBack feedBack = new()
        {
            Name = Name,
            Phone = Phone,
            Mail = Mail,
            CompanyName = CompanyName,
            Theme = Theme,
            Country = Country,
            Content = Content,
            FType = 2,
            UId = ManageProvider.User?.ID ?? 0,
            UName = ManageProvider.User?.Name ?? "",
        };

        feedBack.Insert();

        var modelMsgTpl = OtherMsgTpl.FindByMCode("FeedBack"); //获取注册消息模板
        if (modelMsgTpl != null)
        {
            var (fs, fs1) = MailCommonHelp.GetFeedBack(modelMsgTpl, Name, Mail, Content);
            var box = new EmailBox
            {
                Subject = fs1.ToString(),
                To = ["<EMAIL>"],
                //To = ["<EMAIL>"],
                Body = fs.ToString(),
                IsBodyHtml = false
            };
            await MailCommonHelp.SendMail(box).ConfigureAwait(false);  //邮件发送
            var model = new SendLog
            {
                SType = 1,
                Account = Mail,
                Msg = fs.ToString(),
                MType = 1
            };
            model.SaveAsync();
        }

        res.success = true;
        res.msg = GetResource("操作成功");
        return Json(res);
    }

    /// <summary>
    /// 商家入驻
    /// </summary>
    /// <returns></returns>
    public IActionResult StoreJoin()
    {
        return View();
    }

    /// <summary>
    /// 入驻协议
    /// </summary>
    /// <returns></returns>
    public IActionResult ApplicationEntry()
    {
        return View();
    }
}