﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using NewLife;
using NewLife.Data;
using Pek;
using Pek.DsMallUI;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCubeUI.Areas.Admin;
using Pek.NCubeUI.Common;
using Pek.Timing;
using System.ComponentModel;
using System.Dynamic;
using XCode.Membership;

namespace B2B2CShop.Areas.Admin.Controllers;

/// <summary>物料管理</summary>
[DisplayName("物料管理")]
[Description("用于商品物料的管理")]
[AdminArea]
[DHMenu(99, ParentMenuName = "WareHouse", ParentMenuOrder = 56, CurrentMenuUrl = "~/{area}/Materials", CurrentMenuName = "MaterialsList", CurrentIcon = "&#xe71f;", LastUpdate = "20250108")]
public class MaterialsController : PekCubeAdminControllerX {
    /// <summary>
    /// 物料列表
    /// </summary>
    /// <param name="storeId"></param>
    /// <param name="materialName"></param>
    /// <param name="remark"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("物料列表")]
    public IActionResult Index(string storeId, string materialName, int status, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "CreateTime",
            Desc = true
        };

        materialName = materialName.SafeString().Trim();

        long storeIdLong = 0;
        if (!storeId.IsNullOrWhiteSpace())
        {
            storeIdLong = storeId.ToDGLong();
        }

        viewModel.list = MerchantMaterial.FindByStoreIdAndMaterialNameAndRemark(storeIdLong, materialName, status, pages);
        viewModel.page = page;
        viewModel.materialName = materialName;
        viewModel.status = status;
        viewModel.storeList = Store.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.Id.ToString(), Text = e.Name, Selected = e.Id == storeIdLong });

        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "storeId", storeId }, { "materialName", materialName }, { "status", status.SafeString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 查看商家物料
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("查看商家物料")]
    [EntityAuthorize(PermissionFlags.Detail)]
    public IActionResult ViewMaterial(long id)
    {
        var merchantMaterial = MerchantMaterial.FindById(id);
        return View(merchantMaterial);
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("是否启用")]
    [HttpPost]
    public IActionResult Enabled(long id)
    {
        var material = MerchantMaterial.FindById(id);
        if (material == null)
        {
            return Json(new DResult { success = false, msg = GetResource("数据不存在在或已被删除") });
        }
        material.Enabled = !material.Enabled;
        material.Update();
        return Json(new DResult { success = true, msg = GetResource("操作成功")});
    }

    /// <summary>
    /// 新增物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增物料")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add()
    {
        dynamic viewModel = new ExpandoObject();
        viewModel.storeList = Store.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.Id.ToString(), Text = e.Name });
        return View(viewModel);
    }

    /// <summary>
    /// 新增物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增物料")]
    [EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    public IActionResult Add(string name, long storeId, string remark)
    {
        var merchantMaterial = new MerchantMaterial
        {
            Name = name,
            StoreId = storeId,
            Enabled = true,
            Remark = remark,
        };
        merchantMaterial.Insert();
        return MessageTip(GetResource("创建成功"));
    }
    /// <summary>
    /// 删除物料
    /// </summary>
    /// <param name="Ids"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除物料")]
    public IActionResult Delete(String Ids)
    {
        var res = new DResult();

        MerchantMaterial.Delete(MerchantMaterial._.Id.In(Ids.Trim(',')));
        MerchantMaterial.Meta.Cache.Clear("");

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑物料
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑物料")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int64 Id)
    {
        var model = MerchantMaterial.FindById(Id);

        if (model == null)
        {
            return Content(GetResource("物料不存在"));
        }

        dynamic viewModel = new ExpandoObject();
        viewModel.merchantMaterial = model;
        viewModel.storeList = Store.FindAllWithCache().OrderBy(e => e.Id).Select(e => new SelectListItem { Value = e.Id.ToString(), Text = e.Name });
        return View(viewModel);
    }

    /// <summary>
    /// 编辑物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑物料")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult Edit(Int64 Id, String Name, Int64 StoreId, String Remark)
    {
        var model = MerchantMaterial.FindById(Id);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("物料不存在") });
        }

        model.Name = Name;
        model.StoreId = StoreId;
        model.Remark = Remark;

        model.Update();

        return Prompt(new PromptModel { Message = GetResource("编辑成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 审核物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("审核")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult AuditMaterial(Int64 Id)
    {
        if (Id <= 0)
        {
            return Content(GetResource("物料不存在"));
        }
        var merchantMaterial = MerchantMaterial.FindById(Id);
        return View(merchantMaterial);
    }

    /// <summary>
    /// 审核物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("审核")]
    [EntityAuthorize(PermissionFlags.Update)]
    [HttpPost]
    public IActionResult AuditMaterial(Int64 Id,int status,string cause)
    {
        if (Id <= 0)
        {
            return Json( new DResult() { success = false, msg = GetResource("物料不存在") });
        }
        var merchantMaterial = MerchantMaterial.FindById(Id);
        if (merchantMaterial == null)
        {
            return Json(new DResult() { success = false, msg = GetResource("物料不存在") });
        }
        if (status != 1 && status != 2)
        {
            return Json( new DResult() { success = false, msg = GetResource("请选择审核结果") });
        }
        if (status == 2 && cause.IsNullOrWhiteSpace())
        {
            return Json( new DResult() { success = false, msg = GetResource("请填写审核不通过的原因") });
        }
        merchantMaterial.Status = status;
        merchantMaterial.Cause = cause;
        merchantMaterial.Auditor = ManageProvider.User?.ID ?? 0;
        merchantMaterial.AuditTime = UnixTime.ToTimestamp(DateTime.Now);
        merchantMaterial.Update();

        return Json( new DResult() { success = true, msg = GetResource("操作成功") });
    }


}
