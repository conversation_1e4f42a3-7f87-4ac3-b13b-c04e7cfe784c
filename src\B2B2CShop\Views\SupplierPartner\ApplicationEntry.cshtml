﻿@{
  PekHtml.AppendCssFileParts("~/public/css/pageCss/applicationEntry.css");
}
@* 入驻申请 *@
<div class="main">
  <!-- 面包屑 -->
  <div class="breadBox">
    <a href="/">@T("首页")</a>
    <div>></div>
    <a href="@Url.Action("Index")">
      @T("供应商合作")
    </a>
    <div>></div>
    <a class="textSelect" href="#">
      @T("商家入驻申请")
    </a>
  </div>
  <div class="layui-container">
    <!-- 步骤条 -->
    <div class="steps-bar">
      <div class="step-item active">
        <div class="step-circle">
          <i class="layui-icon layui-icon-ok"></i>
        </div>
        <p>@T("签订入驻协议")</p>
      </div>
      <div class="step-item">
        <div class="step-circle">2</div>
        <p>@T("公司资质信息")</p>
      </div>
      <div class="step-item">
        <div class="step-circle">3</div>
        <p>@T("财务资质信息")</p>
      </div>
      <div class="step-item">
        <div class="step-circle">4</div>
        <p>@T("店铺经营信息")</p>
      </div>
      <div class="step-item">
        <div class="step-circle">5</div>
        <p>@T("合同签订")</p>
      </div>
      <div class="step-item">
        <div class="step-circle">6</div>
        <p>@T("店铺开通")</p>
      </div>
    </div>

    <!-- 协议内容卡片 -->
    <div class="center-card layui-card">
      <div class="layui-card-header">@T("入驻协议")</div>
      <div class="layui-card-body">
        <div class="agreement-content">
          <span>@T("1.本协议的订立")</span>
          <p>
            @T("在本网站(<a class='www' href='http://www.engrworld.com' target='_blank'>www.engrworld.com</a>)依据《海凌科城网站用户注册协议》登记注册，且符合本网站商家入驻标准的用户(以下简称'商 家')，在同意本协议全部条款后方有资格使用'海凌科商城商家在线入驻系统'(以下简称'入驻系统')申请入驻。一经商家点击'同意以上协议，下一步'按键，即意味著商家同意与本网站签订本协议并同意受本协议约束。")
          </p>

          <span>@T("2.入驻系统使用说明")</span>
          <p>
            @T("2.1商家通过入驻系统提出入驻申请，并按照要求填写商家信息、提供商家资质资料后，由本网站审核并与有合作意向的商家联系协商合作相关事宜，经双方协商一致线下签订书面《开放平台供应商合作运营协议》(以下简称'运营协议')，且商家按照'运营协议'约定入驻本网站。本网站将为入驻商家开通商家后台系统，商家可通过商家后台系统在本网站运营自己的入驻店铺。")
          </p>
          <p>
            @T("2.2商家以及本网站通过入驻系统做出的申请、资料提交及确认等各类沟通，仅为双方合作的意向以及本网站对商家资格审核的必备程序，除遵守本协议备项约定外，对双方不产生法律约束力。双方间最终合作事宜及运营规则均以'运营协议'的约定及商家后台系统公示的各项规则为准。")
          </p>

          <span>@T("3.商家权利义务")</span>
          <p>
            @T("用户使用'海凌科商城商家在线入驻系统'前请认真阅读并理解本协议内容，本协议内容中以加粗方式显著标识的条款，请用户着重阅读、慎重考虑。")
          </p>
        </div>
        <div class="agreement-check">
          <input type="checkbox" id="readCheck" name="read" title="@T("我已阅读并同意以上协议")" lay-skin="primary">
          <label for="readCheck" style="color:#888;">@T("我已阅读并同意以上协议")</label>
        </div>
        <div class="agreement-btns">
          <button class="layui-btn layui-btn-primary" id="btnPersonal" >@T("个人入驻")</button>
          <button class="layui-btn layui-btn-primary" id="btnCompany">@T("企业入驻")</button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="bug"></div>
<script>
  layui.use(['form', 'layer'], function () {
    var $ = layui.$;
    var layer = layui.layer;

      // 检查是否阅读并同意协议
    function checkReadAndSubmit(type) {
      let flag = $('#readCheck').prop('checked')
      console.log(flag);
      if (!flag) {
        layer.msg('请先阅读并同意以上协议');
        return false;
      }
      // 这里写你真正的跳转或提交逻辑
      if (type === 'personal') {
        // 个人入驻逻辑
        window.location.href = "@Url.Action("CompanyQualification",new {type = 1})";
      } else {
        // 企业入驻逻辑
          window.location.href = "@Url.Action("CompanyQualification",new {type = 0})";
      }
    }

    $('#btnPersonal').on('click', function (e) {
      e.preventDefault();
      checkReadAndSubmit('personal');
    });
    $('#btnCompany').on('click', function (e) {
      e.preventDefault();
      checkReadAndSubmit('company');
    });
  });
</script>