﻿﻿@* @T("商家入驻") *@
@inject IManageProvider _provider
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/recruitment.css");
    var user = _provider.TryLogin(Context);

}
<div class="recruitment">
    <img class="enter" src="~/public/images/icons/shangjiaruzhu.png" alt="">
    <div class="layui-row recruitment-apply-row">

        @* 中间部分 *@
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header textOver">@T("入驻申请")</div>
                <div class="layui-card-body">
                    <span class="textOver title">@T("未填写入驻申请资料的请按指引填写")</span>
                    <br><br>
                    <a class="layui-btn layui-btn-normal textOver" style="color:white !important;" href="javascript:void(0);" onclick="checkLoginAndRedirect()">@T("我要入驻")</a>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header textOver">@T("入驻进度")</div>
                <div class="layui-card-body">
                    <span class="textOver title">@T("店铺未开通时可了解最新审核进度")</span>
                    <br><br>
                    <a class="layui-btn layui-btn-normal textOver" href="@Url.Action("CheckProgress")" style="color:white !important;">@T("查看入驻进度")</a>
                </div>
            </div>
        </div>
    </div>
    @* 中间部分 *@

    <div class="layui-row recruitment-step-row">
        <div class="layui-col-md3">
            <div class="recruitment-step-item">
                <img src="~/public/images/icons/tijiaoziliao.png" alt="">
                <div class="recruitment-step-content">
                    <span class="recruitment-step-title">@T("提交入驻资料")</span>
                    <button class="layui-btn layui-btn-primary layui-border-blue textOver">@T("约2小时")</button>
                    <ul class="recruitment-step-list">
                        <li class="textOver">@T("填写企业信息")</li>
                        <li class="textOver">@T("选择店铺类型/类目")</li>
                        <li class="textOver">@T("填写品牌信息/资质信息")</li>
                        <li class="textOver">@T("店铺名称")</li>
                        <li class="textOver">@T("确认合同/提交资料")</li>
                    </ul>
                </div>
                <span class="recruitment-step-arrow layui-icon layui-icon-right"></span>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="recruitment-step-item">
                <img src="~/public/images/icons/dengdaishenhe.png" alt="">
                <div class="recruitment-step-content">
                    <span class="recruitment-step-title">@T("商家等待审核")</span>
                    <button class="layui-btn layui-btn-primary layui-border-blue textOver">@T("3-6个工作日")</button>
                    <ul class="recruitment-step-list">
                        <li class="textOver">@T("资质审核")</li>
                        <li class="textOver">@T("品牌审核")</li>
                    </ul>
                </div>
                <span class="recruitment-step-arrow layui-icon layui-icon-right"></span>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="recruitment-step-item">
                <img src="~/public/images/icons/hetongqianding.png" alt="">
                <div class="recruitment-step-content">
                    <span class="recruitment-step-title">@T("合同签订")</span>
                    <button class="layui-btn layui-btn-primary layui-border-blue textOver">@T("约10分钟")</button>
                    <ul class="recruitment-step-list">
                        <li class="textOver">@T("实名认证/一致性校验")</li>
                        <li class="textOver">@T("签署协议/协议回收")</li>
                    </ul>
                </div>
                <span class="recruitment-step-arrow layui-icon layui-icon-right"></span>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="recruitment-step-item">
                <img src="~/public/images/icons/dianpushangxian.png" alt="">
                <div class="recruitment-step-content">
                    <span class="recruitment-step-title">@T("店铺上限")</span>
                    <button class="layui-btn layui-btn-primary layui-border-blue textOver">@T("约20分钟")</button>
                    <ul class="recruitment-step-list">
                        <li class="textOver">@T("发布商品")</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function checkLoginAndRedirect() {
        // 检查用户登录状态
        var u = "@user";
            if(u == null || u == '')
            {
                window.location.href = '/Login';
                return;
            }
            window.location.href = '@Url.Action("ApplicationEntry", "SupplierPartner")';


    }
</script>