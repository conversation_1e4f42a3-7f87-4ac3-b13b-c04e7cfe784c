﻿using B2B2CShop.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek;
using Pek.DsMallUI.Common;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.Common;
using System.ComponentModel;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Sellers.Controllers;

/// <summary>
/// 商家中心商品物料控制器
/// </summary>
[SellersArea]
[SellersAuthorize]
public class SellerGoodsMaterialsController : PekBaseControllerX {

    /// <summary>
    /// 商品物料首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index(string materialName, int status = 1, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "CreateTime",
            Desc = true
        };
        var provider = ManageProvider.Provider;
        var userId = provider?.Current.ID??0;
        var store = Store.FindByUId(userId);
        var materials = MerchantMaterial.FindByStoreIdAndMaterialNameAndRemark(store?.Id??0, materialName, status, pages);
        viewModel.MaterialName = materialName;
        viewModel.MaterialsList = materials;
        viewModel.status = status;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> {  { "materialName", materialName }, { "status", status.SafeString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 商品物料列表
    /// </summary>
    /// <param name="materialName"></param>
    /// <param name="remark"></param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [DisplayName("商品物料列表")]
    public IActionResult List(string materialName, string remark, int page = 1, int limit = 10)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        var provider = ManageProvider.Provider;

        var userId = provider?.Current.ID??0;

        var store = Store.FindByUId(userId);

        var materials = MerchantMaterial.FindByStoreIdAndMaterialNameAndRemark(store?.Id??0, materialName, -1, pages);

        return Json(materials);
    }

    /// <summary>
    /// 新增商品物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增商品物料")]
    public IActionResult AddMaterial()
    {
        return View();
    }

    /// <summary>
    /// 新增商品物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增商品物料")]
    [HttpPost]
    public IActionResult AddMaterial(string name,decimal weight,decimal volume, List<TieredPriceInput> tieredPrice, bool enabled = true)
    {
        if (name.IsNullOrWhiteSpace())
        {
            return Prompt(new PromptModel { Message = GetResource("物料名称不能为空") });
        }
        var exit = MerchantMaterial.Find(MerchantMaterial._.Name, name);
        if (exit != null)
        {
            return Prompt(new PromptModel { Message = GetResource("物料名称已经存在") });
        }
        if (weight.IsNull())
        {
            return Prompt(new PromptModel { Message = GetResource("请填写重量") });
        }
        var provider = ManageProvider.Provider;
        var userId = provider?.Current.ID??0;
        var store = Store.FindByUId(userId);
        using (var tran = MerchantMaterial.Meta.CreateTrans())
        {

            var merchantMaterial = new MerchantMaterial
            {
                Name = name,
                Weight = weight,
                Volume = volume,
                StoreId = store?.Id ?? 0,
                Enabled = enabled,
                Status = 0,
                Quantity = 0, // Hardcoded to 0
            };
            #region 物料图片
            string images = "";
            for (int i = 0; i < 5; i++)
            {
                var image = (GetRequest($"img[0][{i}][name]")).SafeString().Trim();
                if (!image.IsNullOrEmpty())
                {
                    images += image + ",";
                }
            }
            if (!images.IsNullOrEmpty())
            {
                images = images.Substring(0, images.Length);
            }else
            {
                return Prompt(new PromptModel { Message = GetResource("请上传物料图片") });
            }
            merchantMaterial.Images = images;
            #endregion
            merchantMaterial.Insert();

            tran.Commit();  
        }
        return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index",new {status=0}) });
    }

    /// <summary>
    /// 编辑商品物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑商品物料")]
    public IActionResult EditMaterial(long id)
    {
        var model = MerchantMaterial.FindById(id);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("物料不存在") });
        }      
        return View(model);
    }

    /// <summary>
    /// 编辑商品物料
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑商品物料")]
    [HttpPost]
    public IActionResult EditMaterial(long id, string name, decimal weight, decimal volume, List<TieredPriceInput> tieredPrice, bool enabled)
    {
        var model = MerchantMaterial.FindById(id) ?? new MerchantMaterial();
        model.Name = name;
        model.Weight = weight;
        model.Volume = volume;
        model.Enabled = enabled;
        if (model.Status==2)
        {
            model.Status = 0;
        }
        #region 物料图片
        if (model.Status != 1)//审核过无需重新上传图片
        {
            string images = "";
            for (int i = 0; i < 5; i++)
            {
                var image = (GetRequest($"img[0][{i}][name]")).SafeString().Trim();
                if (!image.IsNullOrEmpty())
                {
                    images += image + ",";
                }
            }
            if (!images.IsNullOrEmpty())
            {
                images = images.Substring(0, images.Length);
            }
            else
            {
                return Prompt(new PromptModel { Message = GetResource("请上传物料图片") });
            }
            model.Images = images;
        }

        #endregion

        model.Update();
     
        return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
    }

    /// <summary>
    /// 新增商品物料
    /// </summary>
    /// <param name="classificationId"></param>
    /// <param name="subClassificationId"></param>
    /// <param name="specificationName"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [DisplayName("新增商品物料")]
    [HttpPost]
    public IActionResult AddSpec(string subClassificationId, string specificationName, int sort)
    {
        var merchantMaterial = new MerchantMaterial();
        var currentStoreId = Store.FindByUId(ManageProvider.Provider?.Current.ID ?? 0)?.Id;

        return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true });
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("是否启用")]
    [HttpPost]
    public IActionResult Enabled(int id)
    {
        var material = MerchantMaterial.FindById(id);
        if (material == null)
        {
            return Prompt(new PromptModel { Message = GetResource("数据不存在在或已被删除") });
        }
        if (material.StoreId != Store.FindByUId(ManageProvider.Provider?.Current.ID??0)?.Id)
        {
            return Prompt(new PromptModel { Message = GetResource("无权操作") });
        }
        material.Enabled = !material.Enabled;
        material.Update();
        return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true });
    }

    /// <summary>
    /// 删除商品物料
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("删除商品物料")]
    public IActionResult Delete(string ids)
    {
        string[] idArr = ids.Split(",");
        var currentStoreId = Store.FindByUId(ManageProvider.Provider?.Current.ID??0)?.Id;

        foreach (var id in idArr)
        {
            var material = MerchantMaterial.FindById(Convert.ToInt64(id));
            if (material == null || material.StoreId != currentStoreId)
            {
                return Json(new DResult { success = false, code = 10001, msg = GetResource("记录不存在") });
            }
            if (material.Quantity > 0)
            {
                return Json(new DResult { success = false, code = 10001, msg = GetResource("库存数量大于0，不能删除") });
            }
            // TODO: 删除时须确保没有关联的商品SKU，且库存数量为0
            var goodses = Goods.FindAll(Goods._.MerchantMaterial == material.Id);
            if (goodses.Count > 0)
            {
                return Json(new DResult { success = false, code = 10001, msg = GetResource("物料编号已绑定商品，不能删除") });
            }
        }

        var res = new DResult();
        MerchantMaterial.DelByIds(ids);
        MerchantMaterial.Meta.Cache.Clear("", true);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }
}
