﻿@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsMaterials";

    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
}
@await Html.PartialAsync("_Left")
<script src="~/static/admin/js/admin.js"></script>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>


<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li class="current"><a href="@Url.Action("Index")">物料管理</a></li>
        </ul>
        <a href="@Url.Action("AddMaterial")" class="dssc-btn dssc-btn-green">添加物料</a>
    </div>
    <div class="p20">
        <table class="search-form">
            <form method="get">
                <tr>
                    <td>&nbsp;</td>
                    <th>状态</th>
                <td class="w160">
                    <select name="status">
                        <!option value="-1">@T("全部")</!option>
                        <!option value="0" @(Model.status == 0?"selected":"")>@T("待审核")</!option>
                        <!option value="1" @(Model.status == 1 ? "selected" : "")>@T("已审核")</!option>
                        <!option value="2" @(Model.status == 2 ? "selected" : "")>@T("审核未通过")</!option>
                    </select>
                </td>
                    <th>物料名称</th>
                <td class="w160"><input type="text" class="text" name="materialName" value="@Model.MaterialName" /></td>
                    <td class="w70 tc">
                        <input type="submit" class="submit" value="搜索" />
                    </td>
                </tr>
            </form>
        </table>
        <table class="dssc-default-table">
            <thead>
                <tr>
                    <th>@T("物料ID")</th>
                    <th class="align-center">@T("物料名称")</th>
                    <th class="align-center">@T("重量")</th>
                    <th class="align-center">@T("体积")</th>
                    <th class="align-center">@T("库存")</th>
                    <th class="align-center">@T("是否启用")</th>
                    <th class="align-center">@T("状态")</th>
                    <th class="align-center">@T("操作")</th>
                </tr>
            </thead>
            <tbody>
                @foreach (MerchantMaterial item in Model.MaterialsList)
                {
                    <tr class="bd-line">
                        <td><p class="name"><strong>@item.Id</strong></p></td>
                        <td class="align-center">@item.Name</td>
                        <td class="align-center">@item.Weight</td>
                        <td class="align-center">@item.Volume</td>
                        <td class="align-center">@item.Quantity</td>
                        <td class="align-center">
                            @if (item.Enabled)
                            {
                                @T("启用")
                            }
                            else
                            {
                                @T("禁用")
                            }
                        </td>
                        <td class="align-center">
                            @(
                            item.Status switch
                            {
                                1=> T("已审核"),
                                2=> T("审核未通过")+"："+item.Cause,
                                _ => T("待审核")
                            }
                            )
                        </td>
                        <td class="nscs-table-handle">
                            <span><a href="@Url.Action("EditMaterial",new{id = item.Id})" class="btn-blue">@* <i class="iconfont">&#xe731;</i> *@<p>编辑</p></a></span>
                            <span>
                                <a href="javascript:dsLayerConfirm('@Url.Action("Delete")?ids=' + '@item.Id' + '','@T("您确定要删除吗?")')" class="btn-red">
                                    @* <i class="iconfont">&#xe725;</i> *@
                                    <p>删除</p>
                                </a>
                            </span>
                        </td>
                    </tr>
                }
                <tr>
                    <td colspan="12"><div class="pagination"> </div></td>
                </tr>
            </tbody>
        </table>
        <ul class="pagination">
            @Html.Raw(Model.PageHtml)
        </ul>
    </div>
</div>
