﻿@{
    var storeList = Model.storeList as IEnumerable<SelectListItem>;
}
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<script src="~/lib/select2/js/select2.min.js"></script>

<div class="page" id="material-list">
    
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>@T("物料管理")</h3>
                <h5></h5>
            </div>
            <ul class="tab-base ds-row">
                <li><a href="/Admin/Materials" class="current"><span>@T("管理")</span></a></li>
                @* <li><a href="/biz/Materials/Add"><span>@T("添加")</span></a></li> *@
            </ul>
        </div>
    </div>
    <form id="formSearch" method="get" name="StoreId">
        <div class="ds-search-form">
            <dl>
                <dt>@T("所属店铺")</dt>
                <dd>
                    <span class="w400">
                        <select id="Store" name="StoreId">
                            <!option value="0">
                            @T("全部")
                            </!option>
                            @foreach (SelectListItem item in storeList ?? [])
                            {
                                <!option value="@item.Value" @(item.Selected ? "selected" : "")>
                                @item.Text
                                </!option>
                            }
                        </select>
                    </span>
                </dd>
                <dt>@T("状态")</dt>
                <dd>
                    <span class="w400">
                        <select id="status" name="status">
                            <!option value="-1">
                            @T("全部")
                            </!option>
                            <!option value="0" @(Model.status == 0 ? "selected" : "")>
                            @T("待审核")
                            </!option>
                            <!option value="1" @(Model.status == 1 ? "selected" : "")>
                            @T("已审核")
                            </!option>
                            <!option value="2" @(Model.status == 2 ? "selected" : "")>
                            @T("审核未通过")
                            </!option>
                        </select>
                    </span>
                </dd>
            </dl>
        </div>
    </form>
    <table class="ds-default-table">
        <thead>
            <tr>
                <th></th>
                <th>@T("物料ID")</th>
                <th>@T("物料名称")</th>
                <th>@T("是否启用")</th>
                <th>@T("状态")</th>
                <th>@T("库存")</th>
                <th>@T("操作")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (MerchantMaterial item in Model.list)
            {
                <tr>
                    <th>@* <input type="checkbox" class="checkitem" value="@item.Id"> *@</th>
                    <td>@item.Id</td>
                    <td>@item.Name</td>
                    <td>
                        <div class="onoff" data-id="@item.Id">
                            <label for="State1" onclick="excute('@item.Id')"  class="cb-enable @(item.Enabled?"selected":"")">@T("是")</label>
                            <label for="State0" onclick="excute('@item.Id')"  class="cb-disable @(!item.Enabled?"selected":"")">@T("否")</label>
                            <input id="State1" name="Enabled" value="true" data-id="@item.Id"  type="radio" @(item.Enabled ? "checked" : "")>
                            <input id="State0" name="Enabled" value="false" data-id="@item.Id" type="radio" @(!item.Enabled ? "checked" : "")>
                        </div>
                    </td>
                    <td>
                        @(
                            item.Status switch
                            {
                                1=> T("已审核"),
                                2=> T("审核未通过")+"："+item.Cause,
                                _ => T("待审核")
                            }
                         )
                    </td>
                    <td>@item.Quantity</td>
                    <td>
                        <a href="javascript:dsLayerOpen('@Url.Action("ViewMaterial",new  { Id = item.Id })','@T("查看")')"
                            class="dsui-btn-view">
                            <i class="iconfont"></i>@T("查看")
                        </a>
                        @if (item.Status==0)
                        {
                            <a href="@Url.Action("AuditMaterial", new { Id = item.Id })"
                               class="dsui-btn-edit">
                                <i class="iconfont"></i>@T("审核")
                            </a>
                        }
                    </td>
                </tr>
            }
        </tbody>
       @*  <tfoot>
            <tr class="tfoot">
                <td><input type="checkbox" class="checkall" id="checkallBottom"></td>
                <td colspan="16">
                    <label for="checkallBottom">@T("全选")</label>
                    &nbsp;&nbsp;<a href="JavaScript:void(0);" class="btn btn-small"
                                   onclick="submit_delete_batch()"><span>@T("删除")</span></a>
                </td>
            </tr>
        </tfoot> *@
    </table>
    <ul class="pagination">
        @Html.Raw(Model.PageHtml)
    </ul>
</div>

<script asp-location="Footer">
    function submit_delete(items) {
        _uri = "@Url.Action("Delete")?ids=" + items;
        dsLayerConfirm(_uri, '@T("您确定要删除吗?")');
    }
</script>

<script>
   
    function excute(id) {
         $.post("@Url.Action("Enabled")",{id},function (res) {
              if(res.success) {
                   layui.layer.msg(res.msg, {icon:1, time: 1000 }, function() {
                        location.reload();
                    });
              } else {
                  layui.layer.msg('@T("操作失败")',{icon:2 });
              }
            })
            .fail(function(err) {
                console.log('选择失败！',err);
                // 在这里处理错误请求或者代码报错
               layui.layer.msg('@T("启用失败")')
            });
    }
    $(document).ready(function () {
        $('#Store').select2({
            placeholder: "@T("请选择店铺")",
            allowClear: true
        })

        $('select').on("change", function (e) {

            var status = $("#status").val();
            var storeId = $("#Store").val();
            if (!storeId) $("#Store").val(-1);
            $('#formSearch').submit();
        });
    });
</script>